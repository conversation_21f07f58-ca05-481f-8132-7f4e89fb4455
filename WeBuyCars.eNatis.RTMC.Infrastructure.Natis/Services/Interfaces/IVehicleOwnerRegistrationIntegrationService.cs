using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IVehicleOwnerRegistrationIntegrationService
    {

        Task<VehicleOwnerRegistrationInformation> RegisterVehicleOwnerQuery(Guid auditLogId, NatisVehicleOwnerRegistrationRequest natisVehicleOwnerRegistrationRequest, string username, string password, string environmentName);
        Task<VehicleOwnerRegistrationInformation> ConvertNatisVehicleOwnerRegistrationResponseXML(string natisVehicleOwnerRegistrationResponse);
        string ConvertNatisRegisterVehicleOwnerRequestXML(NatisVehicleOwnerRegistrationRequest registerVehicleOwnerRequest, string username, string password);
    }
}