using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IDriverIntegrationService
    {
        Task<DriverInformation> GetDriverQuery(Guid auditLogId,NatisGetDriverInformationRequest driverInformationRequest, string environmentName);
        Task<DriverInformation> ConvertNatisDriverInformationResponseXML(string natisDriverInformationResponse);
        string ConvertNatisDriverInformationRequestXML(NatisGetDriverInformationRequest driverInformationRequest);
    }
}