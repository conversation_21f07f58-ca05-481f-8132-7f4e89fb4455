using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IVehicleDetailIntegrationService
    {
        Task<VehicleDetailInformation> GetVehicleDetailedQuery(Guid auditLogId, NatisGetVehicleDetailedRequest vehicleDetailedRequest, string environmentName);
        Task<VehicleDetailInformation> ConvertNatisVehicleDetailedResponseXML(string natisVehicleResponse);
        string ConvertNatisVehicleDetailedRequestXML(NatisGetVehicleDetailedRequest vehicleRequest);
    }
}