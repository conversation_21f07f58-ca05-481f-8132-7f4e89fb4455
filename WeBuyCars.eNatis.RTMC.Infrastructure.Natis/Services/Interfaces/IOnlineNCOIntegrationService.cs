using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IOnlineNCOIntegrationService
    {
        
        Task<OnlineNCOInformation> NominateOwnerChangeQuery(Guid auditLogId, NatisOnlineNCORequest natisOnlineNCOInformationRequest, string username, string password, string environmentName);
        Task<OnlineNCOInformation> ConvertNatisOnlineNCOInformationResponseXML(string natisOnlineNCOInformationResponse);
        string ConvertNatisOnlineNCORequestXML(NatisOnlineNCORequest onlineNCORequest, string username, string password);
    }
}