using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.ControlNumber;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisCalculateVehicleLicenceFeeRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.CompleteRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesDueForRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesQuotation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.InitiateRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface INatisIntegrationService
    {

        #region Health Check
        Task<bool> NatisSystemAvailability();
        Task<bool> CheckRTMCVehicleEndPointAsync();
        Task<bool> CheckRTMCVehicleDetailEndPointAsync();
        Task<bool> CheckRTMCOwnerTitleHolderConfirmationEndPointAsync();
        Task<bool> CheckRTMCDriverEndPointAsync();
        Task<bool> CheckRTMCVehicleOwnerRegistrationEndPointAsync();
        Task<bool> CheckRTMCTitleHolderTransferEndPointAsync();
        #endregion

        Task<VehicleInformation> GetVehicleQuery(NatisGetVehicleRequest vehicleRequest);
        Task<VehicleDetailInformation> GetVehicleDetailedQuery(NatisGetVehicleDetailedRequest vehicleRequest, Guid auditLogId);
        Task<VehicleOwnerVerification> GetOwnerTitleHolderConfirmationQuery(NatisGetVehicleOwnerVerificationRequest vehicleOwnerTitleHolderConfirmationRequest);
        Task<DriverInformation> GetDriverQuery(NatisGetDriverInformationRequest driverInformationRequest);
        Task<VehicleOwnerRegistrationInformation> RegisterVehicleOwnerQuery(NatisVehicleOwnerRegistrationRequest vehicleOwnerRegistrationRequest, string username, string password, string environmentName, Guid auditLogId);
        Task<OnlineNCOInformation> OnlineNCOQuery(NatisOnlineNCORequest onlineNCORequest, string username, string password, string environmentName, Guid auditLogId);
        Task<OwnershipHistoryData> OwnershipHistoryQuery(NatisOwnershipHistoryRequest ownershipHistoryRequest, string token, string businessRegistrationNumber);
        Task<ControlNumberVerificationData> ControlNumberVerificationQuery(NatisControlNumberVerificationRequest controlNumberVerificationRequest, string token, string businessRegistrationNumber);
        Task<CalculateVehicleLicenceFeeData> CalculateVehicleLicenceFeeQuery(NatisCalculateVehicleLicenceFeeRequest licenceFeeCalculationRequest, string token, string businessRegistrationNumber);
        Task<VehiclesLicenseExpiryData> GetVehiclesAndLicenseExpiryDatesQuery(NatisGetVehiclesAndLicenseExpiryDatesRequest vehiclesAndLicenseExpiryDatesRequest, string token, string businessRegistrationNumber);
        Task<VehiclesQuotationData> GetVehiclesLicenceRenewalQuotationQuery(NatisGetVehiclesQuotationRequest vehiclesQuotationRequest, string token, string businessRegistrationNumber);
        Task<InitiateRenewalData> InitiateVehicleLicenseRenewalQuery(NatisInitiateRenewalRequest initiateVehicleLicenseRenewalRequest, string token, string businessRegistrationNumber);
        Task<CompleteRenewalData> CompleteVehicleLicenseRenewalQuery(NatisCompleteRenewalRequest completeVehicleLicenseRenewalRequest, string token, string businessRegistrationNumber);
    }
}