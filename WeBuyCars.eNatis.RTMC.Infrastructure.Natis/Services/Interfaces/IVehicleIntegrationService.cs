using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IVehicleIntegrationService
    {
        Task<VehicleInformation> GetVehicleQuery(Guid auditLogId, NatisGetVehicleRequest vehicleRequest, string environmentName);
        Task<VehicleInformation> ConvertNatisVehicleResponseXML(string natisVehicleResponse);
        string ConvertNatisVehicleRequestXML(NatisGetVehicleRequest vehicleRequest);
    }
}