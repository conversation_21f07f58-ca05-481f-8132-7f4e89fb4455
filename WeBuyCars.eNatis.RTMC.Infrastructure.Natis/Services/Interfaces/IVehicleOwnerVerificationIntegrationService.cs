using System;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces
{
    public interface IVehicleOwnerVerificationIntegrationService
    {
        Task<VehicleOwnerVerification> GetVehicleOwnerVerificationQuery(Guid auditLogId, NatisGetVehicleOwnerVerificationRequest vehicleOwnerVerificationRequest, string environmentName);
        Task<VehicleOwnerVerification> ConvertNatisVehicleOwnerVerificationResponseXML(string natisVehicleResponse);
        string ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML(NatisGetVehicleOwnerVerificationRequest vehicleOwnerVerificationRequest);
    }
}