using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using System.Net.Http;
using System.Text;
using System.Net;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using System.Xml.Serialization;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Xml;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using Envelope = WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class NatisWRIntegrationService : INatisWRIntegrationService
    {
        #region prop

        readonly eNatisServiceOptions _serviceOptions;
        readonly eNatisEndPointOptions _endPointOptions;
        readonly ILogger<NatisIntegrationService> _logger;
        private readonly HttpClient _httpClient;

        #endregion

        #region ctor

        public NatisWRIntegrationService(
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            IOptionsMonitor<eNatisEndPointOptions> endpointOptions,
            ILogger<NatisIntegrationService> logger,
            HttpClient httpClient
            )
        {

            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _endPointOptions = endpointOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(endpointOptions));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        }

        #endregion

        #region public


        public Task<string> RTMCIntegration(Guid auditLogId, Envelope envelope){

            Task<string> result = null;

            try
            {

                ServicePointManager.ServerCertificateValidationCallback += ValidateCertificate;

                // Serialize the envelope object to XML
                XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
                StringWriter stringWriter = new StringWriter();
                XmlSerializerNamespaces namespaces = new XmlSerializerNamespaces();
                namespaces.Add("soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
                namespaces.Add("sch", "http://tasima/common/ws/schema/");
                namespaces.Add("wsse", "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd");
                serializer.Serialize(stringWriter, envelope, namespaces);

                string xmlString = stringWriter.ToString();
                stringWriter.Close();

                // Remove the namespace declarations from the Envelope element
                xmlString = xmlString.Replace(" xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"", "");
                xmlString = xmlString.Replace(" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\"", "");
                xmlString = xmlString.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "");
                xmlString = xmlString.Replace("<wsse:UsernameToken>", "<wsse:UsernameToken xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">");
                xmlString = xmlString.Replace("<wsse:Security", "<wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"");

                _logger.LogError("GUID : " + auditLogId + " : NatisWRIntegrationService : RTMCIntegration | xmlString : " + xmlString);

                // Prepare the web request
                string webAddress = _serviceOptions.BaseUrl;

                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(webAddress);
                request.Method = "POST";
                request.ContentType = "text/xml;charset=UTF-8";

                byte[] requestData = Encoding.UTF8.GetBytes(xmlString);
                request.ContentLength = requestData.Length;
                Stream requestStream = request.GetRequestStream();
                requestStream.Write(requestData, 0, requestData.Length);
                requestStream.Close();

                // Send the request and get the response
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream responseStream = response.GetResponseStream();
                StreamReader reader = new StreamReader(responseStream);
                string responseXml = reader.ReadToEnd();
                reader.Close();
                responseStream.Close();
                response.Close();

                _logger.LogError("GUID : " + auditLogId + " : NatisWRIntegrationService : RTMCIntegration | RTMC Response : " + responseXml);

                return result = Task.FromResult(responseXml);

            }catch(Exception ex)
            {
                //Log Error with Integration
                _logger.LogError("GUID : " + auditLogId + " : NatisWRIntegrationService : RTMCIntegration | An Exception occurred when integrating to RTMC : Envelope : " + envelope +  " : Exception = " + ex);
                throw ex;                        
            }

        }

        // SSL certificate validation callback method
        private static bool ValidateCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            // Ignore SSL certificate errors
            return true;
        }
        
        #endregion

    }
}
