using System;
using AutoMapper;
using Microsoft.Extensions.Logging;
using System.Xml.Serialization;
using System.IO;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class VehicleDetailIntegrationService  : IVehicleDetailIntegrationService
    {

        #region prop
        readonly ILogger<VehicleDetailIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;

        #endregion

        #region ctor
        
        public VehicleDetailIntegrationService(
            ILogger<VehicleDetailIntegrationService> logger,
            IMapper mapper,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService

        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));
            
        }

        #endregion

        #region public

        public async Task<VehicleDetailInformation> GetVehicleDetailedQuery(Guid auditLogId, NatisGetVehicleDetailedRequest vehicleDetailedRequest, string environmentName)
        {
            
            VehicleDetailInformation result = new VehicleDetailInformation();

            // if(environmentName != "Production")
            // {
            //     //Check if the End Point contains a Prod in the URL
            //     if(_serviceOptions.BaseUrl.Contains("prod"))
            //     {
            //         result.BaseResponse = new BaseResponse()
            //         {
            //             Successful = false,
            //             Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
            //         };
            //         return result;
            //     }
            // }    

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = _serviceOptions.PayloadUsername,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = _serviceOptions.PayloadPassword
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X3067Request = new Models.Shared.Envelope.X3067Request()
                        {
                            VehicleQuery = new Models.Shared.Envelope.VehicleQuery()
                            {
                                RegisterNumber = vehicleDetailedRequest.RegisterNumber,
                                VinOrChassis = vehicleDetailedRequest.VinOrChassis,
                                LicenceNumber = vehicleDetailedRequest.LicenceNumber,
                                EngineNumber = vehicleDetailedRequest.EngineNumber,
                            }
                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                //Convert Response Object to Class
                result = await ConvertNatisVehicleDetailedResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : VehicleDetailIntegrationService : GetVehicleQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "An Error has ocurred when attempting a call to RTMC Exception : " + ex.ToString()
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Vehicle Detailed Request to XML String used for Enatis request
        /// </summary>
        /// <param name="vehicleRequest"></param>
        /// <returns></returns>
        public string ConvertNatisVehicleDetailedRequestXML(NatisGetVehicleDetailedRequest vehicleRequest){

            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X3067Request><sch:VehicleQuery>";

                if(vehicleRequest.RegisterNumber != null)
                {
                    result = result + $"<sch:RegisterNumber>{vehicleRequest.RegisterNumber}</sch:RegisterNumber>";
                }

                if(vehicleRequest.VinOrChassis != null)
                {
                    result = result + $"<sch:VinOrChassis>{vehicleRequest.VinOrChassis}</sch:VinOrChassis>";
                }

                if(vehicleRequest.EngineNumber != null)
                {
                    result = result + $"<sch:EngineNumber>{vehicleRequest.EngineNumber}</sch:EngineNumber>";
                }

                if(vehicleRequest.LicenceNumber != null)
                {
                    result = result + $"<sch:LicenceNumber>{vehicleRequest.LicenceNumber}</sch:LicenceNumber>";
                }

                //Apply Trailing Record Information
                result = result +  "</sch:VehicleQuery></sch:X3067Request>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";


                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }


        }

        /// <summary>
        /// Convert Vehicle Response from string to Object
        /// </summary>
        /// <param name="natisVehicleResponse"></param>
        /// <returns></returns>/
        public async Task<VehicleDetailInformation> ConvertNatisVehicleDetailedResponseXML(string natisVehicleResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed.Envelope));
                using (StringReader reader = new StringReader(natisVehicleResponse))
                {
                    var natisGetVehicleDetailedResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed.Envelope)serializer.Deserialize(reader);

                    if(natisGetVehicleDetailedResponse.Body.X3067Response != null)
                    {
                        if(natisGetVehicleDetailedResponse.Body.X3067Response.executionResult.successful == true)
                        {
                            return MapXMLtoVehicleDetailedResponse(natisGetVehicleDetailedResponse);
                        }else
                        {
                            _logger.LogError("Error XML Response Message | Response : + " + JsonConvert.SerializeObject(natisGetVehicleDetailedResponse,Formatting.Indented));
                            return MapXMLErrorToVehicleDetailedResponse(natisGetVehicleDetailedResponse);             
                        }
                    }

                    if(natisGetVehicleDetailedResponse.Body.Fault != null )
                    {
                            _logger.LogTrace("Fault in XML Response Message | Response : + " + JsonConvert.SerializeObject(natisGetVehicleDetailedResponse,Formatting.Indented));
                            return MapXMLErrorToVehicleDetailedResponse(natisGetVehicleDetailedResponse);     
                    }

                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + JsonConvert.SerializeObject(natisGetVehicleDetailedResponse,Formatting.Indented));

                }
            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }
        #endregion

        #region private

        /// <summary>
        /// Convert Vehicle XML to Class
        /// </summary>
        /// <param name="vehiclerequestxml"></param>
        /// <returns></returns>
        private VehicleDetailInformation MapXMLtoVehicleDetailedResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed.Envelope vehiclerequestxml)
        {
            try{

                var vehicleDetailResponse = new VehicleDetailInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehiclerequestxml);
                vehicleDetailResponse = _mapper.Map<VehicleDetailInformation>(vehiclerequestxml);

                vehicleDetailResponse.BaseResponse = baseResponse;
                vehicleDetailResponse.BaseResponse.Successful = true;

                return vehicleDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Vehicle XML to Class
        /// </summary>
        /// <param name="vehiclerequestxml"></param>
        /// <returns></returns>
        private VehicleDetailInformation MapXMLErrorToVehicleDetailedResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed.Envelope vehiclerequestxml)
        {
            try{

                var vehicleDetailResponse = new VehicleDetailInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehiclerequestxml);
                vehicleDetailResponse = _mapper.Map<VehicleDetailInformation>(vehiclerequestxml);

                vehicleDetailResponse.BaseResponse = baseResponse;
                vehicleDetailResponse.BaseResponse.Successful = false;

                return vehicleDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }





        #endregion





    }

}