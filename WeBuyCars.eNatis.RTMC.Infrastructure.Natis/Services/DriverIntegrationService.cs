using System;
using System.IO;
using System.Threading.Tasks;
using System.Xml.Serialization;
using AutoMapper;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class DriverIntegrationService : IDriverIntegrationService
    {

        #region prop
        readonly ILogger<NatisIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;
        #endregion
        
        #region ctor
        public DriverIntegrationService(
            IMapper mapper,
            ILogger<NatisIntegrationService> logger,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService
        )
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));

        }
        #endregion

        #region public
        
        public async Task<DriverInformation> GetDriverQuery(Guid auditLogId, NatisGetDriverInformationRequest driverInformationRequest, string environmentName)
        {
            
            DriverInformation result = new DriverInformation();

            // if(environmentName != "Production")
            // {
            //     //Check if the End Point contains a Prod in the URL
            //     if(_serviceOptions.BaseUrl.Contains("prod"))
            //     {
            //         result.BaseResponse = new BaseResponse()
            //         {
            //             Successful = false,
            //             Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
            //         };
            //         return result;
            //     }
            // }    

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = _serviceOptions.PayloadUsername,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = _serviceOptions.PayloadPassword
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X3042Request = new Models.Shared.Envelope.X3042Request()
                        {

                            IdDocumentNumber = driverInformationRequest.DocumentNumber,
                            IdDocumentTypeCode = driverInformationRequest.DocumentTypeCode,

                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                //Convert Response Object to Class
                result = await ConvertNatisDriverInformationResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : DriverIntegrationService : GetDriverQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "An Error has ocurred when attempting a call to RTMC Exception : " + ex.ToString()
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Driver Information Request to XML String used for Enatis request
        /// </summary>
        /// <param name="driverInformationRequest"></param>
        /// <returns></returns>
        public string ConvertNatisDriverInformationRequestXML(NatisGetDriverInformationRequest driverInformationRequest){

            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X3042Request>";

                if(driverInformationRequest.DocumentTypeCode != null)
                {
                    result = result + $"<sch:idDocumentTypeCode>{driverInformationRequest.DocumentTypeCode}</sch:idDocumentTypeCode>";
                }

                if(driverInformationRequest.DocumentNumber != null)
                {
                    result = result + $"<sch:idDocumentNumber>{driverInformationRequest.DocumentNumber}</sch:idDocumentNumber>";
                }

                //Apply Trailing Record Information
                result = result +  "</sch:X3042Request>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";

                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisDriverInformationRequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisDriverInformationRequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisDriverInformationRequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }


        }

        /// <summary>
        /// Convert Driver Information Response from string to Object
        /// </summary>
        /// <param name="natisDriverInformationResponse"></param>
        /// <returns></returns>/
        public async Task<DriverInformation> ConvertNatisDriverInformationResponseXML(string natisDriverInformationResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation.Envelope));
                using (StringReader reader = new StringReader(natisDriverInformationResponse))
                {
                    var natisGetDriverInformationResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation.Envelope)serializer.Deserialize(reader);

                    if(natisGetDriverInformationResponse.Body.X3042Response != null)
                    {
                        if(natisGetDriverInformationResponse.Body.X3042Response.executionResult.successful == true)
                        {
                            return await MapXMLtoDriverInformationResponse(natisGetDriverInformationResponse);
                        }else
                        {
                            _logger.LogTrace("RTMCDriverService : ConvertNatisDriverInformationResponseXML | Error Message Response : + " + natisGetDriverInformationResponse.ToString());
                            return await MapXMLErrorToDriverInformationResponse(natisGetDriverInformationResponse);                            
                        }
                    }

                    if(natisGetDriverInformationResponse.Body.Fault != null )
                    {
                            _logger.LogTrace("Fault in XML Response Message | Response : + " + natisGetDriverInformationResponse.ToString());
                            return await MapXMLErrorToDriverInformationResponse(natisGetDriverInformationResponse);    
                    }

                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + natisGetDriverInformationResponse);

                }
            }catch(Exception ex)
            {
                _logger.LogError("RTMCDriverService : ConvertNatisDriverInformationResponseXML | An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }

        #endregion

        #region private
        /// <summary>
        /// Convert Driver Information Response XML to Class
        /// </summary>
        /// <param name="driverInformationResponseXML"></param>
        /// <returns></returns>
        private async Task<DriverInformation> MapXMLtoDriverInformationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation.Envelope driverInformationResponseXML)
        {
            try{

                var driverInformationDetailResponse = new DriverInformation();

                var baseResponse = _mapper.Map<BaseResponse>(driverInformationResponseXML);
                driverInformationDetailResponse = _mapper.Map<DriverInformation>(driverInformationResponseXML);

                driverInformationDetailResponse.BaseResponse = baseResponse;
                driverInformationDetailResponse.BaseResponse.Successful = true;

                return driverInformationDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("RTMCDriverService : MapXMLtoDriverInformationResponse | An Exception occurred when mapping MapXMLtoDriverInformationResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoDriverInformationResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Driver Information Response XML to Class
        /// </summary>
        /// <param name="driverInformationResponseXML"></param>
        /// <returns></returns>
        private async Task<DriverInformation> MapXMLErrorToDriverInformationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation.Envelope driverInformationResponseXML)
        {
            try{

                var driverInformationDetailResponse = new DriverInformation();

                var baseResponse = _mapper.Map<BaseResponse>(driverInformationResponseXML);
                driverInformationDetailResponse = _mapper.Map<DriverInformation>(driverInformationResponseXML);

                driverInformationDetailResponse.BaseResponse = baseResponse;
                driverInformationDetailResponse.BaseResponse.Successful = false;

                return driverInformationDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("RTMCDriverService : MapXMLErrorToDriverInformationResponse | An Exception occurred when mapping MapXMLErrorToDriverInformationResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToDriverInformationResponse XML Response string to Class : Exception = " + ex);
            }
        }
        #endregion


    }
}