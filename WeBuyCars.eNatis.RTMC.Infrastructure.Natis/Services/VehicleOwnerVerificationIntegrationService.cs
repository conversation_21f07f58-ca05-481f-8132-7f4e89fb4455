using System;
using AutoMapper;
using Microsoft.Extensions.Logging;
using System.Xml.Serialization;
using System.IO;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class VehicleOwnerVerificationIntegrationService  : IVehicleOwnerVerificationIntegrationService
    {
        #region ctor

        readonly ILogger<VehicleOwnerVerificationIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;
        
        public VehicleOwnerVerificationIntegrationService(
            ILogger<VehicleOwnerVerificationIntegrationService> logger,
            IMapper mapper,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));
        }

        #endregion

        #region public

        public async Task<VehicleOwnerVerification> GetVehicleOwnerVerificationQuery(Guid auditLogId, NatisGetVehicleOwnerVerificationRequest vehicleOwnerVerificationRequest, string environmentName)
        {
            
            VehicleOwnerVerification result = new VehicleOwnerVerification();

            // if(environmentName != "Production")
            // {
            //     //Check if the End Point contains a Prod in the URL
            //     if(_serviceOptions.BaseUrl.Contains("prod"))
            //     {
            //         result.BaseResponse = new BaseResponse()
            //         {
            //             Successful = false,
            //             Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
            //         };
            //         return result;
            //     }
            // }

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = _serviceOptions.PayloadUsername,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = _serviceOptions.PayloadPassword
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X3004Request = new Models.Shared.Envelope.X3004Request()
                        {
                            vehicle = new Models.Shared.Envelope.vehicle()
                            {
                                vinOrChassis = vehicleOwnerVerificationRequest.Vin,
                                registerNumber = vehicleOwnerVerificationRequest.RegisterNumber,
                                licenceNumber = vehicleOwnerVerificationRequest.LicenceNumber
                            },
                            person = new Models.Shared.Envelope.person()
                            {
                                IdDocumentNumber = vehicleOwnerVerificationRequest.DocumentNumber,
                                IdDocumentTypeCode = vehicleOwnerVerificationRequest.DocumentTypeCode
                            }
                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                // //Convert Response Object to Class
                result = await ConvertNatisVehicleOwnerVerificationResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : VehicleDetailIntegrationService : GetVehicleQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "An Error has ocurred when attempting a call to RTMC Exception : " + ex.ToString(),
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Vehicle Owner Title Holder Confirmation Request to XML String used for Enatis request
        /// </summary>
        /// <param name="vehicleOwnerVerificationRequest"></param>
        /// <returns></returns>
        public string ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML(NatisGetVehicleOwnerVerificationRequest vehicleOwnerVerificationRequest){

            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X3004Request>
        <sch:person>";

                if(vehicleOwnerVerificationRequest.DocumentTypeCode != null)
                {
                    result = result + $"<sch:idDocumentTypeCode>{vehicleOwnerVerificationRequest.DocumentTypeCode}</sch:idDocumentTypeCode>";
                }

                if(vehicleOwnerVerificationRequest.DocumentNumber != null)
                {
                    result = result + $"<sch:idDocumentNumber>{vehicleOwnerVerificationRequest.DocumentNumber}</sch:idDocumentNumber>";
                }

                result = result + "</sch:person><sch:vehicle>";

                if(vehicleOwnerVerificationRequest.Vin != null)
                {
                    result = result + $"<sch:vinOrChassis>{vehicleOwnerVerificationRequest.Vin}</sch:vinOrChassis>";
                }

                if(vehicleOwnerVerificationRequest.RegisterNumber != null)
                {
                    result = result + $"<sch:registerNumber>{vehicleOwnerVerificationRequest.RegisterNumber}</sch:registerNumber>";
                }

                if(vehicleOwnerVerificationRequest.LicenceNumber != null)
                {
                    result = result + $"<sch:licenceNumber>{vehicleOwnerVerificationRequest.LicenceNumber}</sch:licenceNumber>";
                }

                result = result + "</sch:vehicle>";

                //Apply Trailing Record Information
                result = result +  "</sch:X3004Request>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";

                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisVehicleOwnerTitleHolderConfirmationRequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }


        }

        /// <summary>
        /// Convert Vehicle Owner Verification Response from string to Object
        /// </summary>
        /// <param name="natisVehicleResponse"></param>
        /// <returns></returns>/
        public async Task<VehicleOwnerVerification> ConvertNatisVehicleOwnerVerificationResponseXML(string natisVehicleResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification.Envelope));
                using (StringReader reader = new StringReader(natisVehicleResponse))
                {
                    var natisGetVehicleOwnershipVerificationResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification.Envelope)serializer.Deserialize(reader);

                    if(natisGetVehicleOwnershipVerificationResponse.Body.X3004Response.executionResult.successful == true)
                    {
                        return await MapXMLtoVehicleOwnerhsipVerificationResponse(natisGetVehicleOwnershipVerificationResponse);
                    }else
                    {
                        _logger.LogTrace("RTMCVehicleOwnerVerifcationService : ConvertNatisVehicleResponseXML | Error Message Response : + " + natisGetVehicleOwnershipVerificationResponse.ToString());

                        return await MapXMLErrorToVehicleOwnershipVerificationResponse(natisGetVehicleOwnershipVerificationResponse);
                        
                    }
                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + natisGetVehicleOwnershipVerificationResponse);

                }
            }catch(Exception ex)
            {
                _logger.LogError("RTMCVehicleOwnerVerifcationService : ConvertNatisVehicleResponseXML | An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }

        #endregion

        #region private

        /// <summary>
        /// Convert Vehicle Owner Verification XML to Class
        /// </summary>
        /// <param name="vehicleOwnershipVerificationRequestXML"></param>
        /// <returns></returns>
        private async Task<VehicleOwnerVerification> MapXMLtoVehicleOwnerhsipVerificationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification.Envelope vehicleOwnershipVerificationRequestXML)
        {
            try{

                var vehicleOwnershipVerificationDetailResponse = new VehicleOwnerVerification();

                var baseResponse = _mapper.Map<BaseResponse>(vehicleOwnershipVerificationRequestXML);

                baseResponse.Successful = true;

                vehicleOwnershipVerificationDetailResponse = _mapper.Map<VehicleOwnerVerification>(vehicleOwnershipVerificationRequestXML);

                vehicleOwnershipVerificationDetailResponse.BaseResponse = baseResponse;

                return vehicleOwnershipVerificationDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("RTMCVehicleOwnerVerifcationService : MapXMLtoVehicleResponse | An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Vehicle Owner Verification XML to Class
        /// </summary>
        /// <param name="vehicleOwnershipRequestXML"></param>
        /// <returns></returns>
        private async Task<VehicleOwnerVerification> MapXMLErrorToVehicleOwnershipVerificationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification.Envelope vehicleOwnershipRequestXML)
        {
            try{

                var vehicleOwnerhipsVerificationDetailResponse = new VehicleOwnerVerification();

                var baseResponse = _mapper.Map<BaseResponse>(vehicleOwnershipRequestXML);

                baseResponse.Successful = false;

                vehicleOwnerhipsVerificationDetailResponse = _mapper.Map<VehicleOwnerVerification>(vehicleOwnershipRequestXML);

                vehicleOwnerhipsVerificationDetailResponse.BaseResponse = baseResponse;

                return vehicleOwnerhipsVerificationDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("RTMCVehicleOwnerVerifcationService : MapXMLtoVehicleResponse | An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }

        #endregion
    }

}