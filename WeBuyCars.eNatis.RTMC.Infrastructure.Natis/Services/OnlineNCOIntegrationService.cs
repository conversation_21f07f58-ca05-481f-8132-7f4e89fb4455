using System;
using System.IO;
using System.Threading.Tasks;
using System.Xml.Serialization;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class OnlineNCOIntegrationService : IOnlineNCOIntegrationService
    {
        #region prop
        readonly ILogger<NatisIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;
        #endregion
        
        #region ctor
        public OnlineNCOIntegrationService(
            IMapper mapper,
            ILogger<NatisIntegrationService> logger,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService
        )
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));
        }
            
        #endregion

        #region public

        public async Task<OnlineNCOInformation> NominateOwnerChangeQuery(Guid auditLogId, NatisOnlineNCORequest natisOnlineNCOInformationRequest, string username, string password, string environmentName)
        {
            
            OnlineNCOInformation result = new OnlineNCOInformation();

            //This needs to be Uncommented when moving to Production
            if(environmentName != "Production")
            {
                //Check if the End Point contains a Prod in the URL
                if(_serviceOptions.BaseUrl.Contains("prod"))
                {
                    result.BaseResponse = new BaseResponse()
                    {
                        Successful = false,
                        Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
                    };
                    return result;
                }
            }    

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = username,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = password
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X314ARequest = new Models.Shared.Envelope.X314ARequest()
                        {
                            Receiver = new Models.Shared.Envelope.Receiver()
                            {
                                IdDocumentType = natisOnlineNCOInformationRequest.ReceiverDocumentTypeCode,
                                IdDocumentNumber = natisOnlineNCOInformationRequest.ReceiverDocumentNumber,
                            },
                            Vehicle = new Models.Shared.Envelope.Vehicle()
                            {
                                VinOrChassis = natisOnlineNCOInformationRequest.VinOrChassis,
                                ControlNumber = natisOnlineNCOInformationRequest.ControlNumber,
                                RegisterNumber = natisOnlineNCOInformationRequest.RegisterNumber,
                                LicenceNumber = natisOnlineNCOInformationRequest.LicenceNumber,
                            },
                            ChangeDate = natisOnlineNCOInformationRequest.ChangeDate,
                            ReceiverDetails = new Models.Shared.Envelope.ReceiverDetails()
                            {
                                // Proxy = new Models.Shared.Envelope.Proxy()
                                // {
                                //     IdDocumentType = natisOnlineNCOInformationRequest.ReceiverProxyDocumentTypeCode,
                                //     IdDocumentNumber = natisOnlineNCOInformationRequest.ReceiverProxyDocumentNumber,
                                // },
                                // Representative = new Models.Shared.Envelope.Representative()
                                // {
                                //     IdDocumentType = natisOnlineNCOInformationRequest.ReceiverRepresentativeDocumentTypeCode,
                                //     IdDocumentNumber = natisOnlineNCOInformationRequest.ReceiverRepresentativeDocumentNumber,
                                // },
                            },
                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                //Convert Response Object to Class
                result = await ConvertNatisOnlineNCOInformationResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : DriverIntegrationService : GetDriverQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "An Error has ocurred when attempting a call to RTMC Exception : " + ex.ToString()
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Online NCO Request to XML String used for Enatis request
        /// </summary>
        /// <param name="onlineNCORequest"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        /// <exception cref="DomainException"></exception>
        public string ConvertNatisOnlineNCORequestXML(NatisOnlineNCORequest onlineNCORequest, string username, string password){
            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X314ARequest><sch:Receiver>";

                //Receiver Information
                if(onlineNCORequest.ReceiverDocumentTypeCode != null)
                {
                    result = result + $"<sch:IdDocumentType>{onlineNCORequest.ReceiverDocumentTypeCode}</sch:IdDocumentType>";
                }
                if(onlineNCORequest.ReceiverDocumentNumber != null)
                {
                    result = result + $"<sch:IdDocumentNumber>{onlineNCORequest.ReceiverDocumentNumber}</sch:IdDocumentNumber>";
                }
                result = result + "</sch:Receiver>";

                //Vehicle Details
                result = result + "<sch:Vehicle>";
                if(onlineNCORequest.VinOrChassis != null)
                {
                    result = result + $"<sch:VinOrChassis>{onlineNCORequest.VinOrChassis}</sch:VinOrChassis>";
                }
                if(onlineNCORequest.RegisterNumber != null)
                {
                    result = result + $"<sch:RegisterNumber>{onlineNCORequest.RegisterNumber}</sch:RegisterNumber>";
                }
                if(onlineNCORequest.LicenceNumber != null)
                {
                    result = result + $"<sch:LicenceNumber>{onlineNCORequest.LicenceNumber}</sch:LicenceNumber>";
                }
                result = result +  "</sch:Vehicle>";

                if(onlineNCORequest.ChangeDate != null)
                {
                    var changeDate = onlineNCORequest.ChangeDate.Substring(0,10).Replace("/","-");

                    result = result + $"<sch:ChangeDate>{changeDate}</sch:ChangeDate>";
                }

                //Receiver Details
                result = result + "<sch:ReceiverDetails>";

                //Receiver Proxy Information
                if(onlineNCORequest.ReceiverProxyDocumentNumber != null)
                {
                    if(onlineNCORequest.ReceiverProxyDocumentTypeCode != null)
                    {
                        result = result + $"<sch:Proxy><sch:IdDocumentType>{onlineNCORequest.ReceiverProxyDocumentTypeCode}</sch:IdDocumentType>";
                    }
                    result = result + $"<sch:IdDocumentNumber>{onlineNCORequest.ReceiverProxyDocumentNumber}</sch:IdDocumentNumber></sch:Proxy>";
                }
                //Receiver Information
                if(onlineNCORequest.ReceiverRepresentativeDocumentNumber != null)
                {
                    if(onlineNCORequest.ReceiverRepresentativeDocumentTypeCode != null)
                    {
                        result = result + $"<sch:Representative><sch:IdDocumentType>{onlineNCORequest.ReceiverRepresentativeDocumentTypeCode}</sch:IdDocumentType>";
                    }

                    result = result + $"<sch:IdDocumentNumber>{onlineNCORequest.ReceiverRepresentativeDocumentNumber}</sch:IdDocumentNumber></sch:Representative>";
                }

                //Apply Trailing Record Information
                result = result +  "</sch:ReceiverDetails></sch:X314ARequest>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";

                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisOnlineNCORequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisOnlineNCORequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisOnlineNCORequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }

        }

        /// <summary>
        /// Convert Online NCO Information Response from string to Object
        /// </summary>
        /// <param name="natisOnlineNCOInformationResponse"></param>
        /// <returns></returns>/
        public async Task<OnlineNCOInformation> ConvertNatisOnlineNCOInformationResponseXML(string natisOnlineNCOInformationResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO.Envelope));
                using (StringReader reader = new StringReader(natisOnlineNCOInformationResponse))
                {
                    var natisVehicleOwnerOnlineNCOInformationResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO.Envelope)serializer.Deserialize(reader);

                    if(natisVehicleOwnerOnlineNCOInformationResponse.Body.X314AResponse != null)
                    {
                        if(natisVehicleOwnerOnlineNCOInformationResponse.Body.X314AResponse.transactionStatus == "SUCCESS")
                        {
                            return await MapXMLtoOnlineNCOResponse(natisVehicleOwnerOnlineNCOInformationResponse);
                        }else
                        {
                            _logger.LogTrace("OnlineNCOIntegrationService : ConvertNatisOnlineNCOInformationResponseXML | Error Message Response : + " + natisVehicleOwnerOnlineNCOInformationResponse.ToString());
                            return await MapXMLErrorToOnlineNCOResponse(natisVehicleOwnerOnlineNCOInformationResponse);                            
                        }
                    }

                    if(natisVehicleOwnerOnlineNCOInformationResponse.Body != null )
                    {
                            _logger.LogTrace("Fault in XML Response Message | Response : + " + natisVehicleOwnerOnlineNCOInformationResponse.ToString());
                            return await MapXMLErrorToOnlineNCOResponse(natisVehicleOwnerOnlineNCOInformationResponse);    
                    }

                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + natisVehicleOwnerOnlineNCOInformationResponse);

                }
            }catch(Exception ex)
            {
                _logger.LogError("OnlineNCOIntegrationService : ConvertNatisOnlineNCOInformationResponseXML | An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }

        #endregion

        #region private
        /// <summary>
        /// Convert Online NCO Information Request XML to Class
        /// </summary>
        /// <param name="onlineNCORequestXML"></param>
        /// <returns></returns>
        private async Task<OnlineNCOInformation> MapXMLtoOnlineNCOResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO.Envelope onlineNCORequestXML)
        {
            try{

                var onlineNCOResponse = new OnlineNCOInformation();

                var baseResponse = _mapper.Map<BaseResponse>(onlineNCORequestXML);

                baseResponse.Successful = true;

                onlineNCOResponse = _mapper.Map<OnlineNCOInformation>(onlineNCORequestXML);

                onlineNCOResponse.BaseResponse = baseResponse;

                return onlineNCOResponse;

            }catch(Exception ex)
            {
                _logger.LogError("OnlineNCOIntegrationService : MapXMLtoOnlineNCOResponse | An Exception occurred when mapping MapXMLtoOnlineNCOResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Online NCO Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoOnlineNCOResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Online NCO Information Request XML to Class
        /// </summary>
        /// <param name="onlineNCORequestXML"></param>
        /// <returns></returns>
        private async Task<OnlineNCOInformation> MapXMLErrorToOnlineNCOResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO.Envelope onlineNCORequestXML)
        {
            try{

                var onlineNCOResponse = new OnlineNCOInformation();

                var baseResponse = _mapper.Map<BaseResponse>(onlineNCORequestXML);

                baseResponse.Successful = false;
                baseResponse.Message = onlineNCORequestXML.Body.X314AResponse.messages.field;
                baseResponse.StatusCode = onlineNCORequestXML.Body.X314AResponse.messages.code;                

                //Apply Code to Response Message if possible
                if(!String.IsNullOrWhiteSpace(baseResponse.StatusCode))
                {
                    try
                    {
                        var errorCode = typeof(NatisErrorList).GetField(baseResponse.StatusCode, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                        var errorValue = (string)errorCode.GetValue(null);
                        
                        baseResponse.Message = errorValue;

                    }catch(Exception ex)
                    {
                        _logger.LogError("OnlineNCOIntegrationService : MapXMLErrorToOnlineNCOResponse | An Exception occurred when mapping MapXMLErrorToOnlineNCOResponse XML Response Were not able to Convert Error Code to Description : Exception = " + ex);   
                    }

                }

                onlineNCOResponse.BaseResponse = baseResponse;

                return onlineNCOResponse;

            }catch(Exception ex)
            {
                _logger.LogError("OnlineNCOIntegrationService : MapXMLErrorToOnlineNCOResponse | An Exception occurred when mapping MapXMLErrorToOnlineNCOResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Online NCO Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToOnlineNCOResponse XML Response string to Class : Exception = " + ex);
            }
        }
        #endregion

    }
}