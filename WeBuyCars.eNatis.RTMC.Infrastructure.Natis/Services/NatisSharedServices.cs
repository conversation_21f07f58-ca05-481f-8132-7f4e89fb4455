using System;
using Microsoft.Extensions.Logging;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{

    public class NatisSharedServices
    {

        #region prop
        readonly ILogger<NatisSharedServices> _logger;
        #endregion

        #region ctor
            
        public NatisSharedServices(ILogger<NatisSharedServices> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #endregion

        #region public
        /// <summary>
        /// Remove Payload Password
        /// </summary>
        /// <param name="input"></param>
        /// <returns></returns>
        public string MaskXMLPassword(string input)
        {

            var result = "";

            try
            {
                var startIndex = input.IndexOf(@"PasswordText");
                var endIndex = input.IndexOf(@"</wsse:Password>");

                if(startIndex < endIndex)
                {
                    result = input.Remove(startIndex + 14, endIndex - startIndex - 14);
                }            
            }catch(Exception ex)
            {
                _logger.LogError("Error : Unable to Mask Password" + ex.ToString());
            }

            return result;
        }

        #endregion




    }

}
