using System;
using System.IO;
using System.Threading.Tasks;
using System.Xml.Serialization;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class VehicleOwnerRegistrationIntegrationService : IVehicleOwnerRegistrationIntegrationService
    {
        #region prop
        readonly ILogger<NatisIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;

        #endregion
        
        #region ctor
        public VehicleOwnerRegistrationIntegrationService(
            IMapper mapper,
            ILogger<NatisIntegrationService> logger,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService
        )
        {
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));
        }
            
        #endregion

        #region public

        public async Task<VehicleOwnerRegistrationInformation> RegisterVehicleOwnerQuery(Guid auditLogId, NatisVehicleOwnerRegistrationRequest vehicleOwnerRegistrationRequest, string username, string password, string environmentName)
        {
            
            VehicleOwnerRegistrationInformation result = new VehicleOwnerRegistrationInformation();

            //This needs to be Uncommented when moving to Production
            if(environmentName != "Production")
            {
                //Check if the End Point contains a Prod in the URL
                if(_serviceOptions.BaseUrl.Contains("prod"))
                {
                    result.BaseResponse = new BaseResponse()
                    {
                        Successful = false,
                        Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
                    };
                    return result;
                }
            }    

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = username,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = password
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X3141Request = new Models.Shared.Envelope.X3141Request()
                        {
                            Owner = new Models.Shared.Envelope.Owner()
                            {
                                IdDocumentType = vehicleOwnerRegistrationRequest.OwnerDocumentTypeCode,
                                IdDocumentNumber = vehicleOwnerRegistrationRequest.OwnerDocumentNumber,
                            },
                            OwnerProxy = new Models.Shared.Envelope.OwnerProxy()
                            {
                                IdDocumentType = vehicleOwnerRegistrationRequest.ProxyDocumentTypeCode,
                                IdDocumentNumber = vehicleOwnerRegistrationRequest.ProxyDocumentNumber
                            },
                            OwnerRepresentative = new Models.Shared.Envelope.OwnerRepresentative()
                            {
                                IdDocumentType = vehicleOwnerRegistrationRequest.RepresentativeDocumentTypeCode,
                                IdDocumentNumber = vehicleOwnerRegistrationRequest.RepresentativeDocumentNumber
                            },
                            Vehicle = new Models.Shared.Envelope.Vehicle()
                            {
                                RegisterNumber = vehicleOwnerRegistrationRequest.RegisterNumber,
                                VinOrChassis = vehicleOwnerRegistrationRequest.VinOrChassis,
                                LicenceNumber = vehicleOwnerRegistrationRequest.LicenceNumber,
                                RegistrationLiabiltyDate = vehicleOwnerRegistrationRequest.RegistrationLiabilityDate,
                                NatureOfOwnership = vehicleOwnerRegistrationRequest.NatureOfOwnership,
                                VehicleUsage = vehicleOwnerRegistrationRequest.VehicleUsage,
                                VehicleCertificateNumber = vehicleOwnerRegistrationRequest.VehicleCertificateNumber,
                                RegistrationReason = vehicleOwnerRegistrationRequest.RegistrationReason,

                            }
                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                //Convert Response Object to Class
                result = await ConvertNatisVehicleOwnerRegistrationResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : DriverIntegrationService : GetDriverQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "An Error has ocurred when attempting a call to RTMC Exception : " + ex.ToString()
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Vehicle Ownership Registration Request to XML String used for Enatis request
        /// </summary>
        /// <param name="registerVehicleOwnerRequest"></param>
        /// <param name="username"></param>
        /// <param name="password"></param>
        /// <returns></returns>
        /// <exception cref="DomainException"></exception>
        public string ConvertNatisRegisterVehicleOwnerRequestXML(NatisVehicleOwnerRegistrationRequest registerVehicleOwnerRequest, string username, string password){
            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X3141Request><sch:Owner>";

                //Owner Information
                if(registerVehicleOwnerRequest.OwnerDocumentTypeCode != null)
                {
                    result = result + $"<sch:IdDocumentType>{registerVehicleOwnerRequest.OwnerDocumentTypeCode}</sch:IdDocumentType>";
                }

                if(registerVehicleOwnerRequest.OwnerDocumentNumber != null)
                {
                    result = result + $"<sch:IdDocumentNumber>{registerVehicleOwnerRequest.OwnerDocumentNumber}</sch:IdDocumentNumber>";
                }

                result = result + "</sch:Owner>";

                //Owner Proxy Information
                if(registerVehicleOwnerRequest.ProxyDocumentTypeCode != null)
                {
                    result = result + $"<sch:OwnerProxy><sch:IdDocumentType>{registerVehicleOwnerRequest.ProxyDocumentTypeCode}</sch:IdDocumentType>";
                }

                if(registerVehicleOwnerRequest.ProxyDocumentNumber != null)
                {
                    result = result + $"<sch:IdDocumentNumber>{registerVehicleOwnerRequest.ProxyDocumentNumber}</sch:IdDocumentNumber></sch:OwnerProxy>";
                }

                //Representative Information
                if(registerVehicleOwnerRequest.RepresentativeDocumentTypeCode != null)
                {
                    result = result + $"<sch:OwnerRepresentative><sch:IdDocumentType>{registerVehicleOwnerRequest.RepresentativeDocumentTypeCode}</sch:IdDocumentType>";
                }

                if(registerVehicleOwnerRequest.RepresentativeDocumentNumber != null)
                {
                    result = result + $"<sch:IdDocumentNumber>{registerVehicleOwnerRequest.RepresentativeDocumentNumber}</sch:IdDocumentNumber></sch:OwnerRepresentative>";
                }

                //Vehicle Details
                result = result + "<sch:Vehicle>";

                if(registerVehicleOwnerRequest.RegisterNumber != null)
                {
                    result = result + $"<sch:RegisterNumber>{registerVehicleOwnerRequest.RegisterNumber}</sch:RegisterNumber>";
                }
                if(registerVehicleOwnerRequest.VinOrChassis != null)
                {
                    result = result + $"<sch:VinOrChassis>{registerVehicleOwnerRequest.VinOrChassis}</sch:VinOrChassis>";
                }
                if(registerVehicleOwnerRequest.LicenceNumber != null)
                {
                    result = result + $"<sch:LicenceNumber>{registerVehicleOwnerRequest.LicenceNumber}</sch:LicenceNumber>";
                }
                if(registerVehicleOwnerRequest.RegistrationLiabilityDate != null)
                {
                    var registrationLiabilityDate = registerVehicleOwnerRequest.RegistrationLiabilityDate.Substring(0,10).Replace("/","-");

                    result = result + $"<sch:RegistrationLiabiltyDate>{registrationLiabilityDate}</sch:RegistrationLiabiltyDate>";
                }
                if(registerVehicleOwnerRequest.NatureOfOwnership != null)
                {
                    result = result + $"<sch:NatureOfOwnership>{registerVehicleOwnerRequest.NatureOfOwnership}</sch:NatureOfOwnership>";
                }
                if(registerVehicleOwnerRequest.VehicleUsage != null)
                {
                    result = result + $"<sch:VehicleUsage>{registerVehicleOwnerRequest.VehicleUsage}</sch:VehicleUsage>";
                }
                if(registerVehicleOwnerRequest.VehicleCertificateNumber != null)
                {
                    result = result + $"<sch:VehicleCertificateNumber>{registerVehicleOwnerRequest.VehicleCertificateNumber}</sch:VehicleCertificateNumber>";
                }
                if(registerVehicleOwnerRequest.RegistrationReason != null)
                {
                    result = result + $"<sch:RegistrationReason>{registerVehicleOwnerRequest.RegistrationReason}</sch:RegistrationReason>";
                }

                //Apply Trailing Record Information
                result = result +  "</sch:Vehicle></sch:X3141Request>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";

                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisRegisterVehicleOwnerRequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisRegisterVehicleOwnerRequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisRegisterVehicleOwnerRequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }

        }

        /// <summary>
        /// Convert Vehicle Owner Registration Information Response from string to Object
        /// </summary>
        /// <param name="natisVehicleOwnerRegistrationResponse"></param>
        /// <returns></returns>/
        public async Task<VehicleOwnerRegistrationInformation> ConvertNatisVehicleOwnerRegistrationResponseXML(string natisVehicleOwnerRegistrationResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope));
                using (StringReader reader = new StringReader(natisVehicleOwnerRegistrationResponse))
                {
                    var natisRegisterVehicleOwnerRegistrationInformationResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope)serializer.Deserialize(reader);

                    if(natisRegisterVehicleOwnerRegistrationInformationResponse.Body.X3141Response != null)
                    {
                        if(natisRegisterVehicleOwnerRegistrationInformationResponse.Body.X3141Response.transactionStatus == "SUCCESS")
                        {
                            return await MapXMLtoVehicleOwnerRegistrationResponse(natisRegisterVehicleOwnerRegistrationInformationResponse);
                        }else
                        {
                            _logger.LogTrace("VehicleOwnerRegistrationIntegrationService : ConvertNatisVehicleOwnerRegistrationResponseXML | Error Message Response : + " + natisRegisterVehicleOwnerRegistrationInformationResponse.ToString());
                            return await MapXMLErrorToVehicleOwnerRegistrationResponse(natisRegisterVehicleOwnerRegistrationInformationResponse);                            
                        }
                    }

                    if(natisRegisterVehicleOwnerRegistrationInformationResponse.Body != null )
                    {
                            _logger.LogTrace("Fault in XML Response Message | Response : + " + natisRegisterVehicleOwnerRegistrationInformationResponse.ToString());
                            return await MapXMLErrorToVehicleOwnerRegistrationResponse(natisRegisterVehicleOwnerRegistrationInformationResponse);    
                    }

                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + natisRegisterVehicleOwnerRegistrationInformationResponse);

                }
            }catch(Exception ex)
            {
                _logger.LogError("VehicleOwnerRegistrationIntegrationService : ConvertNatisVehicleOwnerRegistrationResponseXML | An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }

        #endregion

        #region private
                    /// <summary>
        /// Convert Vehicle Owner Registration Information Request XML to Class
        /// </summary>
        /// <param name="vehicleOwnerRegistrationRequestXML"></param>
        /// <returns></returns>
        private async Task<VehicleOwnerRegistrationInformation> MapXMLtoVehicleOwnerRegistrationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope vehicleOwnerRegistrationRequestXML)
        {
            try{

                var vehicleOwnerRegistrationResponse = new VehicleOwnerRegistrationInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehicleOwnerRegistrationRequestXML);

                baseResponse.Successful = true;

                vehicleOwnerRegistrationResponse = _mapper.Map<VehicleOwnerRegistrationInformation>(vehicleOwnerRegistrationRequestXML);

                vehicleOwnerRegistrationResponse.BaseResponse = baseResponse;

                return vehicleOwnerRegistrationResponse;

            }catch(Exception ex)
            {
                _logger.LogError("VehicleOwnerRegistrationIntegrationService : MapXMLtoVehicleOwnerRegistrationResponse | An Exception occurred when mapping MapXMLtoVehicleOwnerRegistrationResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Owner Regiatrion Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleOwnerRegistrationResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Vehicle Owner Registration Information Request XML to Class
        /// </summary>
        /// <param name="vehicleOwnerRegistrationRequestXML"></param>
        /// <returns></returns>
        private async Task<VehicleOwnerRegistrationInformation> MapXMLErrorToVehicleOwnerRegistrationResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope vehicleOwnerRegistrationRequestXML)
        {
            try{

                var vehicleOwnerRegistrationResponse = new VehicleOwnerRegistrationInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehicleOwnerRegistrationRequestXML);

                baseResponse.Successful = false;
                baseResponse.Message = vehicleOwnerRegistrationRequestXML.Body.X3141Response.messages.field??vehicleOwnerRegistrationRequestXML.Body.X3141Response.messages.message;
                baseResponse.StatusCode = vehicleOwnerRegistrationRequestXML.Body.X3141Response.messages.code;

                //Apply Code to Response Message if possible
                if(!String.IsNullOrWhiteSpace(baseResponse.StatusCode))
                {
                    try
                    {
                        var errorCode = typeof(NatisErrorList).GetField(baseResponse.StatusCode, System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Static);
                        var errorValue = (string)errorCode?.GetValue(null);
                        
                        if(!String.IsNullOrWhiteSpace(errorValue))
                        {
                            baseResponse.Message = errorValue;
                        }                        

                    }catch(Exception ex)
                    {
                        _logger.LogError("VehicleOwnerRegistrationIntegrationService : MapXMLErrorToVehicleOwnerRegistrationResponse | An Exception occurred when mapping MapXMLErrorToVehicleOwnerRegistrationResponse XML Response Were not able to Convert Error Code to Description : Exception = " + ex);   
                    }

                }

                vehicleOwnerRegistrationResponse = _mapper.Map<VehicleOwnerRegistrationInformation>(vehicleOwnerRegistrationRequestXML);

                vehicleOwnerRegistrationResponse.BaseResponse = baseResponse;

                return vehicleOwnerRegistrationResponse;

            }catch(Exception ex)
            {
                _logger.LogError("VehicleOwnerRegistrationIntegrationService : MapXMLErrorToVehicleOwnerRegistrationResponse | An Exception occurred when mapping MapXMLErrorToVehicleOwnerRegistrationResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToVehicleOwnerRegistrationResponse XML Response string to Class : Exception = " + ex);
            }
        }

        #endregion

    }
}