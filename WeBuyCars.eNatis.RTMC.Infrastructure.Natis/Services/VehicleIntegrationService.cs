using System;
using AutoMapper;
using Microsoft.Extensions.Logging;
using System.Xml.Serialization;
using System.IO;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class VehicleIntegrationService  : IVehicleIntegrationService
    {

        #region prop
        readonly ILogger<VehicleIntegrationService> _logger;
        readonly IMapper _mapper;
        readonly NatisSharedServices _natisSharedServices;
        readonly eNatisServiceOptions _serviceOptions;
        readonly INatisWRIntegrationService _natisWRIntegrationService;

        #endregion

        #region ctor
            
        public VehicleIntegrationService(
            ILogger<VehicleIntegrationService> logger,
            IMapper mapper,
            NatisSharedServices natisSharedServices,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            INatisWRIntegrationService natisWRIntegrationService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisSharedServices = natisSharedServices ?? throw new ArgumentNullException(nameof(natisSharedServices));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _natisWRIntegrationService = natisWRIntegrationService  ?? throw new ArgumentNullException(nameof(natisWRIntegrationService));
        }

        #endregion

        #region public

        /// <summary>
        /// Main Call to RTMC Get Vehicle Web Request
        /// </summary>
        /// <param name="vehicleRequest"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        public async Task<VehicleInformation> GetVehicleQuery(Guid auditLogId, NatisGetVehicleRequest vehicleRequest, string environmentName)
        {
            
            VehicleInformation result = new VehicleInformation();

            // if(environmentName != "Production")
            // {
            //     //Check if the End Point contains a Prod in the URL
            //     if(_serviceOptions.BaseUrl.Contains("prod"))
            //     {
            //         result.BaseResponse = new BaseResponse()
            //         {
            //             Successful = false,
            //             Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
            //         };
            //         return result;
            //     }
            // }    

            try
            {
                
                WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope envelope = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = _serviceOptions.PayloadUsername,
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = _serviceOptions.PayloadPassword
                                }
                            }
                        }
                    }
                    , Body = new WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope.Body()
                    {
                        X3003Request = new Models.Shared.Envelope.X3003Request()
                        {
                            RegisterNumber = vehicleRequest.RegisterNumber,
                            VinOrChassis = vehicleRequest.VinOrChassis,
                            LicenceNumber = vehicleRequest.LicenceNumber,
                            EngineNumber = vehicleRequest.EngineNumber,
                        }
                    }           
                };

                //Call Integration Method
                var response = await _natisWRIntegrationService.RTMCIntegration(auditLogId, envelope);

                //Convert Response Object to Class
                result = await ConvertNatisVehicleResponseXML(response);

            }catch(Exception ex)
            {

                _logger.LogError("GUID : " + auditLogId + " : VehicleIntegrationService : GetVehicleQuery | An Exception occurred when integrating to RTMC: Exception = " + ex);

                result.BaseResponse = new BaseResponse()
                {
                    Successful = false,
                    Message = "The Development Environment is Pointing to Production for a Sensitive Service!"
                };
            }

            return result;

        }

        /// <summary>
        /// Convert Vehicle Request to XML String used for Enatis request
        /// </summary>
        /// <param name="vehicleRequest"></param>
        /// <returns></returns>
        public string ConvertNatisVehicleRequestXML(NatisGetVehicleRequest vehicleRequest){

            try
            {

                var result = @"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:sch=""http://tasima/common/ws/schema/"">
        <soapenv:Header>
        <wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">                             
        <wsse:UsernameToken xmlns:wsu=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd"">                             
        <wsse:Username>{0}</wsse:Username>
        <wsse:Password Type=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText"">{1}</wsse:Password>
        </wsse:UsernameToken>
        </wsse:Security>
    </soapenv:Header>
    <soapenv:Body>
        <sch:X3003Request>";

                if(vehicleRequest.RegisterNumber != null)
                {
                    result = result + $"<sch:RegisterNumber>{vehicleRequest.RegisterNumber}</sch:RegisterNumber>";
                }

                if(vehicleRequest.VinOrChassis != null)
                {
                    result = result + $"<sch:VinOrChassis>{vehicleRequest.VinOrChassis}</sch:VinOrChassis>";
                }

                if(vehicleRequest.EngineNumber != null)
                {
                    result = result + $"<sch:EngineNumber>{vehicleRequest.EngineNumber}</sch:EngineNumber>";
                }

                if(vehicleRequest.LicenceNumber != null)
                {
                    result = result + $"<sch:LicenceNumber>{vehicleRequest.LicenceNumber}</sch:LicenceNumber>";
                }

                //Apply Trailing Record Information
                result = result +  "</sch:X3003Request>" +
                "</soapenv:Body>" +
                "</soapenv:Envelope>";


                _logger.LogWarning("Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Result before Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                //Apply Credentials
                result = string.Format(result, _serviceOptions.PayloadUsername, _serviceOptions.PayloadPassword);

                _logger.LogTrace("Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Result after Credentials : " + _natisSharedServices.MaskXMLPassword(result));

                return result;

            }catch(Exception ex)
            {
                _logger.LogError("Error : Service : NatisIntegrationService | Method : ConvertNatisVehicleRequestXML | Problem creating XML Payload Exception : " + ex.ToString());
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }


        }

        /// <summary>
        /// Convert Vehicle Response from string to Object
        /// </summary>
        /// <param name="natisVehicleResponse"></param>
        /// <returns></returns>/
        public async Task<VehicleInformation> ConvertNatisVehicleResponseXML(string natisVehicleResponse){

            try{

                XmlSerializer serializer = new XmlSerializer(typeof(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle.Envelope));
                using (StringReader reader = new StringReader(natisVehicleResponse))
                {
                    var natisGetVehicleResponse = (WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle.Envelope )serializer.Deserialize(reader);

                    if(natisGetVehicleResponse.Body.X3003Response != null)
                    {
                        if(natisGetVehicleResponse.Body.X3003Response.executionResult.successful == true)
                        {
                            return await MapXMLtoVehicleResponse(natisGetVehicleResponse);
                        }else
                        {
                            _logger.LogTrace("Error XML Response Message | Response : + " + JsonConvert.SerializeObject(natisVehicleResponse,Formatting.Indented));
                            return await MapXMLErrorToVehicleResponse(natisGetVehicleResponse);                            
                        }
                    }

                    if(natisGetVehicleResponse.Body.Fault != null )
                    {
                            _logger.LogTrace("Fault in XML Response Message | Response : + " + JsonConvert.SerializeObject(natisVehicleResponse,Formatting.Indented));
                            return await MapXMLErrorToVehicleResponse(natisGetVehicleResponse);    
                    }

                    throw new DomainException("Error when Converting XML Response string to Class | Natis Response : " + JsonConvert.SerializeObject(natisVehicleResponse,Formatting.Indented));

                }
            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred when Converting XML Response string to Class : Exception = " + ex);
            }            
        }

        #endregion

        #region private

        /// <summary>
        /// Convert Vehicle XML to Class
        /// </summary>
        /// <param name="vehiclerequestxml"></param>
        /// <returns></returns>
        private async Task<VehicleInformation> MapXMLtoVehicleResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle.Envelope  vehiclerequestxml)
        {
            try{

                var vehicleDetailResponse = new VehicleInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehiclerequestxml);

                baseResponse.Successful = true;

                vehicleDetailResponse = _mapper.Map<VehicleInformation>(vehiclerequestxml);

                vehicleDetailResponse.BaseResponse = baseResponse;
                vehicleDetailResponse.BaseResponse.Successful = true;

                return vehicleDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }

        /// <summary>
        /// Convert Vehicle XML to Class
        /// </summary>
        /// <param name="vehiclerequestxml"></param>
        /// <returns></returns>
        private async Task<VehicleInformation> MapXMLErrorToVehicleResponse(WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle.Envelope  vehiclerequestxml)
        {
            try{

                var vehicleDetailResponse = new VehicleInformation();

                var baseResponse = _mapper.Map<BaseResponse>(vehiclerequestxml);
                
                baseResponse.Successful = false;

                vehicleDetailResponse = _mapper.Map<VehicleInformation>(vehiclerequestxml);

                vehicleDetailResponse.BaseResponse = baseResponse;
                vehicleDetailResponse.BaseResponse.Successful = false;

                return vehicleDetailResponse;

            }catch(Exception ex)
            {
                _logger.LogError("An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLErrorToVehicleResponse XML Response string to Class : Exception = " + ex);
            }
        }

        #endregion
        
    }

}