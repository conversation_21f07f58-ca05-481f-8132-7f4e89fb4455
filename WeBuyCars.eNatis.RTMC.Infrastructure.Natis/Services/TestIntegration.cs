using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using System.Reflection;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using System.Net.Http;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnerTitleHolderTransfer;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory;
using Newtonsoft.Json;
using System.Net.Http.Headers;
using System.Text;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using System.Net;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.ControlNumber;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisCalculateVehicleLicenceFeeRequest;
using System.Collections.Generic;
using System.Linq;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using System.Xml.Serialization;
using System.Security.Cryptography.X509Certificates;
using System.Net.Security;
using System.Xml;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services
{
    public class TestIntegration : ITestIntegration
    {
        #region prop

        readonly eNatisServiceOptions _serviceOptions;
        readonly eNatisEndPointOptions _endPointOptions;
        readonly ILogger<NatisIntegrationService> _logger;
        private readonly HttpClient _httpClient;
        readonly IDriverIntegrationService _driverIntegrationService;
        readonly IVehicleIntegrationService _vehicleIntegrationService;
        readonly IVehicleDetailIntegrationService _vehicleDetailIntegrationService;
        readonly IVehicleOwnerVerificationIntegrationService _vehicleOwnerVerificationIntegrationService;
        readonly IVehicleOwnerRegistrationIntegrationService _vehicleOwnerRegistrationIntegrationService;
        readonly IOnlineNCOIntegrationService _onlineNCOIntegrationService;

        #endregion

        #region ctor

        public TestIntegration(
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            IOptionsMonitor<eNatisEndPointOptions> endpointOptions,
            ILogger<NatisIntegrationService> logger,
            HttpClient httpClient,
            IDriverIntegrationService driverIntegrationService,
            IVehicleIntegrationService vehicleIntegrationService,
            IVehicleDetailIntegrationService vehicleDetailIntegrationService,
            IVehicleOwnerVerificationIntegrationService vehicleOwnerVerificationIntegrationService,
            IVehicleOwnerRegistrationIntegrationService vehicleOwnerRegistrationIntegrationService,
            IOnlineNCOIntegrationService onlineNCOIntegrationService
            )
        {

            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _endPointOptions = endpointOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(endpointOptions));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _driverIntegrationService = driverIntegrationService ?? throw new ArgumentNullException(nameof(driverIntegrationService));
            _vehicleIntegrationService = vehicleIntegrationService ?? throw new ArgumentNullException(nameof(vehicleIntegrationService));
            _vehicleDetailIntegrationService = vehicleDetailIntegrationService ?? throw new ArgumentNullException(nameof(vehicleDetailIntegrationService));
            _vehicleOwnerVerificationIntegrationService = vehicleOwnerVerificationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerVerificationIntegrationService));
            _vehicleOwnerRegistrationIntegrationService = vehicleOwnerRegistrationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerRegistrationIntegrationService));
            _onlineNCOIntegrationService = onlineNCOIntegrationService ?? throw new ArgumentNullException(nameof(onlineNCOIntegrationService));

        }

        #endregion

        #region public

        [XmlRoot(ElementName = "Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class Envelope
        {
            [XmlElement(ElementName = "Header", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
            public Header Header { get; set; }

            [XmlElement(ElementName = "Body", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
            public Body Body { get; set; }
        }

        public class Header
        {
            [XmlElement(ElementName = "Security", Namespace = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd")]
            public Security Security { get; set; }
        }

        public class Security
        {
            [XmlElement(ElementName = "UsernameToken", Namespace = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd")]
            public UsernameToken UsernameToken { get; set; }
        }

        public class UsernameToken
        {
            [XmlElement(ElementName = "Username")]
            public string Username { get; set; }

            [XmlElement(ElementName = "Password")]
            public Password Password { get; set; }
        }

        public class Password
        {
            [XmlAttribute(AttributeName = "Type")]
            public string Type { get; set; }

            [XmlText]
            public string Value { get; set; }
        }

        [XmlRoot(ElementName = "Body", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class Body
        {
            [XmlElement(ElementName = "X3003Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3003Request X3003Request { get; set; }
        }

        public class X3003Request
        {
            [XmlElement(ElementName = "RegisterNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string RegisterNumber { get; set; }
        }


        public Task<VehicleInformation> GetVehicleQuery(NatisGetVehicleRequest vehicleRequest)
        {
            
            VehicleInformation result = new VehicleInformation();

            try
            {

                ServicePointManager.ServerCertificateValidationCallback += ValidateCertificate;

                // Create the envelope object with data
                Envelope envelope = new Envelope()
                {
                    Header = new Header()
                    {
                        Security = new Security()
                        {
                            UsernameToken = new UsernameToken()
                            {
                                Username = "4988A001",
                                Password = new Password()
                                {
                                    Type = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText",
                                    Value = "TESTER01"
                                }
                            }
                        }
                    },
                    Body = new Body()
                    {
                        X3003Request = new X3003Request()
                        {
                            RegisterNumber = "BBB044F"
                        }
                    }
                };

                // Serialize the envelope object to XML
                XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
                StringWriter stringWriter = new StringWriter();
                XmlSerializerNamespaces namespaces = new XmlSerializerNamespaces();
                namespaces.Add("soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
                namespaces.Add("sch", "http://tasima/common/ws/schema/");
                namespaces.Add("wsse", "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd");
                serializer.Serialize(stringWriter, envelope, namespaces);

                string xmlString = stringWriter.ToString();
                stringWriter.Close();

                // Modify the XML string to move the namespace declaration
                // xmlString = xmlString.Replace("<wsse:Security ", "<wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" ");
                // xmlString = xmlString.Replace("<wsse:UsernameToken ", "<wsse:UsernameToken xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\" ");

                // Remove the namespace declarations from the Envelope element
                xmlString = xmlString.Replace(" xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"", "");
                xmlString = xmlString.Replace(" xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\"", "");
                xmlString = xmlString.Replace("<?xml version=\"1.0\" encoding=\"utf-16\"?>", "");
                xmlString = xmlString.Replace("<wsse:UsernameToken>", "<wsse:UsernameToken xmlns:wsu=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd\">");
                xmlString = xmlString.Replace("<wsse:Security", "<wsse:Security xmlns:wsse=\"http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd\"");

                // Prepare the web request
                string webAddress = "https://iftst.enatis.co.za:4443/enatis/ws";
                HttpWebRequest request = (HttpWebRequest)WebRequest.Create(webAddress);
                request.Method = "POST";
                request.ContentType = "text/xml;charset=UTF-8";


                byte[] requestData = Encoding.UTF8.GetBytes(xmlString);
                request.ContentLength = requestData.Length;
                Stream requestStream = request.GetRequestStream();
                requestStream.Write(requestData, 0, requestData.Length);
                requestStream.Close();

                // Send the request and get the response
                ServicePointManager.Expect100Continue = true;
                ServicePointManager.SecurityProtocol = SecurityProtocolType.Tls12;
                HttpWebResponse response = (HttpWebResponse)request.GetResponse();
                Stream responseStream = response.GetResponseStream();
                StreamReader reader = new StreamReader(responseStream);
                string responseXml = reader.ReadToEnd();
                reader.Close();
                responseStream.Close();
                response.Close();

                // Process the response as needed
                Console.WriteLine(responseXml);

            }catch(Exception ex)
            {
                throw;                        
            }

            return Task.FromResult(result);

        }

        // SSL certificate validation callback method
        private static bool ValidateCertificate(object sender, X509Certificate certificate, X509Chain chain, SslPolicyErrors sslPolicyErrors)
        {
            // Ignore SSL certificate errors
            return true;
        }


                // Move namespace declaration from Envelope to specified element in the XML string
        private static string MoveNamespaceDeclaration(string xmlString, string elementName)
        {
            // Load the XML string into an XmlDocument
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xmlString);

            // Find the specified element
            XmlNamespaceManager namespaceManager = new XmlNamespaceManager(xmlDoc.NameTable);
            namespaceManager.AddNamespace("soapenv", "http://schemas.xmlsoap.org/soap/envelope/");
            namespaceManager.AddNamespace("wsse", "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd");
            namespaceManager.AddNamespace("wsu", "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd");
            XmlNode elementNode = xmlDoc.SelectSingleNode($"//soapenv:Envelope/soapenv:Header/{elementName}", namespaceManager);
            if (elementNode == null)
            {
                throw new ArgumentException("Element not found in the XML.", nameof(elementName));
            }

            // Move the namespace declaration from Envelope to the specified element
            XmlAttribute xmlnsAttribute = xmlDoc.DocumentElement.Attributes[elementName.StartsWith("wsse:") ? "xmlns:wsse" : "xmlns:wsu"];
            if (xmlnsAttribute != null)
            {
                elementNode.Attributes.Append(xmlDoc.CreateAttribute(elementName.StartsWith("wsse:") ? "xmlns:wsse" : "xmlns:wsu")).Value = xmlnsAttribute.Value;
                xmlDoc.DocumentElement.Attributes.Remove(xmlnsAttribute);
            }

            // Get the modified XML string
            StringBuilder sb = new StringBuilder();
            XmlWriterSettings settings = new XmlWriterSettings
            {
                OmitXmlDeclaration = true,
                Indent = true
            };

            using (XmlWriter writer = XmlWriter.Create(sb, settings))
            {
                xmlDoc.WriteTo(writer);
            }

            return sb.ToString();
        }



        #endregion

    }
}
