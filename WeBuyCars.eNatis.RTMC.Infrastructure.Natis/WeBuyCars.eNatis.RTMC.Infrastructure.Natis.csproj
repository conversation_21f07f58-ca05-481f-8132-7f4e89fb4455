<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <LangVersion>10.0</LangVersion>
    <TargetFramework>net8.0</TargetFramework>
    <!-- <TargetFramework>netcoreapp6.0</TargetFramework> -->
    <RootNamespace>WeBuyCars.eNatis.RTMC.Infrastructure.Natis</RootNamespace>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <noWarn>1591</noWarn>
  </PropertyGroup>


  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
    <PackageReference Include="System.ServiceModel.Http" Version="4.10.0" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="4.10.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.1" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="6.22.0" />
    <PackageReference Include="IdentityModel" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.1" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.17.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="11.0.0" />
    
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeBuyCars.eNatis.RTMC.Core\WeBuyCars.eNatis.RTMC.Core.csproj" />
  </ItemGroup>

</Project>
