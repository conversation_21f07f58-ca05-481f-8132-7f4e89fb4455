using AutoMapper;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Mappers
{

    public class OnlineNCOIntegrationMapper : Profile
    {

        public OnlineNCOIntegrationMapper()
        {
      
            CreateMap<Infrastructure.Natis.Models.OnlineNCO.Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X314AResponse.transactionStatus))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X314AResponse.transactionStatus))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X314AResponse.messages))
                .ReverseMap();

            CreateMap<Infrastructure.Natis.Models.OnlineNCO.Envelope, OnlineNCOInformation>()
                .ForMember(dest => dest.RegAuthority, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.RegAuthority))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.RegisterNumber))
                .ForMember(dest => dest.VehicleCertificateNumber, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.VehicleCertificateNumber))
                .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.VinOrChassis))
                .ForMember(dest => dest.EngineNumber, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.EngineNumber))
                .ForMember(dest => dest.Make, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Make))
                .ForMember(dest => dest.ModelName, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.ModelName))
                .ForMember(dest => dest.VehicleCategory, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.VehicleCategory))
                .ForMember(dest => dest.Driven, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Driven))
                .ForMember(dest => dest.VehicleDescription, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.VehicleDescription))
                .ForMember(dest => dest.Tare, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Tare))
                .ForMember(dest => dest.FirstLicenceLiabilityDate, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.FirstLicenceLiabiltyDate))
                .ForMember(dest => dest.VehicleLifeStatus, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.VehicleLifeStatus))

                .ForMember(dest => dest.SellerIdDocumentType, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Seller.IdDocumentType))
                .ForMember(dest => dest.SellerIdDocumentNumber, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Seller.IdDocumentNumber))
                .ForMember(dest => dest.SellerCountryOfIssue, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Seller.CountryOfIssue))
                .ForMember(dest => dest.SellerName, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Seller.Name))

                .ForMember(dest => dest.PurchaserIdDocumentType, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Purchaser.IdDocumentType))
                .ForMember(dest => dest.PurchaserIdDocumentNumber, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Purchaser.IdDocumentNumber))
                .ForMember(dest => dest.PurchaserCountryOfIssue, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Purchaser.CountryOfIssue))
                .ForMember(dest => dest.PurchaserName, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Purchaser.Name))

                .ForMember(dest => dest.FirstRegistrationLiabilityDate, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.FirstRegistrationLiabiltyDate))
                .ForMember(dest => dest.IssueDate, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.IssueDate))
                .ForMember(dest => dest.IssuedBy, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.IssuedBy))
                .ForMember(dest => dest.Barcode, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Barcode))
                .ForMember(dest => dest.Watermark, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.Watermark))
                .ForMember(dest => dest.DateTime, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.DateTime))
                .ForMember(dest => dest.UserGroupCode, opt => opt.MapFrom(src => src.Body.X314AResponse.result.RC2.UserGroupCode))

                .ReverseMap();

        }

    }
}