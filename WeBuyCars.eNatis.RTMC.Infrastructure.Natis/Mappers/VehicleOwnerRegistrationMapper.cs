using AutoMapper;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Mappers
{

    public class VehicleOwnerRegistrationMapper : Profile
    {

        public VehicleOwnerRegistrationMapper()
        {
      
            CreateMap<Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3141Response.transactionStatus))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3141Response.transactionStatus))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3141Response.transactionStatus))
                .ReverseMap();

            CreateMap<Infrastructure.Natis.Models.VehicleOwnerRegistration.Envelope, VehicleOwnerRegistrationInformation>()
                .ForMember(dest => dest.ConvenienceFeeAmount, opt => opt.MapFrom(src => src.Body.X3141Response.result.ConvenienceFeeAmount))
                .ForMember(dest => dest.PaymentReferenceNumber, opt => opt.MapFrom(src => src.Body.X3141Response.result.PaymentReferenceNumber))
                .ForMember(dest => dest.RegistrationFeeAmount, opt => opt.MapFrom(src => src.Body.X3141Response.result.RegistrationFeeAmount))
                .ForMember(dest => dest.TitleHolderIdDocumentType, opt => opt.MapFrom(src => src.Body.X3141Response.result.TitleHolder.IdDocumentType))
                .ForMember(dest => dest.TitleHolderIdDocumentNumber, opt => opt.MapFrom(src => src.Body.X3141Response.result.TitleHolder.IdDocumentNumber))
                .ForMember(dest => dest.TotalRegistrationFeeAmount, opt => opt.MapFrom(src => src.Body.X3141Response.result.TotalRegistrationFeeAmount))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.Body.X3141Response.result.VehicleParticulars.RegisterNumber))
                .ForMember(dest => dest.VehicleCertificateNumber, opt => opt.MapFrom(src => src.Body.X3141Response.result.VehicleParticulars.VehicleCertificateNumber))
                .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.Body.X3141Response.result.VehicleParticulars.VinOrChassis))
                .ForMember(dest => dest.OwnerIdDocumentNumber, opt => opt.MapFrom(src => src.Body.X3141Response.result.Owner.IdDocumentNumber))
                .ForMember(dest => dest.OwnerIdDocumentType, opt => opt.MapFrom(src => src.Body.X3141Response.result.Owner.IdDocumentType))
                .ReverseMap();

        }

    }
}