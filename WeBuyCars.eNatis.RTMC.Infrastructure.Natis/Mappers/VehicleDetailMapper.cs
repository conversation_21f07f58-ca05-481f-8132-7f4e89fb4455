using AutoMapper;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Mappers
{

    public class VehicleDetailMapper : Profile
    {

        public VehicleDetailMapper()
        {
            CreateMap<RTMCResponse, VehicleInformation>().ReverseMap();
            CreateMap<RTMCVehicleDetail, VehicleInformation>().ReverseMap();      

            CreateMap<Infrastructure.Natis.Models.GetVehicle.Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3003Response.executionResult.errorMessages.code))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3003Response.executionResult.successful))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3003Response.executionResult.errorMessages.message))
                .ReverseMap();


            CreateMap<Infrastructure.Natis.Models.GetVehicle.Envelope, VehicleInformation>()
                .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.LicenceNumber))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RegisterNumber))
                .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.VinOrChassis))
                .ForMember(dest => dest.EngineNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.EngineNumber))
                .ForMember(dest => dest.MakeCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Make.code))
                .ForMember(dest => dest.MakeDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Make.description))
                .ForMember(dest => dest.ModelNameCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.ModelName.code))
                .ForMember(dest => dest.ModelNameDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.ModelName.description))
                .ForMember(dest => dest.CategoryCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Category.code))
                .ForMember(dest => dest.CategoryDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Category.description))
                .ForMember(dest => dest.DrivenCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Driven.code))
                .ForMember(dest => dest.DrivenDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Driven.description))
                .ForMember(dest => dest.NetPower, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.NetPower))
                .ForMember(dest => dest.DescriptionCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Description.code))
                .ForMember(dest => dest.DescriptionDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Description.description))
                .ForMember(dest => dest.EngineDisplacement, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.EngineDisplacement))
                .ForMember(dest => dest.FuelTypeCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.FuelType.code))
                .ForMember(dest => dest.FuelTypeDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.FuelType.description))
                .ForMember(dest => dest.GVM, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.GVM))
                .ForMember(dest => dest.AxlesTotal, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.AxlesTotal))
                .ForMember(dest => dest.NoOfWheels, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.NoOfWheels))
                .ForMember(dest => dest.OverallWidth, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.OverallWidth))
                .ForMember(dest => dest.RoadworthyStatusCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RoadworthyStatus.code))
                .ForMember(dest => dest.RoadworthyStatusDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RoadworthyStatus.description))
                .ForMember(dest => dest.Tare, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.Tare))
                .ForMember(dest => dest.MainColourCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.MainColour.code))
                .ForMember(dest => dest.MainColourDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.MainColour.description))
                .ForMember(dest => dest.AxlesDriven, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.AxlesDriven))
                .ForMember(dest => dest.OverallLength, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.OverallLength))
                .ForMember(dest => dest.OverallHeight, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.OverallHeight))
                .ForMember(dest => dest.RoadworthyStatusDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RoadworthyStatusDate))
                .ForMember(dest => dest.RoadworthyTestDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RoadworthyTestDate))
                .ForMember(dest => dest.SapMarkCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapMark.code))
                .ForMember(dest => dest.SapMarkDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapMark.description))
                .ForMember(dest => dest.SapMarkDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapMarkDate))
                .ForMember(dest => dest.SapClearanceStatusCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapClearanceStatus.code))
                .ForMember(dest => dest.SapClearanceStatusDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapClearanceStatus.description))
                .ForMember(dest => dest.SapClearanceDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.SapClearanceDate))
                .ForMember(dest => dest.LifeStatusCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.LifeStatus.code))
                .ForMember(dest => dest.LifeStatusDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.LifeStatus.description))
                .ForMember(dest => dest.VehicleStateCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.VehicleState.code))
                .ForMember(dest => dest.VehicleStateDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.VehicleState.description))
                .ForMember(dest => dest.VehicleStateDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.VehicleStateDate))
                .ForMember(dest => dest.RegistrationTypeCode, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RegistrationType.code))
                .ForMember(dest => dest.RegistrationTypeDescription, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.RegistrationType.description))
                .ForMember(dest => dest.VehicleCertificateNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.VehicleCertificateNumber))
                .ForMember(dest => dest.LicenceChangeDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.LicenceChangeDate))
                .ForMember(dest => dest.LicenceExpiryDate, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.LicenceExpiryDate))
                .ForMember(dest => dest.PreviousLicenceNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.PreviousLicenceNumber))
                .ForMember(dest => dest.PrePreviousLicenceNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.PrePreviousLicenceNumber))
                .ForMember(dest => dest.PrePrePreviousLicenceNumber, opt => opt.MapFrom(src => src.Body.X3003Response.response.Vehicle.PrePrePreviousLicenceNumber))       
                .ReverseMap();

            CreateMap<RTMCResponse, VehicleDetailInformation>().ReverseMap();
            CreateMap<RTMCVehicleDetail, VehicleDetailInformation>().ReverseMap();      

            CreateMap<Infrastructure.Natis.Models.GetVehicleDetailed.Envelope, VehicleDetailInformation>()
                .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LicenceNumber))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegisterNumber))
                .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VinOrChassis))
                .ForMember(dest => dest.EngineNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.EngineNumber))
                .ForMember(dest => dest.MakeCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Make.code))
                .ForMember(dest => dest.MakeDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Make.description))
                .ForMember(dest => dest.ModelNameCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.ModelName.code))
                .ForMember(dest => dest.ModelNameDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.ModelName.description))
                .ForMember(dest => dest.CategoryCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Category.code))
                .ForMember(dest => dest.CategoryDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Category.description))
                .ForMember(dest => dest.DrivenCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Driven.code))
                .ForMember(dest => dest.DrivenDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Driven.description))
                .ForMember(dest => dest.NetPower, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.NetPower))
                .ForMember(dest => dest.DescriptionCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Description.code))
                .ForMember(dest => dest.DescriptionDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Description.description))
                .ForMember(dest => dest.EngineDisplacement, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.EngineDisplacement))
                .ForMember(dest => dest.FuelTypeCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.FuelType.code))
                .ForMember(dest => dest.FuelTypeDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.FuelType.description))
                .ForMember(dest => dest.GVM, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.GVM))
                .ForMember(dest => dest.AxlesTotal, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.AxlesTotal))
                .ForMember(dest => dest.NoOfWheels, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.NoOfWheels))
                .ForMember(dest => dest.OverallWidth, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.OverallWidth))
                .ForMember(dest => dest.RoadworthyStatusCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RoadworthyStatus.code))
                .ForMember(dest => dest.RoadworthyStatusDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RoadworthyStatus.description))
                .ForMember(dest => dest.Tare, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Tare))
                .ForMember(dest => dest.MainColourCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.MainColour.code))
                .ForMember(dest => dest.MainColourDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.MainColour.description))
                .ForMember(dest => dest.AxlesDriven, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.AxlesDriven))
                .ForMember(dest => dest.OverallLength, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.OverallLength))
                .ForMember(dest => dest.OverallHeight, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.OverallHeight))
                .ForMember(dest => dest.RoadworthyStatusDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RoadworthyStatusDate))
                .ForMember(dest => dest.RoadworthyTestDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RoadworthyTestDate))
                .ForMember(dest => dest.SapMarkCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapMark.code))
                .ForMember(dest => dest.SapMarkDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapMark.description))
                .ForMember(dest => dest.SapMarkDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapMarkDate))
                .ForMember(dest => dest.SapClearanceStatusCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapClearanceStatus.code))
                .ForMember(dest => dest.SapClearanceStatusDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapClearanceStatus.description))
                .ForMember(dest => dest.SapClearanceDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapClearanceDate))
                .ForMember(dest => dest.LifeStatusCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LifeStatus.code))
                .ForMember(dest => dest.LifeStatusDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LifeStatus.description))
                .ForMember(dest => dest.VehicleStateCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VehicleState.code))
                .ForMember(dest => dest.VehicleStateDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VehicleState.description))
                .ForMember(dest => dest.VehicleStateDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VehicleStateDate))
                .ForMember(dest => dest.RegistrationTypeCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationType.code))
                .ForMember(dest => dest.RegistrationTypeDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationType.description))
                .ForMember(dest => dest.VehicleCertificateNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CertificateNumber))
                .ForMember(dest => dest.LicenceChangeDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LicenceChangeDate))
                .ForMember(dest => dest.LicenceExpiryDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LicenceExpiryDate))
                .ForMember(dest => dest.PreviousLicenceNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.PreviousLicenceNumber))
                .ForMember(dest => dest.PrePreviousLicenceNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.PrePreviousLicenceNumber))
                //.ForMember(dest => dest.LicenceLiabilityDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LicenceLiabilityDate))

                .ForMember(dest => dest.RegAuthorityOfLicensingCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegAuthorityOfLicensing.authorityCode))
                .ForMember(dest => dest.RegAuthorityOfLicensingName, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegAuthorityOfLicensing.authorityName))
                .ForMember(dest => dest.RegAuthorityOfLicenceNumberCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegAuthorityOfLicenceNumber.authorityCode))
                .ForMember(dest => dest.RegAuthorityOfLicenceNumberName, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegAuthorityOfLicenceNumber.authorityName))

                .ForMember(dest => dest.RegistrationDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationDate))
                .ForMember(dest => dest.RegistrationQualifierCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationQualifier.code))
                .ForMember(dest => dest.RegistrationQualifierDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationQualifier.description))
                .ForMember(dest => dest.RegistrationQualifierDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationQualifierDate))
                .ForMember(dest => dest.DataOwnerCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.DataOwner.code))
                .ForMember(dest => dest.DataOwnerDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.DataOwner.description))
                .ForMember(dest => dest.Timestamp, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Timestamp))
                .ForMember(dest => dest.TransmissionCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Transmission.code))
                .ForMember(dest => dest.TransmissionDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Transmission.description))
                .ForMember(dest => dest.GearboxNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.GearboxNumber))
                .ForMember(dest => dest.DifferentialNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.DifferentialNumber))
                .ForMember(dest => dest.FirstLicensingDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.FirstLicensingDate))
                //.ForMember(dest => dest.CountryOfExportCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CountryOfExport.code))
                //.ForMember(dest => dest.CountryOfExportDescripiton, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CountryOfExport.description))
                .ForMember(dest => dest.CountryOfImportCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CountryOfImport.code))
                .ForMember(dest => dest.CountryOfImportDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CountryOfImport.description))
                .ForMember(dest => dest.ModelNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.ModelNumber))
                .ForMember(dest => dest.SapClearanceReasonCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapClearanceReason.code))
                .ForMember(dest => dest.SapClearanceReasonDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.SapClearanceReason.description))
                .ForMember(dest => dest.VehicleUsageCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VehicleUsage.code))
                .ForMember(dest => dest.VehicleUsageDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VehicleUsage.description))
                .ForMember(dest => dest.EconomicSectorCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.EconomicSector.code))
                .ForMember(dest => dest.EconomicSectorDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.EconomicSector.description))
                .ForMember(dest => dest.PreviousVehicleCertificateNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.PreviousCertificateNumber))
                .ForMember(dest => dest.CapacitySitting, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CapacitySitting))
                .ForMember(dest => dest.CapacityStanding, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.CapacityStanding))
                .ForMember(dest => dest.LicenceFee, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.LicenceFee))
                .ForMember(dest => dest.VtsNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VtsNumber))
                .ForMember(dest => dest.VtsName, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.VtsName))
                .ForMember(dest => dest.ExaminerNumber, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.ExaminerNumber))
                .ForMember(dest => dest.ExaminerName, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.ExaminerName))
                .ForMember(dest => dest.ExemptionCode, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Exemption.code))
                .ForMember(dest => dest.ExemptionDescription, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Exemption.description))
                .ForMember(dest => dest.RoadUseIndicator, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RoadUseIndicator))
                .ForMember(dest => dest.Overdue, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.Overdue))

                .ForMember(dest => dest.FirstRegistrationDate, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.FirstRegistrationDate))
                .ForMember(dest => dest.AdministrationMarkIndicator, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.AdministrationMarkIndicator))
                .ForMember(dest => dest.RegistrationAllowed, opt => opt.MapFrom(src => src.Body.X3067Response.responseDetail.Vehicle.RegistrationAllowed))
                
                .ReverseMap();

            CreateMap<Infrastructure.Natis.Models.GetVehicleDetailed.Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3067Response.executionResult.errorMessages.code))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3067Response.executionResult.successful))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3067Response.executionResult.errorMessages.message))
                .ReverseMap();

        }

    }
}