using AutoMapper;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Mappers
{


    public class DriverInformationMapper : Profile
    {

        public DriverInformationMapper()
        {
            CreateMap<RTMCResponse, DriverInformation>().ReverseMap();
            CreateMap<RTMCDriverInformationDetail, DriverInformation>().ReverseMap();

            CreateMap<Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.errorMessages.code))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.successful))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.errorMessages.message))
                .ReverseMap();

            CreateMap<Envelope, DriverInformation>()
                .ForMember(dest => dest.DrivingLicenceNumber, opt => opt.MapFrom(src => src.Body.X3042Response.response.licenceCard.drivingLicenceNumber))
                .ForMember(dest => dest.DrivingLicencedateOfFirstIssue, opt => opt.MapFrom(src => src.Body.X3042Response.response.licenceCard.dateOfFirstIssue))
                .ForMember(dest => dest.LicenceCardcardIssueNumber, opt => opt.MapFrom(src => src.Body.X3042Response.response.licenceCard.cardIssueNumber))
                .ForMember(dest => dest.LicenceCardvalidTo, opt => opt.MapFrom(src => src.Body.X3042Response.response.licenceCard.validTo))

                .ForMember(dest => dest.IdDocumentTypeCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.idDocumentType.code))
                .ForMember(dest => dest.IdDocumentTypeDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.idDocumentType.description))

                .ForMember(dest => dest.IdDocumentNumber, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.idDocumentNumber))
                .ForMember(dest => dest.Initials, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.initials))
                .ForMember(dest => dest.BusinessOrSurname, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.businessOrSurname))
                .ForMember(dest => dest.BirthDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.birthDate))
                .ForMember(dest => dest.Age, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.age))
                .ForMember(dest => dest.NatureOfPersonCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.natureOfPersonCode.code))
                .ForMember(dest => dest.NatureOfPersonDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.natureOfPersonCode.description))
                .ForMember(dest => dest.PopulationGroupCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.populationGroupCode.code))
                .ForMember(dest => dest.PopulationGroupDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.populationGroupCode.description))
                .ForMember(dest => dest.LicenceRestrictionCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.licenceRestriction.code))
                .ForMember(dest => dest.LicenceRestrictionDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.person.licenceRestriction.description))

                .ForMember(dest => dest.DrivingLicencedateOfFirstIssue, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.dateOfFirstIssue))
                .ForMember(dest => dest.DrivingLicenceTypeCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.drivingLicenceType.code))
                .ForMember(dest => dest.DrivingLicenceTypeDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.drivingLicenceType.description))
                .ForMember(dest => dest.LicenceAuthorisationDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.licenceAuthorisationDate))
                .ForMember(dest => dest.DrivingLicenceValidFrom, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.validFrom))
                .ForMember(dest => dest.VehicleRestrictionCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.vehicleRestriction.code))
                .ForMember(dest => dest.VehicleRestrictionDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.drivingLicence.vehicleRestriction.description))

                .ForMember(dest => dest.LearnerCertificateNumber, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.learnerCertificateNumber))
                .ForMember(dest => dest.LearnerLicenceTypeCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.learnerLicenceType.code))
                .ForMember(dest => dest.LearnerLicenceTypeDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.learnerLicenceType.description))
                .ForMember(dest => dest.LearnerLicenceStatusCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.learnerLicenceStatus.code))
                .ForMember(dest => dest.LearnerLicenceStatusDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.learnerLicenceStatus.description))
                .ForMember(dest => dest.LearnerLicenceValidFrom, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.validFrom))
                .ForMember(dest => dest.LearnerLicenceSpeciallyAdaptedVehicleRequired, opt => opt.MapFrom(src => src.Body.X3042Response.response.learnerLicence.speciallyAdaptedVehicleRequired))

                /* Still need to Update Map and Create Natis Model */
                .ForMember(dest => dest.ProfessionalDateAuthorised, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.dateAuthorised))
                .ForMember(dest => dest.ProfessionalDangerousGoodsCategoryCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.dangerousGoodsCategory.code))
                .ForMember(dest => dest.ProfessionalDangerousGoodsCategoryDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.dangerousGoodsCategory.description))
                .ForMember(dest => dest.ProfessionalGoodsCategoryCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.goodsCategory.code))
                .ForMember(dest => dest.ProfessionalGoodsCategoryDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.goodsCategory.description))
                .ForMember(dest => dest.ProfessionalPassengerCategoryCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.passengerCategory.code))
                .ForMember(dest => dest.ProfessionalPassengerCategoryDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.passengerCategory.description))
                .ForMember(dest => dest.ProfessionalSpareCategoryXCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.spareCategoryX.code))
                .ForMember(dest => dest.ProfessionalSpareCategoryXDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.spareCategoryX.description))
                .ForMember(dest => dest.ProfessionalSpareCategoryYCode, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.spareCategoryY.code))
                .ForMember(dest => dest.ProfessionalSpareCategoryYDescription, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.spareCategoryY.description))
                .ForMember(dest => dest.ProfessionalvalidFromDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.validFromDate))
   
                .ForMember(dest => dest.ProfessionalExpiryDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.expiryDate))
                .ForMember(dest => dest.ProfessionalSuspendedFromDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.suspendedFromDate))
                .ForMember(dest => dest.ProfessionalSuspendedToDate, opt => opt.MapFrom(src => src.Body.X3042Response.response.PrDP.suspendedToDate))

                .ReverseMap();

        }

    }
}