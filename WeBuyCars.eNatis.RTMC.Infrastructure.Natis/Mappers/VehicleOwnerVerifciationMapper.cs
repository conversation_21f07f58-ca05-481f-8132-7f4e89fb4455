using AutoMapper;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class VehicleOwnerVerifciationMapper : Profile
    {

        public VehicleOwnerVerifciationMapper()
        {


            CreateMap<RTMCResponse, VehicleOwnerVerification>().ReverseMap();
            CreateMap<RTMCVehicleOwnerVerificationDetail, VehicleOwnerVerification>().ReverseMap();

            CreateMap<Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3004Response.executionResult.errorMessages.code))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3004Response.executionResult.successful))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3004Response.executionResult.errorMessages.message))
                .ReverseMap();

            CreateMap<Envelope, VehicleOwnerVerification>()
                .ForMember(dest => dest.PersonDocumentTypeCode, opt => opt.MapFrom(src => src.Body.X3004Response.response.person.idDocumentType.code))
                .ForMember(dest => dest.PersonDocumentTypeDescription, opt => opt.MapFrom(src => src.Body.X3004Response.response.person.idDocumentType.description))
                .ForMember(dest => dest.PersonDocumentNumber, opt => opt.MapFrom(src => src.Body.X3004Response.response.person.idDocumentNumber))

                .ForMember(dest => dest.TitleHolderConfirmation, opt => opt.MapFrom(src => src.Body.X3004Response.response.titleHolder.confirm))

                .ForMember(dest => dest.OwnerSurnameofBusiness, opt => opt.MapFrom(src => src.Body.X3004Response.response.owner.surnameOrNameOfBusiness))
                .ForMember(dest => dest.OwnerInitials, opt => opt.MapFrom(src => src.Body.X3004Response.response.owner.initials))
                .ForMember(dest => dest.OwnerConfirmation, opt => opt.MapFrom(src => src.Body.X3004Response.response.owner.confirm))

                .ForMember(dest => dest.VehicleRegistrationNumber, opt => opt.MapFrom(src => src.Body.X3004Response.response.vehicle.registerNumber))
                .ForMember(dest => dest.VehicleLicenseNumber, opt => opt.MapFrom(src => src.Body.X3004Response.response.vehicle.LicenceNumber))
                .ForMember(dest => dest.VehicleVinorChassis, opt => opt.MapFrom(src => src.Body.X3004Response.response.vehicle.vinOrChassis))

                .ReverseMap();

        }

    }
}