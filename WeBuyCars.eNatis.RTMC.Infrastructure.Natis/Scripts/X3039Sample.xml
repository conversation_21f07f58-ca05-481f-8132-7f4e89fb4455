<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://tasima/common/ws/schema/">
     <soapenv:Header>
      <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">                            =20
      <wsse:UsernameToken xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">                           =
 <wsse:Username>1406A001</wsse:Username>
      <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">TESTER01</wsse:Password>
      </wsse:UsernameToken>
      </wsse:Security>
   </soapenv:Header>
   <soapenv:Body>
      <sch:X3039Request>
         <sch:idDocumentTypeCode>04</sch:idDocumentTypeCode>
         <sch:idDocumentNumber>F151166380022</sch:idDocumentNumber>
         <sch:InfringeNoticeNumber>0240060000000220</sch:InfringeNoticeNumber>
         <sch:nomineeIdDocumentTypeCode>02</sch:nomineeIdDocumentTypeCode>
         <sch:nomineeIdDocumentNumber>7411205208087</sch:nomineeIdDocumentNumber>
         <sch:nomineeLicenceNumber>40460000001H</sch:nomineeLicenceNumber></sch:X3039Request>
   </soapenv:Body>
</soapenv:Envelope>
>