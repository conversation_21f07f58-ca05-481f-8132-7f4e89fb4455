<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://tasima/common/ws/schema/">
      <soapenv:Header>
      <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">                             
      <wsse:UsernameToken xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">                             
      <wsse:Username>1406A001</wsse:Username>
      <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">TESTER01</wsse:Password>
      </wsse:UsernameToken>
      </wsse:Security>
   </soapenv:Header>
   <soapenv:Body>
      <sch:X3050Request>
         <!--You have a CHOICE of the next 3 items at this level-->
         <!--Optional:-->
         <sch:Query>
            <sch:idDocumentTypeCode>04</sch:idDocumentTypeCode>
            <sch:idDocumentNumber>F151166380022</sch:idDocumentNumber>
         </sch:Query>
         <sch:Filter>
            <!--Optional:-->
            <sch:CaptureDate>
               <!--Optional:-->
               <sch:CaptureFromDate>2024-01-01T00:00:00</sch:CaptureFromDate>
               <!--Optional:-->
               <sch:CaptureToDate>2024-01-16T16:30:00</sch:CaptureToDate>
            </sch:CaptureDate>
            <sch:IncludeImages>true</sch:IncludeImages>
            <sch:ResultSize>50</sch:ResultSize>
         </sch:Filter>
      </sch:X3050Request>
   </soapenv:Body>
</soapenv:Envelope>