#!/bin/bash


if [ "$OSTYPE" == "linux-gnu" ]; then
	#TODO - These paths need to move to a more standard location (Just here for testing)
	export LD_LIBRARY_PATH=/home/<USER>/openssl/lib:/home/<USER>/curl/lib:$LD_LIBRARY_PATH
	CURL=/home/<USER>/curl/bin/curl
else
	CURL=/usr/bin/curl
fi

$CURL -v --insecure --cacert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/enatisca2028.crt --key /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_key.pem --cert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws
