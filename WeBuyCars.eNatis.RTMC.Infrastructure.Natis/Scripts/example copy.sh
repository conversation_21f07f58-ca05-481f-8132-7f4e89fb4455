curl -v --insecure --cacert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/enatisca2028.crt --key /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_key.pem --cert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d '<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:sch="http://tasima/common/ws/schema/">
      <soapenv:Header>
      <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
      <wsse:UsernameToken xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
      <wsse:Username>4984A001</wsse:Username>
      <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">TESTER01</wsse:Password>
      </wsse:UsernameToken>
      </wsse:Security>
   </soapenv:Header>
   <soapenv:Body>
      <sch:X3003Request>
         <sch:RegisterNumber>BBB001N</sch:RegisterNumber>
      </sch:X3003Request>
   </soapenv:Body>
</soapenv:Envelope>' https://iftst.enatis.co.za/enatis/ws
