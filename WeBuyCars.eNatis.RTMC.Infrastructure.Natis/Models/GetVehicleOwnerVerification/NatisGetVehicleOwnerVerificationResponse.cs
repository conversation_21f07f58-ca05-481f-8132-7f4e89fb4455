namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification
{

    // NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
    public partial class Envelope
    {

        private object headerField;

        private EnvelopeBody bodyField;

        /// <remarks/>
        public object Header
        {
            get
            {
                return this.headerField;
            }
            set
            {
                this.headerField = value;
            }
        }

        /// <remarks/>
        public EnvelopeBody Body
        {
            get
            {
                return this.bodyField;
            }
            set
            {
                this.bodyField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    public partial class EnvelopeBody
    {

        private X3004Response x3004ResponseField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
        public X3004Response X3004Response
        {
            get
            {
                return this.x3004ResponseField;
            }
            set
            {
                this.x3004ResponseField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
    public partial class X3004Response
    {

        private X3004ResponseExecutionResult executionResultField;

        private X3004ResponseResponse responseField;

        /// <remarks/>
        public X3004ResponseExecutionResult executionResult
        {
            get
            {
                return this.executionResultField;
            }
            set
            {
                this.executionResultField = value;
            }
        }

        /// <remarks/>
        public X3004ResponseResponse response
        {
            get
            {
                return this.responseField;
            }
            set
            {
                this.responseField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseExecutionResult
    {

        private bool successfulField;

    private X3004ResponseExecutionResultErrorMessages errorMessagesField;

    /// <remarks/>
    public bool successful
    {
        get
        {
            return this.successfulField;
        }
        set
        {
            this.successfulField = value;
        }
    }

    public X3004ResponseExecutionResultErrorMessages errorMessages
    {
        get
        {
            return this.errorMessagesField;
        }
        set
        {
            this.errorMessagesField = value;
        }
    }

}

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponse
    {

        private X3004ResponseResponsePerson personField;

        private X3004ResponseResponseTitleHolder titleHolderField;

        private X3004ResponseResponseOwner ownerField;

        private X3004ResponseResponseVehicle vehicleField;

        /// <remarks/>
        public X3004ResponseResponsePerson person
        {
            get
            {
                return this.personField;
            }
            set
            {
                this.personField = value;
            }
        }

        /// <remarks/>
        public X3004ResponseResponseTitleHolder titleHolder
        {
            get
            {
                return this.titleHolderField;
            }
            set
            {
                this.titleHolderField = value;
            }
        }

        /// <remarks/>
        public X3004ResponseResponseOwner owner
        {
            get
            {
                return this.ownerField;
            }
            set
            {
                this.ownerField = value;
            }
        }

        /// <remarks/>
        public X3004ResponseResponseVehicle vehicle
        {
            get
            {
                return this.vehicleField;
            }
            set
            {
                this.vehicleField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponsePerson
    {

        private X3004ResponseResponsePersonIdDocumentType idDocumentTypeField;

        private string idDocumentNumberField;

        /// <remarks/>
        public X3004ResponseResponsePersonIdDocumentType idDocumentType
        {
            get
            {
                return this.idDocumentTypeField;
            }
            set
            {
                this.idDocumentTypeField = value;
            }
        }

        /// <remarks/>
        public string idDocumentNumber
        {
            get
            {
                return this.idDocumentNumberField;
            }
            set
            {
                this.idDocumentNumberField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponsePersonIdDocumentType
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponseTitleHolder
    {

        private string confirmField;

        /// <remarks/>
        public string confirm
        {
            get
            {
                return this.confirmField;
            }
            set
            {
                this.confirmField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponseOwner
    {

    private string surnameOrNameOfBusinessField;

    private string initialsField;

    private string confirmField;

    /// <remarks/>
    public string surnameOrNameOfBusiness
    {
        get
        {
            return this.surnameOrNameOfBusinessField;
        }
        set
        {
            this.surnameOrNameOfBusinessField = value;
        }
    }

    /// <remarks/>
    public string initials
    {
        get
        {
            return this.initialsField;
        }
        set
        {
            this.initialsField = value;
        }
    }

        /// <remarks/>
        public string confirm
        {
            get
            {
                return this.confirmField;
            }
            set
            {
                this.confirmField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseResponseVehicle
    {

        private string registerNumberField;

        private string licenceNumberField;

        private string vinOrChassisField;

        /// <remarks/>
        public string registerNumber
        {
            get
            {
                return this.registerNumberField;
            }
            set
            {
                this.registerNumberField = value;
            }
        }

        /// <remarks/>
        public string LicenceNumber
        {
            get
            {
                return this.licenceNumberField;
            }
            set
            {
                this.licenceNumberField = value;
            }
        }

        /// <remarks/>
        public string vinOrChassis
        {
            get
            {
                return this.vinOrChassisField;
            }
            set
            {
                this.vinOrChassisField = value;
            }
        }
    }


    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3004ResponseExecutionResultErrorMessages
    {

        private string fieldField;

        private string codeField;

        private string messageField;

        private string severityField;

        /// <remarks/>
        public string field
        {
            get
            {
                return this.fieldField;
            }
            set
            {
                this.fieldField = value;
            }
        }

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }

        /// <remarks/>
        public string severity
        {
            get
            {
                return this.severityField;
            }
            set
            {
                this.severityField = value;
            }
        }
    }



}