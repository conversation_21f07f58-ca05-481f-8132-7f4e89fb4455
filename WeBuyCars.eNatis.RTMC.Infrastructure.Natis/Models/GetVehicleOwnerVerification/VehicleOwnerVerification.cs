using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification
{
    public class VehicleOwnerVerification
    {

        public VehicleOwnerVerification(){
            // BaseResponse BaseResponse = new BaseResponse();
        }

        public string PersonDocumentTypeCode { get; set; }
        public string PersonDocumentTypeDescription { get; set; }
        public string PersonDocumentNumber { get; set; }
        
        public string TitleHolderConfirmation { get; set; }

        public string OwnerSurnameofBusiness { get; set; }
        public string OwnerInitials { get; set; }
        public string OwnerConfirmation { get; set; }

        public string VehicleRegistrationNumber { get; set; }
        public string VehicleLicenseNumber { get; set; }
        public string VehicleVinorChassis { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }
}