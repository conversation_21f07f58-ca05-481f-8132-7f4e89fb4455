
namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation
{


// NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
public partial class Envelope
{

    private object headerField;

    private EnvelopeBody bodyField;

    /// <remarks/>
    public object Header
    {
        get
        {
            return this.headerField;
        }
        set
        {
            this.headerField = value;
        }
    }

    /// <remarks/>
    public EnvelopeBody Body
    {
        get
        {
            return this.bodyField;
        }
        set
        {
            this.bodyField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
public partial class EnvelopeBody
{

    private X3042Response x3042ResponseField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
    public X3042Response X3042Response
    {
        get
        {
            return this.x3042ResponseField;
        }
        set
        {
            this.x3042ResponseField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
public partial class X3042Response
{

    private X3042ResponseExecutionResult executionResultField;

    private X3042ResponseResponse responseField;

    /// <remarks/>
    public X3042ResponseExecutionResult executionResult
    {
        get
        {
            return this.executionResultField;
        }
        set
        {
            this.executionResultField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponse response
    {
        get
        {
            return this.responseField;
        }
        set
        {
            this.responseField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseExecutionResult
{

    private bool successfulField;


        private X3042ResponseExecutionResultErrorMessages errorMessagesField;

        /// <remarks/>
        public bool successful
        {
            get
            {
                return this.successfulField;
            }
            set
            {
                this.successfulField = value;
            }
        }

        public X3042ResponseExecutionResultErrorMessages errorMessages
        {
            get
            {
                return this.errorMessagesField;
            }
            set
            {
                this.errorMessagesField = value;
            }
        }

    }

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponse
{

    private X3042ResponseResponseLicenceCard licenceCardField;

    private X3042ResponseResponsePerson personField;

    private X3042ResponseResponseDrivingLicence drivingLicenceField;

    private X3042ResponseResponseLearnerLicence learnerLicenceField;

    private X3042ResponseResponsePrDP prDPField;

    /// <remarks/>
    public X3042ResponseResponseLicenceCard licenceCard
    {
        get
        {
            return this.licenceCardField;
        }
        set
        {
            this.licenceCardField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponsePerson person
    {
        get
        {
            return this.personField;
        }
        set
        {
            this.personField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseDrivingLicence drivingLicence
    {
        get
        {
            return this.drivingLicenceField;
        }
        set
        {
            this.drivingLicenceField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseLearnerLicence learnerLicence
    {
        get
        {
            return this.learnerLicenceField;
        }
        set
        {
            this.learnerLicenceField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponsePrDP PrDP
    {
        get
        {
            return this.prDPField;
        }
        set
        {
            this.prDPField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseLicenceCard
{

    private string drivingLicenceNumberField;

    private System.DateTime dateOfFirstIssueField;

    private byte cardIssueNumberField;

    private System.DateTime validToField;

    /// <remarks/>
    public string drivingLicenceNumber
    {
        get
        {
            return this.drivingLicenceNumberField;
        }
        set
        {
            this.drivingLicenceNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime dateOfFirstIssue
    {
        get
        {
            return this.dateOfFirstIssueField;
        }
        set
        {
            this.dateOfFirstIssueField = value;
        }
    }

    /// <remarks/>
    public byte cardIssueNumber
    {
        get
        {
            return this.cardIssueNumberField;
        }
        set
        {
            this.cardIssueNumberField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime validTo
    {
        get
        {
            return this.validToField;
        }
        set
        {
            this.validToField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePerson
{

    private X3042ResponseResponsePersonIdDocumentType idDocumentTypeField;

        private string idDocumentNumberField;

    private string initialsField;

    private string businessOrSurnameField;

    private System.DateTime birthDateField;

    private byte ageField;

    private X3042ResponseResponsePersonNatureOfPersonCode natureOfPersonCodeField;

    private X3042ResponseResponsePersonPopulationGroupCode populationGroupCodeField;

    private X3042ResponseResponsePersonLicenceRestriction licenceRestrictionField;

    /// <remarks/>
    public X3042ResponseResponsePersonIdDocumentType idDocumentType
    {
        get
        {
            return this.idDocumentTypeField;
        }
        set
        {
            this.idDocumentTypeField = value;
        }
    }

        /// <remarks/>
        public string idDocumentNumber
        {
            get
            {
                return this.idDocumentNumberField;
            }
            set
            {
                this.idDocumentNumberField = value;
            }
        }

    /// <remarks/>
    public string initials
    {
        get
        {
            return this.initialsField;
        }
        set
        {
            this.initialsField = value;
        }
    }

    /// <remarks/>
    public string businessOrSurname
    {
        get
        {
            return this.businessOrSurnameField;
        }
        set
        {
            this.businessOrSurnameField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime birthDate
    {
        get
        {
            return this.birthDateField;
        }
        set
        {
            this.birthDateField = value;
        }
    }

    /// <remarks/>
    public byte age
    {
        get
        {
            return this.ageField;
        }
        set
        {
            this.ageField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponsePersonNatureOfPersonCode natureOfPersonCode
    {
        get
        {
            return this.natureOfPersonCodeField;
        }
        set
        {
            this.natureOfPersonCodeField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponsePersonPopulationGroupCode populationGroupCode
    {
        get
        {
            return this.populationGroupCodeField;
        }
        set
        {
            this.populationGroupCodeField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponsePersonLicenceRestriction licenceRestriction
    {
        get
        {
            return this.licenceRestrictionField;
        }
        set
        {
            this.licenceRestrictionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePersonIdDocumentType
{

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePersonNatureOfPersonCode
{

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePersonPopulationGroupCode
{

    private string codeField;

    private string descriptionField;

    /// <remarks/>
    public string code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePersonLicenceRestriction
{

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseDrivingLicence
{

    private System.DateTime dateOfFirstIssueField;

    private X3042ResponseResponseDrivingLicenceDrivingLicenceType drivingLicenceTypeField;

    private System.DateTime licenceAuthorisationDateField;

    private System.DateTime validFromField;

    private X3042ResponseResponseDrivingLicenceVehicleRestriction vehicleRestrictionField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime dateOfFirstIssue
    {
        get
        {
            return this.dateOfFirstIssueField;
        }
        set
        {
            this.dateOfFirstIssueField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseDrivingLicenceDrivingLicenceType drivingLicenceType
    {
        get
        {
            return this.drivingLicenceTypeField;
        }
        set
        {
            this.drivingLicenceTypeField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime licenceAuthorisationDate
    {
        get
        {
            return this.licenceAuthorisationDateField;
        }
        set
        {
            this.licenceAuthorisationDateField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime validFrom
    {
        get
        {
            return this.validFromField;
        }
        set
        {
            this.validFromField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseDrivingLicenceVehicleRestriction vehicleRestriction
    {
        get
        {
            return this.vehicleRestrictionField;
        }
        set
        {
            this.vehicleRestrictionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseDrivingLicenceDrivingLicenceType
{

    private string codeField;

    private string descriptionField;

    /// <remarks/>
    public string code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseDrivingLicenceVehicleRestriction
{

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseLearnerLicence
{

    private string learnerCertificateNumberField;

    private X3042ResponseResponseLearnerLicenceLearnerLicenceType learnerLicenceTypeField;

    private X3042ResponseResponseLearnerLicenceLearnerLicenceStatus learnerLicenceStatusField;

    private System.DateTime validFromField;

    private string speciallyAdaptedVehicleRequiredField;

    /// <remarks/>
    public string learnerCertificateNumber
    {
        get
        {
            return this.learnerCertificateNumberField;
        }
        set
        {
            this.learnerCertificateNumberField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseLearnerLicenceLearnerLicenceType learnerLicenceType
    {
        get
        {
            return this.learnerLicenceTypeField;
        }
        set
        {
            this.learnerLicenceTypeField = value;
        }
    }

    /// <remarks/>
    public X3042ResponseResponseLearnerLicenceLearnerLicenceStatus learnerLicenceStatus
    {
        get
        {
            return this.learnerLicenceStatusField;
        }
        set
        {
            this.learnerLicenceStatusField = value;
        }
    }

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
    public System.DateTime validFrom
    {
        get
        {
            return this.validFromField;
        }
        set
        {
            this.validFromField = value;
        }
    }

    /// <remarks/>
    public string speciallyAdaptedVehicleRequired
    {
        get
        {
            return this.speciallyAdaptedVehicleRequiredField;
        }
        set
        {
            this.speciallyAdaptedVehicleRequiredField = value;
        }
    }
}



    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3042ResponseResponsePrDP
    {

        private System.DateTime dateAuthorisedField;

        private System.DateTime validFromDateField;

        private System.DateTime expiryDateField;

        private System.DateTime suspendedFromDateField;

        private System.DateTime suspendedToDateField;

        private X3042ResponseResponsePrDPDangerousGoodsCategory dangerousGoodsCategoryField;
        private X3042ResponseResponsePrDPGoodsCategory goodsCategoryField;
        private X3042ResponseResponsePrDPPassengerCategory passengerCategoryField;
        private X3042ResponseResponsePrDPSpareCategoryX spareCategoryXField;
        private X3042ResponseResponsePrDPSpareCategoryY spareCategoryYField;


        /// <remarks/>
        public X3042ResponseResponsePrDPDangerousGoodsCategory dangerousGoodsCategory
        {
            get
            {
                return this.dangerousGoodsCategoryField;
            }
            set
            {
                this.dangerousGoodsCategoryField = value;
            }
        }

        /// <remarks/>
        public X3042ResponseResponsePrDPGoodsCategory goodsCategory
        {
            get
            {
                return this.goodsCategoryField;
            }
            set
            {
                this.goodsCategoryField = value;
            }
        }

        /// <remarks/>
        public X3042ResponseResponsePrDPPassengerCategory passengerCategory
        {
            get
            {
                return this.passengerCategoryField;
            }
            set
            {
                this.passengerCategoryField = value;
            }
        }

        /// <remarks/>
        public X3042ResponseResponsePrDPSpareCategoryX spareCategoryX
        {
            get
            {
                return this.spareCategoryXField;
            }
            set
            {
                this.spareCategoryXField = value;
            }
        }

        /// <remarks/>
        public X3042ResponseResponsePrDPSpareCategoryY spareCategoryY
        {
            get
            {
                return this.spareCategoryYField;
            }
            set
            {
                this.spareCategoryYField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime dateAuthorised
        {
            get
            {
                return this.dateAuthorisedField;
            }
            set
            {
                this.dateAuthorisedField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime validFromDate
        {
            get
            {
                return this.validFromDateField;
            }
            set
            {
                this.validFromDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime expiryDate
        {
            get
            {
                return this.expiryDateField;
            }
            set
            {
                this.expiryDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime suspendedFromDate
        {
            get
            {
                return this.suspendedFromDateField;
            }
            set
            {
                this.suspendedFromDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime suspendedToDate
        {
            get
            {
                return this.suspendedToDateField;
            }
            set
            {
                this.suspendedToDateField = value;
            }
        }

    }



    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3042ResponseResponseLearnerLicenceLearnerLicenceType
    {

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponseLearnerLicenceLearnerLicenceStatus
{

    private byte codeField;

    private string descriptionField;

    /// <remarks/>
    public byte code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePrDPDangerousGoodsCategory
{

    private string codeField;

    private string descriptionField;

    /// <remarks/>
    public string code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePrDPGoodsCategory
{

    private string codeField;

    private string descriptionField;

    /// <remarks/>
    public string code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3042ResponseResponsePrDPPassengerCategory
{

    private string codeField;

    private string descriptionField;

    /// <remarks/>
    public string code
    {
        get
        {
            return this.codeField;
        }
        set
        {
            this.codeField = value;
        }
    }

    /// <remarks/>
    public string description
    {
        get
        {
            return this.descriptionField;
        }
        set
        {
            this.descriptionField = value;
        }
    }
}

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3042ResponseResponsePrDPSpareCategoryX
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }


    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3042ResponseResponsePrDPSpareCategoryY
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3042ResponseExecutionResultErrorMessages
    {

        private string fieldField;

        private string codeField;

        private string messageField;

        private string severityField;

        /// <remarks/>
        public string field
        {
            get
            {
                return this.fieldField;
            }
            set
            {
                this.fieldField = value;
            }
        }

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }

        /// <remarks/>
        public string severity
        {
            get
            {
                return this.severityField;
            }
            set
            {
                this.severityField = value;
            }
        }
    }


}