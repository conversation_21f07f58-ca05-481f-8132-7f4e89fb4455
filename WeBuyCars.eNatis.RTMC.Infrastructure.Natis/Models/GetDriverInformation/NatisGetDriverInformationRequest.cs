namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation
{

// XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
// using (StringReader reader = new StringReader(xml))
// {
//    var test = (Envelope)serializer.Deserialize(reader);
// }

    public class NatisGetDriverInformationRequest
    {
            public string DocumentTypeCode { get; set; } 

            public string DocumentNumber { get; set; } 

    }
}