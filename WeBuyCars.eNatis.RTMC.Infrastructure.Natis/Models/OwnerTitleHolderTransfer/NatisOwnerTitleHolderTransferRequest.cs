namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnerTitleHolderTransfer
{

// XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
// using (StringReader reader = new StringReader(xml))
// {
//    var test = (Envelope)serializer.Deserialize(reader);
// }

    public class NatisOwnerTitleHolderTransferRequest
    {
            public string DocumentTypeCode { get; set; } 

            public string DocumentNumber { get; set; } 

    }
}