using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnerTitleHolderTransfer
{
    public class OwnerTitleHolderTransferInformation
    {

        public OwnerTitleHolderTransferInformation(){
            // BaseResponse BaseResponse = new BaseResponse();
        }

        public decimal RegistrationFeeAmount { get; set; }
        public int ConvenienceFeeAmount { get; set; }
        public int TotalRegistrationFeeAmount { get; set; }
        public string PaymentReferenceNumber { get; set; }

        public string RegisterNumber { get; set; }
        public string VinOrChassis { get; set; }
        public string VehicleCertificateNumber { get; set; }

        public string TitleHolderIdDocumentType { get; set; }
        public string TitleHolderIdDocumentNumber { get; set; }

        public string OwnerIdDocumentType { get; set; }
        public string OwnerIdDocumentNumber { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }
}