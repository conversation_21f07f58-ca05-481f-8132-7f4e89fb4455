namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnerTitleHolderTransfer
{
   

// NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
public partial class Envelope
{

    private object headerField;

    private EnvelopeBody bodyField;

    /// <remarks/>
    public object Header
    {
        get
        {
            return this.headerField;
        }
        set
        {
            this.headerField = value;
        }
    }

    /// <remarks/>
    public EnvelopeBody Body
    {
        get
        {
            return this.bodyField;
        }
        set
        {
            this.bodyField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
public partial class EnvelopeBody
{

    private X3141Response x3141ResponseField;

    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
    public X3141Response X3141Response
    {
        get
        {
            return this.x3141ResponseField;
        }
        set
        {
            this.x3141ResponseField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
[System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
public partial class X3141Response
{

    private string transactionStatusField;

    private X3141ResponseResult resultField;

    /// <remarks/>
    public string transactionStatus
    {
        get
        {
            return this.transactionStatusField;
        }
        set
        {
            this.transactionStatusField = value;
        }
    }

    /// <remarks/>
    public X3141ResponseResult result
    {
        get
        {
            return this.resultField;
        }
        set
        {
            this.resultField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3141ResponseResult
{

    private decimal registrationFeeAmountField;

    private ushort convenienceFeeAmountField;

    private ushort totalRegistrationFeeAmountField;

    private string paymentReferenceNumberField;

    private X3141ResponseResultVehicleParticulars vehicleParticularsField;

    private X3141ResponseResultTitleHolder titleHolderField;

    private X3141ResponseResultOwner ownerField;

    /// <remarks/>
    public decimal RegistrationFeeAmount
    {
        get
        {
            return this.registrationFeeAmountField;
        }
        set
        {
            this.registrationFeeAmountField = value;
        }
    }

    /// <remarks/>
    public ushort ConvenienceFeeAmount
    {
        get
        {
            return this.convenienceFeeAmountField;
        }
        set
        {
            this.convenienceFeeAmountField = value;
        }
    }

    /// <remarks/>
    public ushort TotalRegistrationFeeAmount
    {
        get
        {
            return this.totalRegistrationFeeAmountField;
        }
        set
        {
            this.totalRegistrationFeeAmountField = value;
        }
    }

    /// <remarks/>
    public string PaymentReferenceNumber
    {
        get
        {
            return this.paymentReferenceNumberField;
        }
        set
        {
            this.paymentReferenceNumberField = value;
        }
    }

    /// <remarks/>
    public X3141ResponseResultVehicleParticulars VehicleParticulars
    {
        get
        {
            return this.vehicleParticularsField;
        }
        set
        {
            this.vehicleParticularsField = value;
        }
    }

    /// <remarks/>
    public X3141ResponseResultTitleHolder TitleHolder
    {
        get
        {
            return this.titleHolderField;
        }
        set
        {
            this.titleHolderField = value;
        }
    }

    /// <remarks/>
    public X3141ResponseResultOwner Owner
    {
        get
        {
            return this.ownerField;
        }
        set
        {
            this.ownerField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3141ResponseResultVehicleParticulars
{

    private string registerNumberField;

    private string vinOrChassisField;

    private string vehicleCertificateNumberField;

    /// <remarks/>
    public string RegisterNumber
    {
        get
        {
            return this.registerNumberField;
        }
        set
        {
            this.registerNumberField = value;
        }
    }

    /// <remarks/>
    public string VinOrChassis
    {
        get
        {
            return this.vinOrChassisField;
        }
        set
        {
            this.vinOrChassisField = value;
        }
    }

    /// <remarks/>
    public string VehicleCertificateNumber
    {
        get
        {
            return this.vehicleCertificateNumberField;
        }
        set
        {
            this.vehicleCertificateNumberField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3141ResponseResultTitleHolder
{

    private byte idDocumentTypeField;

    private string idDocumentNumberField;

    /// <remarks/>
    public byte IdDocumentType
    {
        get
        {
            return this.idDocumentTypeField;
        }
        set
        {
            this.idDocumentTypeField = value;
        }
    }

    /// <remarks/>
    public string IdDocumentNumber
    {
        get
        {
            return this.idDocumentNumberField;
        }
        set
        {
            this.idDocumentNumberField = value;
        }
    }
}

/// <remarks/>
[System.SerializableAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
public partial class X3141ResponseResultOwner
{

    private byte idDocumentTypeField;

    private string idDocumentNumberField;

    /// <remarks/>
    public byte IdDocumentType
    {
        get
        {
            return this.idDocumentTypeField;
        }
        set
        {
            this.idDocumentTypeField = value;
        }
    }

    /// <remarks/>
    public string IdDocumentNumber
    {
        get
        {
            return this.idDocumentNumberField;
        }
        set
        {
            this.idDocumentNumberField = value;
        }
    }
}




}