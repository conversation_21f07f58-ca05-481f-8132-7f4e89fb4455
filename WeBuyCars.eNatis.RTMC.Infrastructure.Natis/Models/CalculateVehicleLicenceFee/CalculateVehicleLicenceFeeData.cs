using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee
{

    
    public class CalculateVehicleLicenceFeeData
    {

        public CalculateVehicleLicenceFeeData(){
             BaseResponse = new BaseResponse();             
        }

        public Result result { get; set; }
        public Data data { get; set; }
        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }

    public class Result
    {
        public ErrorMessage[] errorMessages { get; set; }
        public bool successful { get; set; }
    }

    public class ErrorMessage
    {
        public string message { get; set; }
        public string code { get; set; }
        public string severity { get; set; }
    }

    public class Data
    {
        public string vehicle { get; set; }
        public string fees { get; set; }
        public string transactionFee { get; set; }
        public string totalAmountDue { get; set; }
        public string existingDebt { get; set; }
        public string arrears { get; set; }
        public string penalties { get; set; }

    }

}