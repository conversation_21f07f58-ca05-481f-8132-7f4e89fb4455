using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle
{
    public class VehicleInformation
    {

        public VehicleInformation(){
            // BaseResponse BaseResponse = new BaseResponse();
        }


        public string AxlesDriven { get; set; }
        public string AxlesTotal { get; set; }
        public string CategoryCode { get; set; }
        public string CategoryDescription { get; set; }
        public string DescriptionCode { get; set; }
        public string DescriptionDescription { get; set; }
        public string DrivenCode { get; set; }
        public string DrivenDescription { get; set; }
        public string EngineDisplacement { get; set; }
        public string EngineNumber { get; set; }
        public string FuelTypeCode { get; set; }
        public string FuelTypeDescription { get; set; }
        public string GVM { get; set; }
        public string LicenceChangeDate { get; set; }
        public string LicenceExpiryDate { get; set; }
        public string LicenceNumber { get; set; }
        public string LifeStatusCode { get; set; }
        public string LifeStatusDescription { get; set; }
        public string MainColourCode { get; set; }
        public string MainColourDescription { get; set; }
        public string MakeCode { get; set; }
        public string MakeDescription { get; set; }
        public string ModelNameCode { get; set; }
        public string ModelNameDescription { get; set; }
        public string NetPower { get; set; }
        public string NoOfWheels { get; set; }
        public string OverallHeight { get; set; }
        public string OverallLength { get; set; }
        public string OverallWidth { get; set; }
        public string PrePreviousLicenceNumber { get; set; }
        public string PreviousLicenceNumber { get; set; }
        public string RegisterNumber { get; set; }
        public string RegistrationTypeCode { get; set; }
        public string RegistrationTypeDescription { get; set; }
        public string VehicleCertificateNumber { get; set; }
        public string RoadworthyStatusCode { get; set; }
        public string RoadworthyStatusDescription { get; set; }
        public string RoadworthyStatusDate { get; set; }
        public string RoadworthyTestDate { get; set; }
        public string SapClearanceDate { get; set; }
        public string SapClearanceStatusCode { get; set; }
        public string SapClearanceStatusDescription { get; set; }
        public string SapMarkCode { get; set; }
        public string SapMarkDescription { get; set; }
        public string SapMarkDate { get; set; }
        public string Tare { get; set; }
        public string VehicleStateCode { get; set; }
        public string VehicleStateDescription { get; set; }
        public string VehicleStateDate { get; set; }
        public string VinOrChassis { get; set; }


        //Vehicle Detail Additional Information
        public string LicenceLiabilityDate { get; set; }

        public string RegAuthorityOfLicensingCode { get; set; }
        public string RegAuthorityOfLicensingName { get; set; }

        public string RegAuthorityOfLicenceNumberCode { get; set; }
        public string RegAuthorityOfLicenceNumberName { get; set; }

        public string RegistrationDate { get; set; }

        public string RegistrationQualifierCode { get; set; }
        public string RegistrationQualifierDescription { get; set; }
        public string RegistrationQualifierDate { get; set; }
        
        public string DataOwnerCode { get; set; }

        public string DataOwnerDescription { get; set; }

        public string Timestamp { get; set; }

        public string TransmissionCode { get; set; }
        public string TransmissionDescription { get; set; }

        public string GearboxNumber { get; set; }

        public string DifferentialNumber { get; set; }

        public string FirstLicensingDate { get; set; }

        public string CountryOfExportCode { get; set; }
        public string CountryOfExportDescripiton { get; set; }

        public string CountryOfImportCode { get; set; }
        public string CountryOfImportDescription { get; set; }

        public string ModelNumber { get; set; }

        public string SapClearanceReasonCode { get; set; }

        public string SapClearanceReasonDescription { get; set; }

        public string VehicleUsageCode { get; set; }        

        public string VehicleUsageDescription { get; set; }        
        
        public string EconomicSectorCode { get; set; }        

        public string EconomicSectorDescription { get; set; }        

        public string PreviousVehicleCertificateNumber { get; set; }        

        public string CapacitySitting { get; set; }        

        public string CapacityStanding { get; set; }       

        public string LicenceFee { get; set; }       
        
        public string VtsNumber { get; set; }       

        public string VtsName { get; set; }       

        public string ExaminerNumber { get; set; }       

        public string ExaminerName { get; set; }       

        public string ExemptionCode { get; set; }

        public string ExemptionDescription { get; set; }    

        public string RoadUseIndicator { get; set; }    

        public string Overdue { get; set; }    
        public string PrePrePreviousLicenceNumber { get; set; }

        
        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
        
    }
}