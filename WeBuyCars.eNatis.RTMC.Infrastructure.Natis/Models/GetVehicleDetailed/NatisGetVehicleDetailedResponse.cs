namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed
{
    // NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
    public partial class Envelope
    {

        private object headerField;

        private EnvelopeBody bodyField;

        /// <remarks/>
        public object Header
        {
            get
            {
                return this.headerField;
            }
            set
            {
                this.headerField = value;
            }
        }

        /// <remarks/>
        public EnvelopeBody Body
        {
            get
            {
                return this.bodyField;
            }
            set
            {
                this.bodyField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    public partial class EnvelopeBody
    {

        private X3067Response x3067ResponseField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
        public X3067Response X3067Response
        {
            get
            {
                return this.x3067ResponseField;
            }
            set
            {
                this.x3067ResponseField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
    public partial class X3067Response
    {

        private X3067ResponseExecutionResult executionResultField;

        private X3067ResponseResponseDetail responseDetailField;

        /// <remarks/>
        public X3067ResponseExecutionResult executionResult
        {
            get
            {
                return this.executionResultField;
            }
            set
            {
                this.executionResultField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetail responseDetail
        {
            get
            {
                return this.responseDetailField;
            }
            set
            {
                this.responseDetailField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseExecutionResult
    {

        private bool successfulField;

        private X3067ResponseExecutionResultErrorMessages errorMessagesField;

        /// <remarks/>
        public bool successful
        {
            get
            {
                return this.successfulField;
            }
            set
            {
                this.successfulField = value;
            }
        }

        public X3067ResponseExecutionResultErrorMessages errorMessages
        {
            get
            {
                return this.errorMessagesField;
            }
            set
            {
                this.errorMessagesField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetail
    {

        private X3067ResponseResponseDetailVehicle vehicleField;

        /// <remarks/>
        public X3067ResponseResponseDetailVehicle Vehicle
        {
            get
            {
                return this.vehicleField;
            }
            set
            {
                this.vehicleField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicle
    {

        private string registerNumberField;

        private string licenceNumberField;

        private string vinOrChassisField;

        private string engineNumberField;

        private X3067ResponseResponseDetailVehicleSapMark sapMarkField;

        private System.DateTime licenceChangeDateField;

        private System.DateTime licenceExpiryDateField;

        private X3067ResponseResponseDetailVehicleRegAuthorityOfLicensing regAuthorityOfLicensingField;

        private X3067ResponseResponseDetailVehicleRegAuthorityOfLicenceNumber regAuthorityOfLicenceNumberField;

        private X3067ResponseResponseDetailVehicleModelName modelNameField;

        private X3067ResponseResponseDetailVehicleCategory categoryField;

        private X3067ResponseResponseDetailVehicleDriven drivenField;

        private X3067ResponseResponseDetailVehicleDescription descriptionField;

        private X3067ResponseResponseDetailVehicleMake makeField;

        private X3067ResponseResponseDetailVehicleMainColour mainColourField;

        private ushort gVMField;

        private ushort engineDisplacementField;

    private uint tareField;

        private X3067ResponseResponseDetailVehicleFuelType fuelTypeField;

        private int netPowerField;

        private System.DateTime registrationDateField;

        private X3067ResponseResponseDetailVehicleRegistrationQualifier registrationQualifierField;

        private System.DateTime registrationQualifierDateField;

        private X3067ResponseResponseDetailVehicleRoadworthyStatus roadworthyStatusField;

        private System.DateTime roadworthyStatusDateField;

        private System.DateTime roadworthyTestDateField;

        private X3067ResponseResponseDetailVehicleVehicleState vehicleStateField;

        private System.DateTime vehicleStateDateField;

        private X3067ResponseResponseDetailVehicleDataOwner dataOwnerField;

        private uint timestampField;

        private X3067ResponseResponseDetailVehicleTransmission transmissionField;

        private string gearboxNumberField;

        private string differentialNumberField;
	
	private System.DateTime firstLicensingDateField;

        private X3067ResponseResponseDetailVehicleCountryOfImport countryOfImportField;

        private X3067ResponseResponseDetailVehicleLifeStatus lifeStatusField;

        private uint modelNumberField;

        private X3067ResponseResponseDetailVehicleSapClearanceReason sapClearanceReasonField;

        private X3067ResponseResponseDetailVehicleSapClearanceStatus sapClearanceStatusField;

        private System.DateTime sapClearanceDateField;

        private System.DateTime sapMarkDateField;

        private X3067ResponseResponseDetailVehicleVehicleUsage vehicleUsageField;

        private X3067ResponseResponseDetailVehicleEconomicSector economicSectorField;

        private string certificateNumberField;

        private string previousCertificateNumberField;

        private string previousLicenceNumberField;

        private string prePreviousLicenceNumberField;

        private ushort axlesTotalField;

        private ushort noOfWheelsField;

        private ushort overallWidthField;

        private ushort axlesDrivenField;

        private ushort overallLengthField;

        private ushort overallHeightField;

        private ushort capacitySittingField;

        private ushort capacityStandingField;

        private int licenceFeeField;

        private X3067ResponseResponseDetailVehicleRegistrationType registrationTypeField;

        private string vtsNumberField;

        private string vtsNameField;

        private string examinerNumberField;

        private string examinerNameField;

        private X3067ResponseResponseDetailVehicleExemption exemptionField;

        private bool roadUseIndicatorField;

        private bool overdueField;

        private System.DateTime firstRegistrationDateField;

        private string administrationMarkIndicatorField;

        private string registrationAllowedField;

        /// <remarks/>
        public string RegisterNumber
        {
            get
            {
                return this.registerNumberField;
            }
            set
            {
                this.registerNumberField = value;
            }
        }

        /// <remarks/>
        public string LicenceNumber
        {
            get
            {
                return this.licenceNumberField;
            }
            set
            {
                this.licenceNumberField = value;
            }
        }

        /// <remarks/>
        public string VinOrChassis
        {
            get
            {
                return this.vinOrChassisField;
            }
            set
            {
                this.vinOrChassisField = value;
            }
        }

        /// <remarks/>
        public string EngineNumber
        {
            get
            {
                return this.engineNumberField;
            }
            set
            {
                this.engineNumberField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleSapMark SapMark
        {
            get
            {
                return this.sapMarkField;
            }
            set
            {
                this.sapMarkField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime LicenceChangeDate
        {
            get
            {
                return this.licenceChangeDateField;
            }
            set
            {
                this.licenceChangeDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime LicenceExpiryDate
        {
            get
            {
                return this.licenceExpiryDateField;
            }
            set
            {
                this.licenceExpiryDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleRegAuthorityOfLicensing RegAuthorityOfLicensing
        {
            get
            {
                return this.regAuthorityOfLicensingField;
            }
            set
            {
                this.regAuthorityOfLicensingField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleRegAuthorityOfLicenceNumber RegAuthorityOfLicenceNumber
        {
            get
            {
                return this.regAuthorityOfLicenceNumberField;
            }
            set
            {
                this.regAuthorityOfLicenceNumberField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleModelName ModelName
        {
            get
            {
                return this.modelNameField;
            }
            set
            {
                this.modelNameField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleCategory Category
        {
            get
            {
                return this.categoryField;
            }
            set
            {
                this.categoryField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleDriven Driven
        {
            get
            {
                return this.drivenField;
            }
            set
            {
                this.drivenField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleDescription Description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleMake Make
        {
            get
            {
                return this.makeField;
            }
            set
            {
                this.makeField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleMainColour MainColour
        {
            get
            {
                return this.mainColourField;
            }
            set
            {
                this.mainColourField = value;
            }
        }

        /// <remarks/>
        public ushort GVM
        {
            get
            {
                return this.gVMField;
            }
            set
            {
                this.gVMField = value;
            }
        }

        /// <remarks/>
        public ushort EngineDisplacement
        {
            get
            {
                return this.engineDisplacementField;
            }
            set
            {
                this.engineDisplacementField = value;
            }
        }

    /// <remarks/>
    public uint Tare
    {
        get
        {
            return this.tareField;
        }
        set
        {
            this.tareField = value;
        }
    }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleFuelType FuelType
        {
            get
            {
                return this.fuelTypeField;
            }
            set
            {
                this.fuelTypeField = value;
            }
        }

        /// <remarks/>
        public int NetPower
        {
            get
            {
                return this.netPowerField;
            }
            set
            {
                this.netPowerField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime RegistrationDate
        {
            get
            {
                return this.registrationDateField;
            }
            set
            {
                this.registrationDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleRegistrationQualifier RegistrationQualifier
        {
            get
            {
                return this.registrationQualifierField;
            }
            set
            {
                this.registrationQualifierField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime RegistrationQualifierDate
        {
            get
            {
                return this.registrationQualifierDateField;
            }
            set
            {
                this.registrationQualifierDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleRoadworthyStatus RoadworthyStatus
        {
            get
            {
                return this.roadworthyStatusField;
            }
            set
            {
                this.roadworthyStatusField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime RoadworthyStatusDate
        {
            get
            {
                return this.roadworthyStatusDateField;
            }
            set
            {
                this.roadworthyStatusDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime RoadworthyTestDate
        {
            get
            {
                return this.roadworthyTestDateField;
            }
            set
            {
                this.roadworthyTestDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleVehicleState VehicleState
        {
            get
            {
                return this.vehicleStateField;
            }
            set
            {
                this.vehicleStateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime VehicleStateDate
        {
            get
            {
                return this.vehicleStateDateField;
            }
            set
            {
                this.vehicleStateDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleDataOwner DataOwner
        {
            get
            {
                return this.dataOwnerField;
            }
            set
            {
                this.dataOwnerField = value;
            }
        }

        /// <remarks/>
        public uint Timestamp
        {
            get
            {
                return this.timestampField;
            }
            set
            {
                this.timestampField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleTransmission Transmission
        {
            get
            {
                return this.transmissionField;
            }
            set
            {
                this.transmissionField = value;
            }
        }

        /// <remarks/>
        public string GearboxNumber
        {
            get
            {
                return this.gearboxNumberField;
            }
            set
            {
                this.gearboxNumberField = value;
            }
        }

        /// <remarks/>
        public string DifferentialNumber
        {
            get
            {
                return this.differentialNumberField;
            }
            set
            {
                this.differentialNumberField = value;
            }
        }
	
	[System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime FirstLicensingDate
        {
            get
            {
                return this.firstLicensingDateField;
            }
            set
            {
                this.firstLicensingDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleCountryOfImport CountryOfImport
        {
            get
            {
                return this.countryOfImportField;
            }
            set
            {
                this.countryOfImportField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleLifeStatus LifeStatus
        {
            get
            {
                return this.lifeStatusField;
            }
            set
            {
                this.lifeStatusField = value;
            }
        }

        /// <remarks/>
        public uint ModelNumber
        {
            get
            {
                return this.modelNumberField;
            }
            set
            {
                this.modelNumberField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleSapClearanceReason SapClearanceReason
        {
            get
            {
                return this.sapClearanceReasonField;
            }
            set
            {
                this.sapClearanceReasonField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleSapClearanceStatus SapClearanceStatus
        {
            get
            {
                return this.sapClearanceStatusField;
            }
            set
            {
                this.sapClearanceStatusField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime SapClearanceDate
        {
            get
            {
                return this.sapClearanceDateField;
            }
            set
            {
                this.sapClearanceDateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime SapMarkDate
        {
            get
            {
                return this.sapMarkDateField;
            }
            set
            {
                this.sapMarkDateField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleVehicleUsage VehicleUsage
        {
            get
            {
                return this.vehicleUsageField;
            }
            set
            {
                this.vehicleUsageField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleEconomicSector EconomicSector
        {
            get
            {
                return this.economicSectorField;
            }
            set
            {
                this.economicSectorField = value;
            }
        }

        /// <remarks/>
        public string CertificateNumber
        {
            get
            {
                return this.certificateNumberField;
            }
            set
            {
                this.certificateNumberField = value;
            }
        }

        /// <remarks/>
        public string PreviousCertificateNumber
        {
            get
            {
                return this.previousCertificateNumberField;
            }
            set
            {
                this.previousCertificateNumberField = value;
            }
        }

        /// <remarks/>
        public string PreviousLicenceNumber
        {
            get
            {
                return this.previousLicenceNumberField;
            }
            set
            {
                this.previousLicenceNumberField = value;
            }
        }

        /// <remarks/>
        public string PrePreviousLicenceNumber
        {
            get
            {
                return this.prePreviousLicenceNumberField;
            }
            set
            {
                this.prePreviousLicenceNumberField = value;
            }
        }

        /// <remarks/>
        public ushort AxlesTotal
        {
            get
            {
                return this.axlesTotalField;
            }
            set
            {
                this.axlesTotalField = value;
            }
        }

        /// <remarks/>
        public ushort NoOfWheels
        {
            get
            {
                return this.noOfWheelsField;
            }
            set
            {
                this.noOfWheelsField = value;
            }
        }

        /// <remarks/>
        public ushort OverallWidth
        {
            get
            {
                return this.overallWidthField;
            }
            set
            {
                this.overallWidthField = value;
            }
        }

        /// <remarks/>
        public ushort AxlesDriven
        {
            get
            {
                return this.axlesDrivenField;
            }
            set
            {
                this.axlesDrivenField = value;
            }
        }

        /// <remarks/>
        public ushort OverallLength
        {
            get
            {
                return this.overallLengthField;
            }
            set
            {
                this.overallLengthField = value;
            }
        }

        /// <remarks/>
        public ushort OverallHeight
        {
            get
            {
                return this.overallHeightField;
            }
            set
            {
                this.overallHeightField = value;
            }
        }

        /// <remarks/>
        public ushort CapacitySitting
        {
            get
            {
                return this.capacitySittingField;
            }
            set
            {
                this.capacitySittingField = value;
            }
        }

        /// <remarks/>
        public ushort CapacityStanding
        {
            get
            {
                return this.capacityStandingField;
            }
            set
            {
                this.capacityStandingField = value;
            }
        }

        /// <remarks/>
        public int LicenceFee
        {
            get
            {
                return this.licenceFeeField;
            }
            set
            {
                this.licenceFeeField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleRegistrationType RegistrationType
        {
            get
            {
                return this.registrationTypeField;
            }
            set
            {
                this.registrationTypeField = value;
            }
        }

        /// <remarks/>
        public string VtsNumber
        {
            get
            {
                return this.vtsNumberField;
            }
            set
            {
                this.vtsNumberField = value;
            }
        }

        /// <remarks/>
        public string VtsName
        {
            get
            {
                return this.vtsNameField;
            }
            set
            {
                this.vtsNameField = value;
            }
        }

        /// <remarks/>
        public string ExaminerNumber
        {
            get
            {
                return this.examinerNumberField;
            }
            set
            {
                this.examinerNumberField = value;
            }
        }

        /// <remarks/>
        public string ExaminerName
        {
            get
            {
                return this.examinerNameField;
            }
            set
            {
                this.examinerNameField = value;
            }
        }

        /// <remarks/>
        public X3067ResponseResponseDetailVehicleExemption Exemption
        {
            get
            {
                return this.exemptionField;
            }
            set
            {
                this.exemptionField = value;
            }
        }

        /// <remarks/>
        public bool RoadUseIndicator
        {
            get
            {
                return this.roadUseIndicatorField;
            }
            set
            {
                this.roadUseIndicatorField = value;
            }
        }

        /// <remarks/>
        public bool Overdue
        {
            get
            {
                return this.overdueField;
            }
            set
            {
                this.overdueField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime FirstRegistrationDate
        {
            get
            {
                return this.firstRegistrationDateField;
            }
            set
            {
                this.firstRegistrationDateField = value;
            }
        }

        /// <remarks/>
        public string AdministrationMarkIndicator
        {
            get
            {
                return this.administrationMarkIndicatorField;
            }
            set
            {
                this.administrationMarkIndicatorField = value;
            }
        }

        /// <remarks/>
        public string RegistrationAllowed
        {
            get
            {
                return this.registrationAllowedField;
            }
            set
            {
                this.registrationAllowedField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleSapMark
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleRegAuthorityOfLicensing
    {

        private ushort authorityCodeField;

        private string authorityNameField;

        /// <remarks/>
        public ushort authorityCode
        {
            get
            {
                return this.authorityCodeField;
            }
            set
            {
                this.authorityCodeField = value;
            }
        }

        /// <remarks/>
        public string authorityName
        {
            get
            {
                return this.authorityNameField;
            }
            set
            {
                this.authorityNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleRegAuthorityOfLicenceNumber
    {

        private ushort authorityCodeField;

        private string authorityNameField;

        /// <remarks/>
        public ushort authorityCode
        {
            get
            {
                return this.authorityCodeField;
            }
            set
            {
                this.authorityCodeField = value;
            }
        }

        /// <remarks/>
        public string authorityName
        {
            get
            {
                return this.authorityNameField;
            }
            set
            {
                this.authorityNameField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleModelName
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleCategory
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleDriven
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleDescription
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleMake
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleMainColour
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleFuelType
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleRegistrationQualifier
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleRoadworthyStatus
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleVehicleState
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleDataOwner
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleTransmission
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleCountryOfImport
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleLifeStatus
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleSapClearanceReason
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleSapClearanceStatus
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleVehicleUsage
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleEconomicSector
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleRegistrationType
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseResponseDetailVehicleExemption
    {

        private ushort codeField;

        private string descriptionField;

        /// <remarks/>
        public ushort code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3067ResponseExecutionResultErrorMessages
    {

        private string fieldField;

        private string codeField;

        private string messageField;

        private string severityField;

        /// <remarks/>
        public string field
        {
            get
            {
                return this.fieldField;
            }
            set
            {
                this.fieldField = value;
            }
        }

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string message
        {
            get
            {
                return this.messageField;
            }
            set
            {
                this.messageField = value;
            }
        }

        /// <remarks/>
        public string severity
        {
            get
            {
                return this.severityField;
            }
            set
            {
                this.severityField = value;
            }
        }
    }

}