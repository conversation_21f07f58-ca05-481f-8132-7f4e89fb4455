namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed
{

// XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
// using (StringReader reader = new StringReader(xml))
// {
//    var test = (Envelope)serializer.Deserialize(reader);
// }

    public class NatisGetVehicleDetailedRequest
    {
            public string RegisterNumber { get; set; } 

            public string VinOrChassis { get; set; } 

            public string EngineNumber { get; set; } 

            public string LicenceNumber { get; set; } 
    }
}