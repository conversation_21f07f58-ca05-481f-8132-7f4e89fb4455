
using System.Diagnostics;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory
{

    
    public class OwnershipHistoryData
    {
        public Result result { get; set; }
        public Data data { get; set; }
    }

    public class Result
    {
        public object[] errorMessages { get; set; }
        public bool successful { get; set; }
    }

    public class Data
    {
        public Vehicle vehicle { get; set; }
        public Ownershiphistory[] ownershipHistory { get; set; }
    }

    public class Vehicle
    {
        public string vinOrChassis { get; set; }
        public string registerNumber { get; set; }
        public string licenseNumber { get; set; }
    }

    public class Ownershiphistory
    {
        public Owner owner { get; set; }
    }

    public class Owner
    {
        public string name { get; set; }
        public string identificationNumber { get; set; }
        public string ownershipStatus { get; set; }
        public string insuranceCompany { get; set; }
        public string ownershipType { get; set; }
        public string ownershipDate { get; set; }
    }

}