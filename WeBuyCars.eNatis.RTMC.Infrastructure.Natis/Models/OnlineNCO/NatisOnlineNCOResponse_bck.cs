// namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO
// {

//     // NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
//     [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
//     public partial class Envelope
//     {

//         private object headerField;

//         private EnvelopeBody bodyField;

//         /// <remarks/>
//         public object Header
//         {
//             get
//             {
//                 return this.headerField;
//             }
//             set
//             {
//                 this.headerField = value;
//             }
//         }

//         /// <remarks/>
//         public EnvelopeBody Body
//         {
//             get
//             {
//                 return this.bodyField;
//             }
//             set
//             {
//                 this.bodyField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
//     public partial class EnvelopeBody
//     {

//         private X314AResponse x314AResponseField;

//         /// <remarks/>
//         [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
//         public X314AResponse X314AResponse
//         {
//             get
//             {
//                 return this.x314AResponseField;
//             }
//             set
//             {
//                 this.x314AResponseField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
//     public partial class X314AResponse
//     {

//         private string transactionStatusField;

//         private X314AResponseResult resultField;

//         private X314AResponseMessages messagesField;

//         /// <remarks/>
//         public string transactionStatus
//         {
//             get
//             {
//                 return this.transactionStatusField;
//             }
//             set
//             {
//                 this.transactionStatusField = value;
//             }
//         }

//         /// <remarks/>
//         public X314AResponseResult result
//         {
//             get
//             {
//                 return this.resultField;
//             }
//             set
//             {
//                 this.resultField = value;
//             }
//         }

//         /// <remarks/>
//         public X314AResponseMessages messages
//         {
//             get
//             {
//                 return this.messagesField;
//             }
//             set
//             {
//                 this.messagesField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     public partial class X314AResponseMessages
//     {

//         private string severityField;

//         private string fieldField;

//         private string codeField;

//         private string messageField;

//         /// <remarks/>
//         public string severity
//         {
//             get
//             {
//                 return this.severityField;
//             }
//             set
//             {
//                 this.severityField = value;
//             }
//         }

//         /// <remarks/>
//         public string field
//         {
//             get
//             {
//                 return this.fieldField;
//             }
//             set
//             {
//                 this.fieldField = value;
//             }
//         }

//         /// <remarks/>
//         public string code
//         {
//             get
//             {
//                 return this.codeField;
//             }
//             set
//             {
//                 this.codeField = value;
//             }
//         }

//         /// <remarks/>
//         public string message
//         {
//             get
//             {
//                 return this.messageField;
//             }
//             set
//             {
//                 this.messageField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     public partial class X314AResponseResult
//     {

//         private X314AResponseResultRC2 rC2Field;

//         /// <remarks/>
//         public X314AResponseResultRC2 RC2
//         {
//             get
//             {
//                 return this.rC2Field;
//             }
//             set
//             {
//                 this.rC2Field = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     public partial class X314AResponseResultRC2
//     {

//         private string regAuthorityField;

//         private string registerNumberField;

//         private string vinOrChassisField;

//         private string engineNumberField;

//         private string makeField;

//         private string modelNameField;

//         private string vehicleCategoryField;

//         private string drivenField;

//         private string vehicleDescriptionField;

//         private long tareField;

//         private System.DateTime firstLicenceLiabiltyDateField;

//         private string vehicleLifeStatusField;

//         private X314AResponseResultRC2Seller sellerField;

//         private X314AResponseResultRC2Purchaser purchaserField;

//         private System.DateTime firstRegistrationLiabiltyDateField;

//         private System.DateTime issueDateField;

//         private string issuedByField;

//         private string vehicleCertificateNumberField;

//         private string barcodeField;

//         private long userGroupCodeField;

//         private System.DateTime dateTimeField;

//         private string watermarkField;

//         /// <remarks/>
//         public string RegAuthority
//         {
//             get
//             {
//                 return this.regAuthorityField;
//             }
//             set
//             {
//                 this.regAuthorityField = value;
//             }
//         }

//         /// <remarks/>
//         public string RegisterNumber
//         {
//             get
//             {
//                 return this.registerNumberField;
//             }
//             set
//             {
//                 this.registerNumberField = value;
//             }
//         }

//         /// <remarks/>
//         public string VinOrChassis
//         {
//             get
//             {
//                 return this.vinOrChassisField;
//             }
//             set
//             {
//                 this.vinOrChassisField = value;
//             }
//         }

//         /// <remarks/>
//         public string EngineNumber
//         {
//             get
//             {
//                 return this.engineNumberField;
//             }
//             set
//             {
//                 this.engineNumberField = value;
//             }
//         }

//         /// <remarks/>
//         public string Make
//         {
//             get
//             {
//                 return this.makeField;
//             }
//             set
//             {
//                 this.makeField = value;
//             }
//         }

//         /// <remarks/>
//         public string ModelName
//         {
//             get
//             {
//                 return this.modelNameField;
//             }
//             set
//             {
//                 this.modelNameField = value;
//             }
//         }

//         /// <remarks/>
//         public string VehicleCategory
//         {
//             get
//             {
//                 return this.vehicleCategoryField;
//             }
//             set
//             {
//                 this.vehicleCategoryField = value;
//             }
//         }

//         /// <remarks/>
//         public string Driven
//         {
//             get
//             {
//                 return this.drivenField;
//             }
//             set
//             {
//                 this.drivenField = value;
//             }
//         }

//         /// <remarks/>
//         public string VehicleDescription
//         {
//             get
//             {
//                 return this.vehicleDescriptionField;
//             }
//             set
//             {
//                 this.vehicleDescriptionField = value;
//             }
//         }

//         /// <remarks/>
//         public long Tare
//         {
//             get
//             {
//                 return this.tareField;
//             }
//             set
//             {
//                 this.tareField = value;
//             }
//         }

//         /// <remarks/>
//         [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
//         public System.DateTime FirstLicenceLiabiltyDate
//         {
//             get
//             {
//                 return this.firstLicenceLiabiltyDateField;
//             }
//             set
//             {
//                 this.firstLicenceLiabiltyDateField = value;
//             }
//         }

//         /// <remarks/>
//         public string VehicleLifeStatus
//         {
//             get
//             {
//                 return this.vehicleLifeStatusField;
//             }
//             set
//             {
//                 this.vehicleLifeStatusField = value;
//             }
//         }

//         /// <remarks/>
//         public X314AResponseResultRC2Seller Seller
//         {
//             get
//             {
//                 return this.sellerField;
//             }
//             set
//             {
//                 this.sellerField = value;
//             }
//         }

//         /// <remarks/>
//         public X314AResponseResultRC2Purchaser Purchaser
//         {
//             get
//             {
//                 return this.purchaserField;
//             }
//             set
//             {
//                 this.purchaserField = value;
//             }
//         }

//         /// <remarks/>
//         [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
//         public System.DateTime FirstRegistrationLiabiltyDate
//         {
//             get
//             {
//                 return this.firstRegistrationLiabiltyDateField;
//             }
//             set
//             {
//                 this.firstRegistrationLiabiltyDateField = value;
//             }
//         }

//         /// <remarks/>
//         [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
//         public System.DateTime IssueDate
//         {
//             get
//             {
//                 return this.issueDateField;
//             }
//             set
//             {
//                 this.issueDateField = value;
//             }
//         }

//         /// <remarks/>
//         public string IssuedBy
//         {
//             get
//             {
//                 return this.issuedByField;
//             }
//             set
//             {
//                 this.issuedByField = value;
//             }
//         }

//         /// <remarks/>
//         public string VehicleCertificateNumber
//         {
//             get
//             {
//                 return this.vehicleCertificateNumberField;
//             }
//             set
//             {
//                 this.vehicleCertificateNumberField = value;
//             }
//         }

//         /// <remarks/>
//         public string Barcode
//         {
//             get
//             {
//                 return this.barcodeField;
//             }
//             set
//             {
//                 this.barcodeField = value;
//             }
//         }

//         /// <remarks/>
//         public long UserGroupCode
//         {
//             get
//             {
//                 return this.userGroupCodeField;
//             }
//             set
//             {
//                 this.userGroupCodeField = value;
//             }
//         }

//         /// <remarks/>
//         public System.DateTime DateTime
//         {
//             get
//             {
//                 return this.dateTimeField;
//             }
//             set
//             {
//                 this.dateTimeField = value;
//             }
//         }

//         /// <remarks/>
//         public string Watermark
//         {
//             get
//             {
//                 return this.watermarkField;
//             }
//             set
//             {
//                 this.watermarkField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     public partial class X314AResponseResultRC2Seller
//     {

//         private string idDocumentTypeField;

//         private string idDocumentNumberField;

//         private string countryOfIssueField;

//         private string nameField;

//         /// <remarks/>
//         public string IdDocumentType
//         {
//             get
//             {
//                 return this.idDocumentTypeField;
//             }
//             set
//             {
//                 this.idDocumentTypeField = value;
//             }
//         }

//         /// <remarks/>
//         public string IdDocumentNumber
//         {
//             get
//             {
//                 return this.idDocumentNumberField;
//             }
//             set
//             {
//                 this.idDocumentNumberField = value;
//             }
//         }

//         /// <remarks/>
//         public string CountryOfIssue
//         {
//             get
//             {
//                 return this.countryOfIssueField;
//             }
//             set
//             {
//                 this.countryOfIssueField = value;
//             }
//         }

//         /// <remarks/>
//         public string Name
//         {
//             get
//             {
//                 return this.nameField;
//             }
//             set
//             {
//                 this.nameField = value;
//             }
//         }
//     }

//     /// <remarks/>
//     [System.SerializableAttribute()]
//     [System.ComponentModel.DesignerCategoryAttribute("code")]
//     [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
//     public partial class X314AResponseResultRC2Purchaser
//     {

//         private string idDocumentTypeField;

//         private string idDocumentNumberField;

//         private string countryOfIssueField;

//         private string nameField;

//         /// <remarks/>
//         public string IdDocumentType
//         {
//             get
//             {
//                 return this.idDocumentTypeField;
//             }
//             set
//             {
//                 this.idDocumentTypeField = value;
//             }
//         }

//         /// <remarks/>
//         public string IdDocumentNumber
//         {
//             get
//             {
//                 return this.idDocumentNumberField;
//             }
//             set
//             {
//                 this.idDocumentNumberField = value;
//             }
//         }

//         /// <remarks/>
//         public string CountryOfIssue
//         {
//             get
//             {
//                 return this.countryOfIssueField;
//             }
//             set
//             {
//                 this.countryOfIssueField = value;
//             }
//         }

//         /// <remarks/>
//         public string Name
//         {
//             get
//             {
//                 return this.nameField;
//             }
//             set
//             {
//                 this.nameField = value;
//             }
//         }
//     }
// }

