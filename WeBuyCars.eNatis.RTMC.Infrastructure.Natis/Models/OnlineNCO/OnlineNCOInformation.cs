using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO
{
    public class OnlineNCOInformation
    {

        public OnlineNCOInformation(){
            // BaseResponse BaseResponse = new BaseResponse();
        }

        public string RegAuthority { get; set; }
        public string RegisterNumber { get; set; }
        public string VinOrChassis { get; set; }
        public string EngineNumber { get; set; }
        public string Make { get; set; }
        public string ModelName { get; set; }
        public string VehicleCategory { get; set; }
        public string Driven { get; set; }
        public string VehicleDescription { get; set; }
        public string Tare { get; set; }
        public string FirstLicenceLiabilityDate { get; set; }
        public string VehicleLifeStatus { get; set; }
        public string SellerIdDocumentType { get; set; }
        public string SellerIdDocumentNumber { get; set; }
        public string SellerCountryOfIssue { get; set; }
        public string SellerName { get; set; }
        public string PurchaserIdDocumentType { get; set; }
        public string PurchaserName { get; set; }
        public string PurchaserIdDocumentNumber { get; set; }
        public string PurchaserCountryOfIssue { get; set; }
        public string FirstRegistrationLiabilityDate { get; set; }
        public string IssueDate { get; set; }
        public string IssuedBy { get; set; }
        public string VehicleCertificateNumber { get; set; }
        public string Barcode { get; set; }
        public string UserGroupCode { get; set; }
        public string DateTime { get; set; }
        public string Watermark { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }
}