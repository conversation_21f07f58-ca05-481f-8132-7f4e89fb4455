namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models
{
    // NOTE: Generated code may require at least .NET Framework 4.5 or .NET Core/Standard 2.0.
    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://schemas.xmlsoap.org/soap/envelope/", IsNullable = false)]
    public partial class Envelope
    {

        private object headerField;

        private EnvelopeBody bodyField;

        /// <remarks/>
        public object Header
        {
            get
            {
                return this.headerField;
            }
            set
            {
                this.headerField = value;
            }
        }

        /// <remarks/>
        public EnvelopeBody Body
        {
            get
            {
                return this.bodyField;
            }
            set
            {
                this.bodyField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
    public partial class EnvelopeBody
    {

        private X3003Response x3003ResponseField;

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(Namespace = "http://tasima/common/ws/schema/")]
        public X3003Response X3003Response
        {
            get
            {
                return this.x3003ResponseField;
            }
            set
            {
                this.x3003ResponseField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    [System.Xml.Serialization.XmlRootAttribute(Namespace = "http://tasima/common/ws/schema/", IsNullable = false)]
    public partial class X3003Response
    {

        private X3003ResponseExecutionResult executionResultField;

        private X3003ResponseResponse responseField;

        /// <remarks/>
        public X3003ResponseExecutionResult executionResult
        {
            get
            {
                return this.executionResultField;
            }
            set
            {
                this.executionResultField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponse response
        {
            get
            {
                return this.responseField;
            }
            set
            {
                this.responseField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseExecutionResult
    {

        private bool successfulField;

        /// <remarks/>
        public bool successful
        {
            get
            {
                return this.successfulField;
            }
            set
            {
                this.successfulField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponse
    {

        private X3003ResponseResponseVehicle vehicleField;

        /// <remarks/>
        public X3003ResponseResponseVehicle Vehicle
        {
            get
            {
                return this.vehicleField;
            }
            set
            {
                this.vehicleField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicle
    {

        private string licenceNumberField;

        private string registerNumberField;

        private string vinOrChassisField;

        private string engineNumberField;

        private X3003ResponseResponseVehicleMake makeField;

        private X3003ResponseResponseVehicleModelName modelNameField;

        private X3003ResponseResponseVehicleCategory categoryField;

        private X3003ResponseResponseVehicleDriven drivenField;

        private X3003ResponseResponseVehicleDescription descriptionField;

        private byte netPowerField;

        private ushort engineDisplacementField;

        private X3003ResponseResponseVehicleFuelType fuelTypeField;

        private byte axlesTotalField;

        private byte noOfWheelsField;

        private byte overallWidthField;

        private X3003ResponseResponseVehicleRoadworthyStatus roadworthyStatusField;

        private ushort tareField;

        private X3003ResponseResponseVehicleMainColour mainColourField;

        private byte axlesDrivenField;

        private byte overallLengthField;

        private byte overallHeightField;

        private System.DateTime roadworthyStatusDateField;

        private X3003ResponseResponseVehicleSapMark sapMarkField;

        private System.DateTime sapMarkDateField;

        private X3003ResponseResponseVehicleSapClearanceStatus sapClearanceStatusField;

        private System.DateTime sapClearanceDateField;

        private X3003ResponseResponseVehicleLifeStatus lifeStatusField;

        private X3003ResponseResponseVehicleVehicleState vehicleStateField;

        private System.DateTime vehicleStateDateField;

        private X3003ResponseResponseVehicleRegistrationType registrationTypeField;

        private string vehicleCertificateNumberField;

        private System.DateTime licenceChangeDateField;

        private string previousLicenceNumberField;

        /// <remarks/>
        public string LicenceNumber
        {
            get
            {
                return this.licenceNumberField;
            }
            set
            {
                this.licenceNumberField = value;
            }
        }

        /// <remarks/>
        public string RegisterNumber
        {
            get
            {
                return this.registerNumberField;
            }
            set
            {
                this.registerNumberField = value;
            }
        }

        /// <remarks/>
        public string VinOrChassis
        {
            get
            {
                return this.vinOrChassisField;
            }
            set
            {
                this.vinOrChassisField = value;
            }
        }

        /// <remarks/>
        public string EngineNumber
        {
            get
            {
                return this.engineNumberField;
            }
            set
            {
                this.engineNumberField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleMake Make
        {
            get
            {
                return this.makeField;
            }
            set
            {
                this.makeField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleModelName ModelName
        {
            get
            {
                return this.modelNameField;
            }
            set
            {
                this.modelNameField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleCategory Category
        {
            get
            {
                return this.categoryField;
            }
            set
            {
                this.categoryField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleDriven Driven
        {
            get
            {
                return this.drivenField;
            }
            set
            {
                this.drivenField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleDescription Description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }

        /// <remarks/>
        public byte NetPower
        {
            get
            {
                return this.netPowerField;
            }
            set
            {
                this.netPowerField = value;
            }
        }

        /// <remarks/>
        public ushort EngineDisplacement
        {
            get
            {
                return this.engineDisplacementField;
            }
            set
            {
                this.engineDisplacementField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleFuelType FuelType
        {
            get
            {
                return this.fuelTypeField;
            }
            set
            {
                this.fuelTypeField = value;
            }
        }

        /// <remarks/>
        public byte AxlesTotal
        {
            get
            {
                return this.axlesTotalField;
            }
            set
            {
                this.axlesTotalField = value;
            }
        }

        /// <remarks/>
        public byte NoOfWheels
        {
            get
            {
                return this.noOfWheelsField;
            }
            set
            {
                this.noOfWheelsField = value;
            }
        }

        /// <remarks/>
        public byte OverallWidth
        {
            get
            {
                return this.overallWidthField;
            }
            set
            {
                this.overallWidthField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleRoadworthyStatus RoadworthyStatus
        {
            get
            {
                return this.roadworthyStatusField;
            }
            set
            {
                this.roadworthyStatusField = value;
            }
        }

        /// <remarks/>
        public ushort Tare
        {
            get
            {
                return this.tareField;
            }
            set
            {
                this.tareField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleMainColour MainColour
        {
            get
            {
                return this.mainColourField;
            }
            set
            {
                this.mainColourField = value;
            }
        }

        /// <remarks/>
        public byte AxlesDriven
        {
            get
            {
                return this.axlesDrivenField;
            }
            set
            {
                this.axlesDrivenField = value;
            }
        }

        /// <remarks/>
        public byte OverallLength
        {
            get
            {
                return this.overallLengthField;
            }
            set
            {
                this.overallLengthField = value;
            }
        }

        /// <remarks/>
        public byte OverallHeight
        {
            get
            {
                return this.overallHeightField;
            }
            set
            {
                this.overallHeightField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime RoadworthyStatusDate
        {
            get
            {
                return this.roadworthyStatusDateField;
            }
            set
            {
                this.roadworthyStatusDateField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleSapMark SapMark
        {
            get
            {
                return this.sapMarkField;
            }
            set
            {
                this.sapMarkField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime SapMarkDate
        {
            get
            {
                return this.sapMarkDateField;
            }
            set
            {
                this.sapMarkDateField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleSapClearanceStatus SapClearanceStatus
        {
            get
            {
                return this.sapClearanceStatusField;
            }
            set
            {
                this.sapClearanceStatusField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime SapClearanceDate
        {
            get
            {
                return this.sapClearanceDateField;
            }
            set
            {
                this.sapClearanceDateField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleLifeStatus LifeStatus
        {
            get
            {
                return this.lifeStatusField;
            }
            set
            {
                this.lifeStatusField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleVehicleState VehicleState
        {
            get
            {
                return this.vehicleStateField;
            }
            set
            {
                this.vehicleStateField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime VehicleStateDate
        {
            get
            {
                return this.vehicleStateDateField;
            }
            set
            {
                this.vehicleStateDateField = value;
            }
        }

        /// <remarks/>
        public X3003ResponseResponseVehicleRegistrationType RegistrationType
        {
            get
            {
                return this.registrationTypeField;
            }
            set
            {
                this.registrationTypeField = value;
            }
        }

        /// <remarks/>
        public string VehicleCertificateNumber
        {
            get
            {
                return this.vehicleCertificateNumberField;
            }
            set
            {
                this.vehicleCertificateNumberField = value;
            }
        }

        /// <remarks/>
        [System.Xml.Serialization.XmlElementAttribute(DataType = "date")]
        public System.DateTime LicenceChangeDate
        {
            get
            {
                return this.licenceChangeDateField;
            }
            set
            {
                this.licenceChangeDateField = value;
            }
        }

        /// <remarks/>
        public string PreviousLicenceNumber
        {
            get
            {
                return this.previousLicenceNumberField;
            }
            set
            {
                this.previousLicenceNumberField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleMake
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleModelName
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleCategory
    {

        private string codeField;

        private string descriptionField;

        /// <remarks/>
        public string code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleDriven
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleDescription
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleFuelType
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleRoadworthyStatus
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleMainColour
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleSapMark
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleSapClearanceStatus
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleLifeStatus
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleVehicleState
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

    /// <remarks/>
    [System.SerializableAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Xml.Serialization.XmlTypeAttribute(AnonymousType = true, Namespace = "http://tasima/common/ws/schema/")]
    public partial class X3003ResponseResponseVehicleRegistrationType
    {

        private byte codeField;

        private string descriptionField;

        /// <remarks/>
        public byte code
        {
            get
            {
                return this.codeField;
            }
            set
            {
                this.codeField = value;
            }
        }

        /// <remarks/>
        public string description
        {
            get
            {
                return this.descriptionField;
            }
            set
            {
                this.descriptionField = value;
            }
        }
    }

}