namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration
{

// XmlSerializer serializer = new XmlSerializer(typeof(Envelope));
// using (StringReader reader = new StringReader(xml))
// {
//    var test = (Envelope)serializer.Deserialize(reader);
// }

    public class NatisVehicleOwnerRegistrationRequest
    {

        /// <summary>
        /// Business Registration
        /// </summary>
        public string BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// Owner Information
        /// </summary>
        public string OwnerDocumentTypeCode { get; set; } 

        public string OwnerDocumentNumber { get; set; } 

        public string ProxyDocumentTypeCode { get; set; } 

        public string ProxyDocumentNumber { get; set; } 

        public string RepresentativeDocumentTypeCode { get; set; } 

        public string RepresentativeDocumentNumber { get; set; } 

        /// <summary>
        /// Vehicle Information
        /// </summary>
        public string RegisterNumber { get; set; } 
        public string VinOr<PERSON>hassis { get; set; } 
        public string LicenceNumber { get; set; } 
        public string RegistrationLiabilityDate { get; set; } 
        public string NatureOfOwnership { get; set; } 
        public string VehicleUsage { get; set; } 
        public string VehicleCertificateNumber { get; set; } 
        public string RegistrationReason { get; set; } 

    }
}