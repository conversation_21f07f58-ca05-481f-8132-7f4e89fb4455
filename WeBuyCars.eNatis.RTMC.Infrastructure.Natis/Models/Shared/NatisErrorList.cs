namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared
{
    public static class NatisErrorList
    {

        public static readonly string BE339 = "Control Number not Valid.";
        public static readonly string BE048 = "Authorisation was performed by the same user.";
        public static readonly string BE049 = "User authentication error.";
        public static readonly string BE050 = "Invalid amount. Please pay the amount of {0} for licence renewal confirmation number {1}.";
        public static readonly string BE051 = "Invalid amount. Please pay the amount of either {0} or {1} for licence renewal confirmation number {2}. Note that if paying the smaller amount, no licence disc will be issued.";
        public static readonly string BE052 = "Information incorrect, police clearance required.";
        public static readonly string BE053 = "Invalid applicant. Only the appointed title holder should register the motor vehicle.";
        public static readonly string BE054 = "The motor vehicle has not been deregistered yet, upon notification of theft or scrapping. Only an insurance company or the current title holder or owner may register the motor vehicle.";
        public static readonly string BE055 = "Only insurance company may register the vehicle.";
        public static readonly string BE056 = "Vin or chassis number entered is already allocated to another vehicle. No duplicates are allowed.";
        public static readonly string BE057 = "Applicant has other unlicensed vehicles.";
        public static readonly string BE058 = "An unpaid transaction exists for the identified vehicle.";
        public static readonly string BE059 = "Invalid date. If the liability date was set by a process other than notification of ownership change, the specified liability date may not be earlier than the stored liability date.";
        public static readonly string BE060 = "Invalid date. The liability date cannot be earlier than recorded ownership start dates if the vehicle was previously registered.";
        public static readonly string BE061 = "No change of owner allowed.";
        public static readonly string BE062 = "No change of title holder or owner allowed.";
        public static readonly string BE063 = "Invalid amount. Please pay the amount of {0) for registration of motor vehicle with vehicle register number {1}.";
        public static readonly string BE064 = "Invalid amount. Please pay the amount of either {0) or {1} for registration of motor vehicle with vehicle register number {2}.";
        public static readonly string BE065 = "A valid test booking does not exist for the applicant for today.";
        public static readonly string BE066 = "The person identified must be a natural person.";
        public static readonly string BE067 = "Invalid sequence number.";
        public static readonly string BE068 = "Motor vehicle has administration mark.";
        public static readonly string BE069 = "Current owner has administration marks.";
        public static readonly string BE070 = "Current title holder has administration mark.";
        public static readonly string BE071 = "The questions for a computerised learner licence test have not been retrieved.";
        public static readonly string BE072 = "Invalid combination of identifiers.";
        public static readonly string BE073 = "Data not found";
        public static readonly string BE074 = "Either the Image handle number or the Licence certificate number must be supplied.";
        public static readonly string BE075 = "The Image handle number does not refer to a valid image.";
        public static readonly string BE076 = "The licence certificate does not exist on the system.";
        public static readonly string BE077 = "This field is mandatory if the licence certificate number is supplied.";
        public static readonly string BE078 = "The credit card number is invalid.";
        public static readonly string BE079 = "The test question does not exist.";
        public static readonly string BE080 = "User not authorised to execute this transaction.";
        public static readonly string BE082 = "No payable infringement exists.";
        public static readonly string BE083 = "The payment does not exist.";
        public static readonly string BE084 = "The initial payment was not made at this user group.";
        public static readonly string BE085 = "This payment has already been cancelled.";
        public static readonly string BE086 = "No duplicates are allowed.  A motor vehicle record with the same value for the field already exists.";
        public static readonly string BE087 = "The business type must be 1 - Manufacturer or 2 - Importer.";
        public static readonly string BE088 = "Agent must be allowed to introduce motor vehicle as the specific type of business.";
        public static readonly string BE089 = "Manufacturer/Importer may not introduce a motor vehicle of any model.";
        public static readonly string BE090 = "If entity is an importer, a country from where vehicle is imported, must be specified.";
        public static readonly string BE091 = "Entity not allowed to introduce this model.";
        public static readonly string BE092 = "If a 17 character VIN/Chassis number is entered the characters 'I', 'O' or 'Q' must not be used.";
        public static readonly string BE141 = "Must supply either suburb or city/town.";
        public static readonly string BE142 = "The suburb is not valid for the city/town.";
        public static readonly string BE143 = "If any lines of the street address were supplied, the postal code is mandatory.";
        public static readonly string BE144 = "The postal code is not a valid postal code.";
        public static readonly string BE145 = "Invalid postal code for suburb or city/town.";
        public static readonly string BE146 = "If a postal code is supplied, at least one line of the street address fields must be supplied.";
        public static readonly string BE147 = "If the nomination was unsuccessful, this field is mandatory.";
        public static readonly string BE148 = "Invalid enforcement order certificate status.";
        public static readonly string BE149 = "Invalid revocation of enforcement order status.";
        public static readonly string BE150 = "The summons is not in a valid status.";
        public static readonly string BE093 = "The length of a specific VIN, must be 17 characters.";
        public static readonly string BE094 = "First 3 characters of Chassis number must be a recognised WMI.";
        public static readonly string BE095 = "Last 4 characters of Chassis number must be numeric.";
        public static readonly string BE096 = "Value must be supplied for self-propelled vehicles.";
        public static readonly string BE097 = "The motor vehicle state must be 01 - Introduced by online MIB controlled or 02 - Introduced by online MIB released.";
        public static readonly string BE098 = "Date may not be older than one year.";
        public static readonly string BE099 = "When tare is not equal to 99999 kg, tare must be within the range specified for the vehicle category and vehicle trailer type.";
        public static readonly string BE100 = "Nature of ownership may only be 3 or 4.";
        public static readonly string BE101 = "Person does not exist on NaTIS or may be moving. If the motor manufacturer is not the title holder, the title holder must exist on NaTIS and may not be busy moving.";
        public static readonly string BE103 = "Value must be supplied if vehicle is allocated for export.";
        public static readonly string BE104 = "GVM must be equal or greater than the tare value supplied.";
        public static readonly string BE105 = "Invalid axle configuration. The number of driving axles cannot be more than total number of axles.";
        public static readonly string BE106 = "MMS not allowed to introduce vehicles.";
        public static readonly string BE107 = "Chassis number/VIN invalid for make, category, status and first licensing date.";
        public static readonly string BE108 = "Tare must be 99999 kg.";
        public static readonly string BE109 = "Motor vehicle does not exist on the local region. A motor vehicle with entered characteristics must exist for the usergroup of the user.";
        public static readonly string BE110 = "Duplicate motor vehicles with same unit number in the same user group are not allowed.";
        public static readonly string BE111 = "Date may not be before the current state date.";
        public static readonly string BE112 = "Date may not be before the current change date.";
        public static readonly string BE123 = "Invalid infringement status.";
        public static readonly string BE124 = "Invalid representation status.";
        public static readonly string BE125 = "The main charge of the identified infringement is not a minor infringement.";
        public static readonly string BE126 = "The infringement notice number does not contain a valid notice type.";
        public static readonly string BE127 = "The infringement notice number does not contain a valid registering authority code.";
        public static readonly string BE128 = "A rejection reason is required for an unsuccessful outcome.";
        public static readonly string BE129 = "Effective date may not be before current status date.";
        public static readonly string BE130 = "The infringement is not in a valid state for change.";
        public static readonly string BE131 = "A charge code to cancel must be supplied for specified representation status.";
        public static readonly string BE132 = "Charge code not linked to the identified infringement notice number.";
        public static readonly string BE133 = "Invalid charge status.";
        public static readonly string BE134 = "The identified person is the current infringer.";
        public static readonly string BE135 = "If the identified person does not exist on the system, this field is mandatory.";
        public static readonly string BE136 = "If the driver identification details relate to a natural person, this field is mandatory.";
        public static readonly string BE137 = "Categories may not be the same.";
        public static readonly string BE138 = "Invalid combination of categories.";
        public static readonly string BE139 = "The suburb is not a valid suburb name.";
        public static readonly string BE140 = "The city/town is not a valid city/town name.";
        public static readonly string BE151 = "If the court date has not been set, this field is mandatory.";
        public static readonly string BE152 = "Date may not be in the past.";
        public static readonly string BE153 = "If the court date has passed, this field is mandatory.";
        public static readonly string BE154 = "If the court case status is Non-prosecution, this field is mandatory.";
        public static readonly string BE155 = "If the court case has been postponed, this field is mandatory.";
        public static readonly string BE156 = "RA not in environment file. Contact help desk.";
        public static readonly string BE157 = "Data inconsistency. Please contact supervisor. MMS environment - no current alias found.";
        public static readonly string BE158 = "If the driver identification details relate to a natural person and  the <DocType> is Driving licence, this field is mandatory.";
        public static readonly string BE159 = "If the driver identification details relate to a natural person and  the <DocType> is Learner's licence, this field is mandatory.";
        public static readonly string BE160 = "If the driver identification details relate to a natural person and  the <DocType> is Operator card, this field is mandatory.";
        public static readonly string BE161 = "If the driver identification details relate to a natural person and  the <DocType> is Professional driving permit, this field is mandatory.";
        public static readonly string BE162 = "The method of payment is not valid.";
        public static readonly string BE163 = "The financial institution is not valid.";
        public static readonly string BE164 = "The account type is not valid.";
        public static readonly string BE165 = "The instalment outcome is not valid.";
        public static readonly string BE166 = "The approved method of payment is not valid.";
        public static readonly string BE167 = "The instalment application date may not be in the future.";
        public static readonly string BE168 = "The <NOfInstalments> is greater than the maximum allowed number of instalments.";
        public static readonly string BE169 = "Invalid infringement notice number.";
        public static readonly string BE170 = "Instalment account does not exist.";
        public static readonly string BE174 = "Invalid value for motor vehicle characteristic where category is {0} and driven is {1}.";
        public static readonly string BE178 = "Entity record is marked.";
        public static readonly string BE179 = "Person record is marked.";
        public static readonly string BE180 = "An application for instalment account already exist for infringement {0}";
        public static readonly string BE183 = "An instalment application already exists for the infringement.";
        public static readonly string BE184 = "The outcome for the instalment account has already been recorded.";
        public static readonly string BE185 = "The entered number of monthly instalments must be greater than 0.";
        public static readonly string BE186 = "Motor vehicle is in an invalid state for change as it has been previously registered.";
        public static readonly string BE187 = "The entered date of first instalment is in the past.";
        public static readonly string BE188 = "The instalment application date may not be before the infringement date.";
        public static readonly string BE189 = " The photo has already been captured {0} ";
        public static readonly string BE190 = "The identified traffic officer does not exist.";
        public static readonly string BE191 = "Registration has been suspended.";
        public static readonly string BE192 = "Registration has been cancelled.";
        public static readonly string BE193 = "Debit order details are mandatory when approved method of payment is debit order.";
        public static readonly string BE194 = "Debit order details are mandatory when applied method of payment is debit order.";
        public static readonly string BE195 = "GVM is mandatory for specific motor vehicle category.";
        public static readonly string BE196 = "No open debt exists for the infringement notice number.";
        public static readonly string BE197 = "Not a valid user group";
        public static readonly string BE198 = "Date may not be older than 10 years";
        public static readonly string BE199 = "This field is mandatory based on the image status value.";
        public static readonly string BE200 = "If the image is rejected, this field must contain only certain specific values.";
        public static readonly string BE201 = "Person entity configured for MIB has invalid length identification number. Contact help desk.";
        public static readonly string BE202 = "Person entity configured for MIB must have numeric identification number. Contact help desk.";
        public static readonly string BE203 = "Person entity configured for MIB must have valid identification number check digit. Contact help desk.";
        public static readonly string BE204 = "Person entity configured for MIB has invalid identification. Contact help desk.";
        public static readonly string BE205 = "Person entity configured for MIB has identification type that does not correlate with the nature of the person. Contact help desk";
        public static readonly string BE206 = "Person entity configured for MIB has administration mark. Contact help desk.";
        public static readonly string BE207 = "RA not in environment file. Contact help desk.";
        public static readonly string BE208 = "Data inconsistency. Please contact supervisor. MMS environment - no current alias found.";
        public static readonly string BE209 = "MMS not allowed to introduce vehicles.";
        public static readonly string BE210 = "Current owner has administration mark. Contact help desk.";
        public static readonly string BE211 = "Current title holder has administration mark. Contact help desk.";
        public static readonly string BE212 = "Title holder identified is not the person that was granted authorisation.";
        public static readonly string BE213 = "A payment for this infringement is already in progress.";
        public static readonly string BE214 = "A payment for this motor vehicle licence is already in progress.";
        public static readonly string BE215 = "More than 200 infringements were found for the specified period. Please reduce the search period.";
        public static readonly string BE216 = " The organisation has not been registered to query infringement notifications. ";
        public static readonly string BE220 = " Nomination of Driver not allowed on Aarto 01 notices ";
        public static readonly string BE234 = " The infringement is not in a valid state to apply for an instalment account ";
        public static readonly string BE248 = " The province code field does not contain a value between 1 and 9 ";
        public static readonly string BE249 = " Invalid issuing authority ";
        public static readonly string BE250 = " Invalid charge code specified ";
        public static readonly string BE251 = " Vehicle does not exist on the NCR ";
        public static readonly string BE252 = " Infringement already exists ";
        public static readonly string BE253 = " Invalid motor vehicle type code specified ";
        public static readonly string BE254 = " Officer may not be suspended ";
        public static readonly string BE255 = " Officer registration may not cancelled. ";
        public static readonly string BE256 = " The entered infringement notice number does not belong to this issuing authority ";
        public static readonly string BE257 = " The entered infringement notice number must be 16 characters long ";
        public static readonly string BE258 = " Invalid serial number ";
        public static readonly string BE259 = " The infringement time may not be in the future (if infringement date is today) ";
        public static readonly string BE260 = " No link exists between the issuing authority and the officer infrastructure number ";
        public static readonly string BE261 = " One of the vehicle identifiers must be entered ";
        public static readonly string BE262 = " Invalid value entered (If Identification type code entered is 01, 02 ";
        public static readonly string BE263 = " Person does not exist (When Alias Identification Type is 01 (TRN)) ";
        public static readonly string BE264 = " This ID is not acceptable (When Alias Identification Type is NOT 13 (Merged alias), Alias CurrStatAlias may NOT be 3 (Unacceptable alias) for the entered Alias Identification Number) ";
        public static readonly string BE265 = " The identified person must be a natural person ";
        public static readonly string BE266 = " If any lines of the residential address were entered, Suburb is mandatory ";
        public static readonly string BE267 = " If any lines of the residential address were entered, Postal code is mandatory ";
        public static readonly string BE268 = " Person Postal Address Line 1 is mandatory ";
        public static readonly string BE269 = " Suburb is mandatory ";
        public static readonly string BE270 = " Postal code is mandatory ";
        public static readonly string BE271 = " Invalid combination of charge code and vehicle type ";
        public static readonly string BE272 = " This charge has already been added to the infringement as a main charge ";
        public static readonly string BE273 = " Some fields in the input data are conflicting. Refer to field name for details ";
        public static readonly string BE285 = " Current vehicle state invalid for queries - stolen/scrapped. Owned by insurance company ";
        public static readonly string BE286 = " Microdot PIN already exists ";
        public static readonly string BE287 = " Chassis number/VIN must consist of 17 characters ";
        public static readonly string BE290 = " Vehicle can not be introduced because no microdot data could be found ";
        public static readonly string BE292 = " Vehicle can not be updated because no microdot data could be found ";
        public static readonly string BE288 = " An MV with this unit number already exists ";
        public static readonly string BE289 = " Field is compulsory and must be entered ";
        public static readonly string BE291 = " Valid infringement notice. Must be payable at a registering authority, driving licence testing station or issuing authority ";
        public static readonly string BE001 = "Invalid value entered. The check digit of the infringement notice number must be valid.";
        public static readonly string BE002 = " Infringement notice number {0} does not exist. ";
        public static readonly string BE003 = " Infringement notice number {0} has been paid already. ";
        public static readonly string BE004 = " Infringement notice number {0} has been cancelled. ";
        public static readonly string BE005 = "Assessment Of Fees must exist.";
        public static readonly string BE006 = "Amount must be greater than R0.00, i.e. may not be equal to R0.00.";
        public static readonly string BE007 = "Invalid amount. Please pay the amount of {0} for infringement number {1}.";
        public static readonly string BE008 = "The identification type selected requires a 13 character identification number.";
        public static readonly string BE009 = "The identification type selected requires a numeric format identification number.";
        public static readonly string BE010 = "Invalid value entered. The check digit of the identification number must be valid.";
        public static readonly string BE011 = "The person identified does not exist.";
        public static readonly string BE012 = "The identification type and number supplied are not acceptable for the person identified.";
        public static readonly string BE013 = "The identification type and the nature of person do not correlate. RSA and foreign id's must identify natural persons. BRN id's must identify organisations.";
        public static readonly string BE014 = "The licence renewal confirmation number entered is invalid.";
        public static readonly string BE015 = "The motor vehicle's current state is not valid for relicensing.";
        public static readonly string BE016 = "The motor vehicle is already licensed.";
        public static readonly string BE017 = "The owner's identification particulars and the licence renewal confirmation number do not correlate.";
        public static readonly string BE018 = "Transfer date may not be greater than System date.";
        public static readonly string BE019 = "Value must be a valid lookup value.";
        public static readonly string BE020 = "The credit card details must be supplied.";
        public static readonly string BE021 = "Vehicle in invalid state for change.";
        public static readonly string BE022 = "Applicant not the title holder.";
        public static readonly string BE023 = "Date may not be in the future.";
        public static readonly string BE024 = "Motor vehicle is not currently licensed.";
        public static readonly string BE025 = "This ID is not acceptable.";
        public static readonly string BE026 = "The entered identification is not indicated on the system as the current identification.";
        public static readonly string BE027 = "XPIDs and TPIDs are not allowed.";
        public static readonly string BE028 = "Value must be numeric only.";
        public static readonly string BE029 = "Another person, with an RSA identification number of which the first 10 digits correspond with the entered RSA identification number, already exists on the System.";
        public static readonly string BE030 = "Person has administration mark.";
        public static readonly string BE031 = "Persons identified as transferor and as receiver cannot be the same.";
        public static readonly string BE032 = "Enter two identifiers for vehicle identification.";
        public static readonly string BE033 = "Motor vehicle does not exist";
        public static readonly string BE034 = "Invalid combination of vehicle identifiers";
        public static readonly string BE035 = "The identified title holder is not valid for the logged in financial institution. Contact help desk.";
        public static readonly string BE036 = "Invalid identification.  The holder of the identification must go to his/her local registering authority to rectify the identification.";
        public static readonly string BE037 = "The person identified may not be a natural person.";
        public static readonly string BE038 = "The identification type does not correlate with the nature of the person entity.";
        public static readonly string BE039 = "The xml document is invalid";
        public static readonly string BE040 = "The motor vehicle record is still controlled by the MIB agent.";
        public static readonly string BE041 = "The motor vehicle ownership was changed after it was reported stolen, scrapped or demolished.";
        public static readonly string BE042 = "This vehicle must be registered by an insurance company.";
        public static readonly string BE043 = "Registration of this vehicle requires authorisation.";
        public static readonly string BE044 = "Another person has been authorised to register this vehicle.";
        public static readonly string BE045 = "No registration/deregistration certificate exists.";
        public static readonly string BE046 = "No valid registration/deregistration certificate or authorisation exists.";
        public static readonly string BE047 = "No valid registration/deregistration certificate but authorisation not possible.";
        public static readonly string BE355 = " For a vehicle in State 05 (Registered - Exempt from licensing), the Vehicle should have been introduced by the logged in MIB, should be a chassis or chassis cab, should never have been licensed and should be dealer stock ";
        public static readonly string BE351 = " Tare not within range ";
        public static readonly string BE295 = " Registration certificate does not exist, has invalid status or is not current ";
        public static readonly string BE296 = " Motor vehicle not found, please ensure that the correct information is entered ";
        public static readonly string BE297 = " Motor vehicle licence has expired ";
        public static readonly string BE298 = " Travel date from may not be in the future ";
        public static readonly string BE299 = " Travel date to may not be in the future ";
        public static readonly string BE300 = " Travel date from must be earlier than travel date to ";
        public static readonly string BE301 = " Applicant is not the current titleholder of the specified motor vehicle ";
        public static readonly string BE302 = " Person specified as the owner is not the current owner of the motor vehicle ";
        public static readonly string BE303 = " Where vehicle owner is a business, proxy or representative information must be entered ";
        public static readonly string BE304 = " Owner must contact registering authority of vehicle due to administration marks ";
        public static readonly string BE305 = " Titleholder must contact registering authority of vehicle due to administration marks ";
        public static readonly string BE306 = " Owner must contact registering authority of vehicle due to police marks or clearance ";
        public static readonly string BE309 = " The chassis number/VIN is required for vehicle identification ";
        public static readonly string BE310 = " The licence number or register number is required for vehicle identification ";
        public static readonly string BE330 = " The user group code and  number does not exist in the FI environment. ";
        public static readonly string BE331 = " The eNatis person identification number configured in the FI environment is not the current alias. ";
        public static readonly string BE332 = " The owner does not reside at the Registering Authority. ";
        public static readonly string BE333 = " The proxy's address details incorrect. Please contact your Registering Authority. ";
        public static readonly string BE326 = " The Proxy details do not match those of person entity stored in the system. ";
        public static readonly string BE328 = " The Representative details do not match those of person entity stored in the system. ";
        public static readonly string BE334 = " The payment date cannot be before the infringement date. ";
        public static readonly string BE312 = " The Licence Card number that was entered was not found for the person identified ";
        public static readonly string BE313 = " The Temporary Licence Number number that was entered was not found for the identified person ";
        public static readonly string BE314 = " The Learners Licence Number number that was entered was not found for the identified person ";
        public static readonly string BE345 = " As at CCYY-MM-DD mm:ss (date and time) the specified driver <IdDocN> with driving licence card <CardN> is not qualified to drive ";
        public static readonly string BE113 = " The Chassis number/VIN may not be the same as that of archived vehicle. ";
        public static readonly string BE352 = " For a vehicle in State 05 (Registered - Exempt from licensing), only the Vehicle Tare and Vehicle Model Number will be allowed to be changed ";
        public static readonly string BE370 = " Infringement status is {0} and is not allowed for this transaction ";
        public static readonly string BE379 = " Transaction not allowed when the infringement is partially paid. ";
        public static readonly string BE380 = " There are no infringement notice numbers for the supplied identification number. ";
        public static readonly string BE361 = " Tare value cannot be 99999 ";
        public static readonly string BE362 = " The entered document control number is not valid for the current title holder. ";
        public static readonly string BE363 = " The entered document control number is not valid for the current ownership. ";
        public static readonly string BE364 = " The change date of this vehicle can not be before the current ownership start date. ";
        public static readonly string BE219 = " Infringement is in pending nominations status. ";
        public static readonly string BE353 = " Issuing authority code must be valid for the identified infringement ";
        public static readonly string BE354 = " Service provider code must be valid for the identified infringement ";
        public static readonly string BE284 = " No authorisation record found for this vehicle ";
        public static readonly string BE357 = " The licence number cannot be verified against the infringement ";
        public static readonly string BE358 = " The licence card number does not match the infringer or infringement number captured ";
        public static readonly string BE359 = " The Vehicle VIN/Chassis number and the Register number do not belong to the same vehicle that matches the licence number ";
        public static readonly string BE360 = " Backdating of payments is not allowed ";
        public static readonly string BE356 = " Person has {0} or more unpaid infringements. Please use more specific search options ";
        public static readonly string BE311 = " Transaction not allowed, pending court case ";
        public static readonly string BE316 = " Date to must not be more than 24 months in the future ";
        public static readonly string BE317 = " If the new and current title holder is a Financial Institution and the new owner is a Financial Institution the reason for registration can either be Ownership or Repossessed. ";
        public static readonly string BE308 = " The Financial Institution may not register the vehicle online, if the liability date for registration is after the FIRegLicMVD and the current title holder is not equal to the logged in Financial Institution, then a notification of change of title holder is required. ";
        public static readonly string BE319 = " The capture from date must not be greater than system date ";
        public static readonly string BE320 = " The capture to date must not be greater than system date. ";
        public static readonly string BE321 = " The capture to date must not be earlier than capture from date. ";
        public static readonly string BE322 = " The result size can be any number greater than 0 ";
        public static readonly string BE318 = " The user group number must exist in the fleet user's environment. ";
        public static readonly string BE315 = " Infringement must belong to Fleet Owner. ";
        public static readonly string BE325 = " Nomination of driver not allowed on an infringement where the Nom_Allowed indicator is set to No. ";
        public static readonly string BE323 = " The entered driver licence card number must be valid for the selected person entity. ";
        public static readonly string BE324 = " Unknown error. ";
        public static readonly string BE376 = " Microdot PIN entered does not exist. ";
        public static readonly string BE377 = " Microdot PIN and VIN Number combination differ to current records refer to Microdot Supplier ";
        public static readonly string BE378 = " Microdot VIN entered does not exist ";
        public static readonly string BE381 = " The infringement must be payable at a registering authority, driving licence testing station or issuing authority. ";
        public static readonly string BE181 = " The MIB may only query a motor vehicle introduced by itself ";
        public static readonly string BE382 = " The entered GPS Coordinates must be 9 characters long ";
        public static readonly string BE221 = " Age of person may not be changed to be less than 17 years ";
        public static readonly string BE222 = " Age of person may not be changed to be less than 18 years ";
        public static readonly string BE223 = " Age of person may not be changed to be less than 21 years ";
        public static readonly string BE224 = " Age of person may not be changed to be less than 25 years ";
        public static readonly string BE225 = " Date of birth is invalid ";
        public static readonly string BE226 = " Age of person may not be changed to be less than 16 years ";
        public static readonly string BE227 = " The entered value must be valid for the selected person entity ";
        public static readonly string BE228 = " Control Number must be 12 digits ";
        public static readonly string BE229 = " Invalid Document Control Number ";
        public static readonly string BE230 = " Invalid Document Status ";
        public static readonly string BE231 = " Invalid number entered. Contact your Registering Authority ";
        public static readonly string BE293 = " Microdot PIN is required ";
        public static readonly string BE294 = " Microdot PIN must be less than or equal to 17 characters long ";
        public static readonly string BE171 = " Supply at least one of the identifiers ";
        public static readonly string BE172 = " MIB introduced motor vehicle which still requires police clearance must have chassis starting with 'AAP' ";
        public static readonly string BE173 = " MIB introduced motor vehicle with 'AAP' format chassis is in invalid state for update ";
        public static readonly string BE175 = " Motor vehicle record is marked ";
        public static readonly string BE176 = " Motor vehicle owner record is marked ";
        public static readonly string BE177 = " Motor vehicle title holder record is marked ";
        public static readonly string BE344 = " The applicant is not the infringer ";
        public static readonly string BE389 = "Incorrect username and password.";
        public static readonly string BE390 = "The Identity type code is required.";
        public static readonly string BE391 = "The Identification document number is required.";
        public static readonly string BE383 = "The vehicle is not in a roadworthy state and is not exempted from roadworthiness.";
        public static readonly string BE384 = "The applicant may not have outstanding enforcement orders/warrants of arrest.";
        public static readonly string BE385 = "The user/owner has more than 20 vehicles that are due for renewal. Consider using the Bulk license renewal portal.";
        
    }
}