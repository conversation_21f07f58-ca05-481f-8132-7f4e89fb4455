using System.Xml.Serialization;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.Envelope
{

        [XmlRoot(ElementName = "Envelope", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class Envelope
        {
            [XmlElement(ElementName = "Header", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
            public Header Header { get; set; }

            [XmlElement(ElementName = "Body", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
            public Body Body { get; set; }
        }

        public class Header
        {
            [XmlElement(ElementName = "Security", Namespace = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd")]
            public Security Security { get; set; }
        }

        public class Security
        {
            [XmlElement(ElementName = "UsernameToken", Namespace = "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd")]
            public UsernameToken UsernameToken { get; set; }
        }

        public class UsernameToken
        {
            [XmlElement(ElementName = "Username")]
            public string Username { get; set; }

            [XmlElement(ElementName = "Password")]
            public Password Password { get; set; }
        }

        public class Password
        {
            [XmlAttribute(AttributeName = "Type")]
            public string Type { get; set; }

            [XmlText]
            public string Value { get; set; }
        }

        [XmlRoot(ElementName = "Body", Namespace = "http://schemas.xmlsoap.org/soap/envelope/")]
        public class Body
        {
            [XmlElement(ElementName = "X3003Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3003Request X3003Request { get; set; }

            [XmlElement(ElementName = "X3067Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3067Request X3067Request { get; set; }

            [XmlElement(ElementName = "X3042Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3042Request X3042Request { get; set; }

            [XmlElement(ElementName = "X3004Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3004Request X3004Request { get; set; }

            [XmlElement(ElementName = "X3141Request", Namespace = "http://tasima/common/ws/schema/")]
            public X3141Request X3141Request { get; set; }

            [XmlElement(ElementName = "X314ARequest", Namespace = "http://tasima/common/ws/schema/")]
            public X314ARequest X314ARequest { get; set; }
        }

        public class X3003Request
        {
            [XmlElement(ElementName = "RegisterNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string RegisterNumber { get; set; }

            [XmlElement(ElementName = "VinOrChassis", Namespace = "http://tasima/common/ws/schema/")]
            public string VinOrChassis { get; set; }

            [XmlElement(ElementName = "LicenceNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string LicenceNumber { get; set; }

            [XmlElement(ElementName = "EngineNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string EngineNumber { get; set; }
        }

        public class X3067Request
        {
            public VehicleQuery VehicleQuery { get; set; }
        }

        public class X3042Request
        {
            [XmlElement(ElementName = "idDocumentTypeCode", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentTypeCode { get; set; }

            [XmlElement(ElementName = "idDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class X3004Request
        {
            public person person { get; set; }

            public vehicle vehicle { get; set; }
        }

        public class X3141Request
        {
            public Owner Owner { get; set; }
            public OwnerProxy OwnerProxy { get; set; }
            public OwnerRepresentative OwnerRepresentative { get; set; }
            public Vehicle Vehicle { get; set; }
        }

        public class X314ARequest
        {
            public Receiver Receiver { get; set; }
            public Vehicle Vehicle { get; set; }

            [XmlElement(ElementName = "ChangeDate", Namespace = "http://tasima/common/ws/schema/")]
            public string ChangeDate { get; set; }
            public ReceiverDetails ReceiverDetails { get; set; }
        }

        public class VehicleQuery
        {
            [XmlElement(ElementName = "RegisterNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string RegisterNumber { get; set; }

            [XmlElement(ElementName = "VinOrChassis", Namespace = "http://tasima/common/ws/schema/")]
            public string VinOrChassis { get; set; }

            [XmlElement(ElementName = "LicenceNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string LicenceNumber { get; set; }

            [XmlElement(ElementName = "EngineNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string EngineNumber { get; set; }
        }

        public class person
        {
            [XmlElement(ElementName = "idDocumentTypeCode", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentTypeCode { get; set; }

            [XmlElement(ElementName = "idDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class Owner
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class vehicle
        {
            [XmlElement(ElementName = "vinOrChassis", Namespace = "http://tasima/common/ws/schema/")]
            public string vinOrChassis { get; set; }

            [XmlElement(ElementName = "registerNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string registerNumber { get; set; }

            [XmlElement(ElementName = "licenceNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string licenceNumber { get; set; }
        }

        public class Vehicle
        {
            [XmlElement(ElementName = "RegisterNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string RegisterNumber { get; set; }

            [XmlElement(ElementName = "VinOrChassis", Namespace = "http://tasima/common/ws/schema/")]
            public string VinOrChassis { get; set; }

            [XmlElement(ElementName = "LicenceNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string LicenceNumber { get; set; }

            [XmlElement(ElementName = "RegistrationLiabiltyDate", Namespace = "http://tasima/common/ws/schema/")]
            public string RegistrationLiabiltyDate { get; set; }

            [XmlElement(ElementName = "NatureOfOwnership", Namespace = "http://tasima/common/ws/schema/")]
            public string NatureOfOwnership { get; set; }

            [XmlElement(ElementName = "VehicleUsage", Namespace = "http://tasima/common/ws/schema/")]
            public string VehicleUsage { get; set; }

            [XmlElement(ElementName = "VehicleCertificateNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string VehicleCertificateNumber { get; set; }

            [XmlElement(ElementName = "RegistrationReason", Namespace = "http://tasima/common/ws/schema/")]
            public string RegistrationReason { get; set; }

            [XmlElement(ElementName = "ControlNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string ControlNumber { get; set; }
        }

        public class OwnerProxy
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class OwnerRepresentative
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class Receiver
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class ReceiverDetails
        {
            [XmlElement(ElementName = "Proxy", Namespace = "http://tasima/common/ws/schema/")]
            public Proxy Proxy { get; set; }

            [XmlElement(ElementName = "Representative", Namespace = "http://tasima/common/ws/schema/")]
            public Representative Representative { get; set; }
        }

        public class Proxy
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

        public class Representative
        {
            [XmlElement(ElementName = "IdDocumentType", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentType { get; set; }

            [XmlElement(ElementName = "IdDocumentNumber", Namespace = "http://tasima/common/ws/schema/")]
            public string IdDocumentNumber { get; set; }
        }

}
