using System;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions
{
    public static class eNatisServiceExtension
    {
        /// <summary>
        /// Registers the ENatis integration services in the Microsoft.Extensions.DependencyInjection.IServiceCollection.
        /// </summary>
        /// <param name="services">The Microsoft.Extensions.DependencyInjection.IServiceCollection to add services to.</param>
        /// <param name="configuration">Application configuration properties.</param>
        /// <returns>The same service collection so that multiple calls can be chained.</returns>
        public static IServiceCollection AddEnatis(this IServiceCollection services, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
        {

            services.AddAutoMapper(Assembly.GetExecutingAssembly());
            services.Configure<eNatisServiceOptions>(configuration.GetSection("EnatisSettings"));
            services.Configure<eNatisEndPointOptions>(configuration.GetSection("EnatisEndPoints"));
            services.Configure<eNatisBRNOptions>(configuration.GetSection("BRNSettings"));
            services.Configure<string>(configuration.GetSection("TokenKey"));

            //Build the Client Configuration for ENatis
            services.AddHttpClient<INatisIntegrationService, NatisIntegrationService>()
                .ConfigureHttpClient((serviceProvider, client) =>
                {
                    var options = serviceProvider.GetRequiredService<IOptionsMonitor<eNatisServiceOptions>>().CurrentValue;
                    client.BaseAddress = new Uri(options.RESTBaseUrl);
                    client.Timeout = TimeSpan.FromSeconds(20);
                    client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));

                })
                .ConfigurePrimaryHttpMessageHandler(() => {

                var handler = new HttpClientHandler();

                    handler.ServerCertificateCustomValidationCallback = (message, cert, chain, sslPolicyErrors) =>
                    {
                        return true;
                    };

                  return handler;

                });
                ;

            return services;
        }
    }
}
