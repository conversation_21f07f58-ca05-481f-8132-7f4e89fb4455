namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations
{
    public class eNatisEndPointOptions
    {
        public string GetVehicle { get; set; }

        public string GetVehicleDetailedInformation { get; set; }

        public string GetOwnerTitleHolderConfirmation { get; set; }

        public string VehicleOwnerRegistration { get; set; }

        public string GetDriver { get; set; }

        public string OwnerTitleHolderTransfer { get; set; }

        public string RESTToken { get; set; }

        public string GetAllOwners { get; set; }

        public string ControlNumberVerification { get; set; }

        public string FeeCalculator { get; set; }

        public string GetVehiclesAndLicenseExpiryDates { get; set; }
        
        public string GetVehiclesQuotationRequest { get; set; }
        
        public string InitiateRenewalRequest { get; set; }
        
        public string CompleteRenewalRequest { get; set; }
    }
}