<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <LangVersion>10.0</LangVersion>
    <TargetFramework>net8.0</TargetFramework>
    <!-- <TargetFramework>netcoreapp6.0</TargetFramework> -->
    <RootNamespace>WeBuyCars.eNatis.RTMC.Infrastructure.Data</RootNamespace>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <noWarn>1591</noWarn>
  </PropertyGroup>



  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Http.Abstractions" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.8" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <!-- <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="3.1.1" /> -->
        <!-- <PackageReference Include="Microsoft.EntityFrameworkCore" Version="3.1.1" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="3.1.1"> -->
    <!-- <PackageReference Include="Telerik.DataSource" Version="1.0.0" />
    <PackageReference Include="Telerik.UI.for.AspNet.Core" Version="2019.2.619" /> -->
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WeBuyCars.eNatis.RTMC.Core\WeBuyCars.eNatis.RTMC.Core.csproj" />
  </ItemGroup>

</Project>
