using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.ReadonlyEntities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.ReadonlyEntityConfigurations
{
    public class OnlineTransactionRecordEntityTypeConfiguration: IEntityTypeConfiguration<OnlineTransactionReportRecord>
    {
        public void Configure(EntityTypeBuilder<OnlineTransactionReportRecord> builder)
        {
            builder.HasNoKey();
            builder.ToView(null);
        }
    }
}