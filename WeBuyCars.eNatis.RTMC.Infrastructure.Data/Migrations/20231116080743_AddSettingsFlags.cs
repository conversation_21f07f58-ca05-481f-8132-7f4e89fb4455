using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddSettingsFlags : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "CanOnlineReg",
                schema: "RTMC",
                table: "Settings",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "CanOnlineRelease",
                schema: "RTMC",
                table: "Settings",
                type: "bit",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "HasLogin",
                schema: "RTMC",
                table: "Settings",
                type: "bit",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CanOnlineReg",
                schema: "RTMC",
                table: "Settings");

            migrationBuilder.DropColumn(
                name: "CanOnlineRelease",
                schema: "RTMC",
                table: "Settings");

            migrationBuilder.DropColumn(
                name: "HasLog<PERSON>",
                schema: "RTMC",
                table: "Settings");
        }
    }
}
