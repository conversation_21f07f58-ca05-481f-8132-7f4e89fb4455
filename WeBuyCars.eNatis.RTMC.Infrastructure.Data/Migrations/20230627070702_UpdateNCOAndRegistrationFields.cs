using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class UpdateNCOAndRegistrationFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "Barcode",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");

            migrationBuilder.DropColumn(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");

            migrationBuilder.AddColumn<byte[]>(
                name: "EncryptedVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "varbinary(512)",
                maxLength: 512,
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "EncryptedBarcode",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "varbinary(512)",
                maxLength: 512,
                nullable: false,
                defaultValue: new byte[0]);

            migrationBuilder.AddColumn<byte[]>(
                name: "EncryptedVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "varbinary(512)",
                maxLength: 512,
                nullable: false,
                defaultValue: new byte[0]);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EncryptedVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "EncryptedBarcode",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");

            migrationBuilder.DropColumn(
                name: "EncryptedVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");

            migrationBuilder.AddColumn<string>(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Barcode",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
