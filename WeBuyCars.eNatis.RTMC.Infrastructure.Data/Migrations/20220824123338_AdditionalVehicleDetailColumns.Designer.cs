// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    [DbContext(typeof(RTMCContext))]
    [Migration("20220824123338_AdditionalVehicleDetailColumns")]
    partial class AdditionalVehicleDetailColumns
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("RTMC")
                .HasAnnotation("ProductVersion", "6.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder, 1L, 1);

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.Audit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("AuditDateTimeUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("KeyValues")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("User")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.HasKey("Id");

                    b.ToTable("Audit", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.CustomProperty", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyValue")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CustomProperty", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCDriverInformationDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("Age")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("BirthDate")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("BusinessOrSurname")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DrivingLicenceNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DrivingLicenceTypeCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("DrivingLicenceTypeDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("DrivingLicenceValidFrom")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DrivingLicencedateOfFirstIssue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IdDocumentNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("IdDocumentTypeCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("IdDocumentTypeDescription")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Initials")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("LearnerCertificateNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LearnerLicenceSpeciallyAdaptedVehicleRequired")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("LearnerLicenceStatusCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LearnerLicenceStatusDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("LearnerLicenceTypeCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LearnerLicenceTypeDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("LearnerLicenceValidFrom")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LicenceAuthorisationDate")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LicenceCardcardIssueNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LicenceCarddateOfFirstIssue")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LicenceCardvalidTo")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("LicenceRestrictionCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LicenceRestrictionDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NatureOfPersonCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("NatureOfPersonDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PopulationGroupCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("PopulationGroupDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ProfessionalDangerousGoodsCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalDangerousGoodsCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalDateAuthorised")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalGoodsCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalGoodsCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalPassengerCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalPassengerCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryXCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryXDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryYCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryYDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSuspendedFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSuspendedToDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalvalidFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleRestrictionCode")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("VehicleRestrictionDescription")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.ToTable("DriverInformationDetail", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCRequest", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("Date")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("IntegrationType")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Locality")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NetworkAddress")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("RequestObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("User")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkStation")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("Request", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCResponse", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("Date")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NatisResponseObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("ReqId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResponseObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResponseStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Response", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCVehicleDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("AxlesDriven")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AxlesTotal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CapacitySitting")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CapacityStanding")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfExportCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfExportDescripiton")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfImportCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CountryOfImportDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("DataOwnerCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DataOwnerDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DescriptionCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DescriptionDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DifferentialNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DrivenCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DrivenDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EconomicSectorCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EconomicSectorDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngineDisplacement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngineNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExaminerName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExaminerNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExemptionCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ExemptionDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FirstLicensingDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelTypeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GVM")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GearboxNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceChangeDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceFee")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceLiabilityDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LifeStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LifeStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainColourCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainColourDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MakeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MakeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelNameCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelNameDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NetPower")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NoOfWheels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallHeight")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallLength")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallWidth")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Overdue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrePreviousLicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviousLicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviousVehicleCertificateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegAuthorityOfLicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegAuthorityOfLicensing")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationQualifier")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationQualifierDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationTypeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadUseIndicator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyTestDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceReasonCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceReasonDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Timestamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransmissionCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TransmissionDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleCertificateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleUsageCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleUsageDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VinOrChassis")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VtsName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VtsNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("VehicleDetail", "RTMC");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCVehicleOwnerVerificationDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    SqlServerPropertyBuilderExtensions.UseIdentityColumn(b.Property<long>("Id"), 1L, 1);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OwnerConfirmation")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("OwnerInitials")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("OwnerSurnameofBusiness")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("PersonDocumentNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("PersonDocumentTypeCode")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("PersonDocumentTypeDescription")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TitleHolderConfirmation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("VehicleLicenseNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("VehicleRegistrationNumber")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("VehicleVinorChassis")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("VehicleOwnerVerificationDetail", "RTMC");
                });
#pragma warning restore 612, 618
        }
    }
}
