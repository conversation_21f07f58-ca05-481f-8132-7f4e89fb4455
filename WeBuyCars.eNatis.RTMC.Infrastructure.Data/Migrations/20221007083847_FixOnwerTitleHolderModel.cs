using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class FixOnwerTitleHolderModel : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Age",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "BirthDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "BusinessOrSurname",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceValidFrom",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicencedateOfFirstIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "IdDocumentNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "IdDocumentTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "Initials",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LearnerCertificateNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceSpeciallyAdaptedVehicleRequired",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceStatusDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceValidFrom",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LicenceAuthorisationDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCardcardIssueNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCarddateOfFirstIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCardvalidTo",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "LicenceRestrictionDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "NatureOfPersonDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "PopulationGroupDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDangerousGoodsCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDangerousGoodsCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDateAuthorised",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalExpiryDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalGoodsCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalGoodsCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalPassengerCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalPassengerCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalSpareCategoryXCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalSpareCategoryXDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalSpareCategoryYCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalSpareCategoryYDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalSuspendedFromDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VehicleRestrictionDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.RenameColumn(
                name: "VehicleRestrictionCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "UserGroupCode");

            migrationBuilder.RenameColumn(
                name: "ProfessionalvalidFromDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "WaterMark");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSuspendedToDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "Barcode");

            migrationBuilder.RenameColumn(
                name: "PopulationGroupCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "SellerIdDocumentNumber");

            migrationBuilder.RenameColumn(
                name: "NatureOfPersonCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "RegisterNumber");

            migrationBuilder.RenameColumn(
                name: "LicenceRestrictionCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "PurchaserIdDocumentNumber");

            migrationBuilder.RenameColumn(
                name: "LearnerLicenceTypeCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "IssueDate");

            migrationBuilder.RenameColumn(
                name: "LearnerLicenceStatusCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "FirstRegistrationLiabilityDate");

            migrationBuilder.RenameColumn(
                name: "IdDocumentTypeCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "FirstLicenceLiabilityDate");

            migrationBuilder.RenameColumn(
                name: "DrivingLicenceTypeCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "DateAndTime");

            migrationBuilder.AddColumn<string>(
                name: "ConvenienceFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Driven",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EngineNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IssuedBy",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Make",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ModelName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PaymentReferenceNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PurchaserCountryOfIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PurchaserIdDocumentType",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PurchaserName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthority",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SellerCountryOfIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SellerIdDocumentType",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SellerName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Tare",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(10)",
                maxLength: 10,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TotalRegistrationFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleCategory",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleLifeStatus",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(60)",
                maxLength: 60,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VinOrChassis",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(40)",
                maxLength: 40,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ConvenienceFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "Driven",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "EngineNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "IssuedBy",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "Make",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "ModelName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "PaymentReferenceNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "PurchaserCountryOfIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "PurchaserIdDocumentType",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "PurchaserName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthority",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "RegistrationFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "SellerCountryOfIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "SellerIdDocumentType",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "SellerName",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "Tare",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "TotalRegistrationFeeAmount",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VehicleCategory",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VehicleDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VehicleLifeStatus",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.DropColumn(
                name: "VinOrChassis",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail");

            migrationBuilder.RenameColumn(
                name: "WaterMark",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "ProfessionalvalidFromDate");

            migrationBuilder.RenameColumn(
                name: "UserGroupCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "VehicleRestrictionCode");

            migrationBuilder.RenameColumn(
                name: "SellerIdDocumentNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "PopulationGroupCode");

            migrationBuilder.RenameColumn(
                name: "RegisterNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "NatureOfPersonCode");

            migrationBuilder.RenameColumn(
                name: "PurchaserIdDocumentNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "LicenceRestrictionCode");

            migrationBuilder.RenameColumn(
                name: "IssueDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "LearnerLicenceTypeCode");

            migrationBuilder.RenameColumn(
                name: "FirstRegistrationLiabilityDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "LearnerLicenceStatusCode");

            migrationBuilder.RenameColumn(
                name: "FirstLicenceLiabilityDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "IdDocumentTypeCode");

            migrationBuilder.RenameColumn(
                name: "DateAndTime",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "DrivingLicenceTypeCode");

            migrationBuilder.RenameColumn(
                name: "Barcode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                newName: "ProfessionalSuspendedToDate");

            migrationBuilder.AddColumn<string>(
                name: "Age",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BirthDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessOrSurname",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceValidFrom",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicencedateOfFirstIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdDocumentNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdDocumentTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Initials",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerCertificateNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceSpeciallyAdaptedVehicleRequired",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceStatusDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceTypeDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceValidFrom",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceAuthorisationDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCardcardIssueNumber",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCarddateOfFirstIssue",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCardvalidTo",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceRestrictionDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NatureOfPersonDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PopulationGroupDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDangerousGoodsCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDangerousGoodsCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDateAuthorised",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalExpiryDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalGoodsCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalGoodsCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalPassengerCategoryCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalPassengerCategoryDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalSpareCategoryXCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalSpareCategoryXDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalSpareCategoryYCode",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalSpareCategoryYDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalSuspendedFromDate",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleRestrictionDescription",
                schema: "RTMC",
                table: "OwnerTitleHolderTransferDetail",
                type: "nvarchar(500)",
                maxLength: 500,
                nullable: true);
        }
    }
}
