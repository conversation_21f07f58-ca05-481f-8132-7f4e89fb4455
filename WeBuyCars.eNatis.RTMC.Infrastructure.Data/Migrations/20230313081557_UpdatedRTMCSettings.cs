using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class UpdatedRTMCSettings : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "OwnerDocumentNumber",
                schema: "RTMC",
                table: "Settings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ProxyDocumentNumber",
                schema: "RTMC",
                table: "Settings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "RepresentativeDocumentNumber",
                schema: "RTMC",
                table: "Settings",
                type: "nvarchar(200)",
                maxLength: 200,
                nullable: false,
                defaultValue: "");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "OwnerDocumentNumber",
                schema: "RTMC",
                table: "Settings");

            migrationBuilder.DropColumn(
                name: "ProxyDocumentNumber",
                schema: "RTMC",
                table: "Settings");

            migrationBuilder.DropColumn(
                name: "RepresentativeDocumentNumber",
                schema: "RTMC",
                table: "Settings");
        }
    }
}
