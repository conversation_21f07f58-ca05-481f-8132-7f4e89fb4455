using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddAdditionalVehicleDetailFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "VehicleOwnerRegistrationDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DrivingLicenceNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceCarddateOfFirstIssue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceCardcardIssueNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceCardvalidTo = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdDocumentTypeCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdDocumentTypeDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IdDocumentNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Initials = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BusinessOrSurname = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    BirthDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Age = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NatureOfPersonCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NatureOfPersonDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PopulationGroupCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PopulationGroupDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceRestrictionCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceRestrictionDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DrivingLicencedateOfFirstIssue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DrivingLicenceTypeCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DrivingLicenceTypeDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LicenceAuthorisationDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DrivingLicenceValidFrom = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleRestrictionCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleRestrictionDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerCertificateNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceTypeCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceTypeDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceStatusCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceStatusDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceValidFrom = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    LearnerLicenceSpeciallyAdaptedVehicleRequired = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalDateAuthorised = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalDangerousGoodsCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalDangerousGoodsCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalGoodsCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalGoodsCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalPassengerCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalPassengerCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryXCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryXDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryYCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryYDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalvalidFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSuspendedFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSuspendedToDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleOwnerRegistrationDetail", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VehicleOwnerRegistrationDetail",
                schema: "RTMC");
        }
    }
}
