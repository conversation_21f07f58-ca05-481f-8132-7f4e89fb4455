using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class ChangeVehicleOwnerRegistrationTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Age",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "BirthDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "BusinessOrSurname",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicenceValidFrom",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "DrivingLicencedateOfFirstIssue",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "IdDocumentNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "IdDocumentTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "IdDocumentTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "Initials",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceSpeciallyAdaptedVehicleRequired",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceStatusCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceStatusDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LearnerLicenceValidFrom",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceAuthorisationDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCardcardIssueNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCarddateOfFirstIssue",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceCardvalidTo",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceRestrictionCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "LicenceRestrictionDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "NatureOfPersonCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "NatureOfPersonDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "PopulationGroupCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "PopulationGroupDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDangerousGoodsCategoryCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDangerousGoodsCategoryDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalDateAuthorised",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalExpiryDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalGoodsCategoryCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ProfessionalGoodsCategoryDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.RenameColumn(
                name: "VehicleRestrictionDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "VinOrChassis");

            migrationBuilder.RenameColumn(
                name: "VehicleRestrictionCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "VehicleCertificateNumber");

            migrationBuilder.RenameColumn(
                name: "ProfessionalvalidFromDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "TotalRegistrationFeeAmount");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSuspendedToDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "TitleHolderIdDocumentType");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSuspendedFromDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "TitleHolderIdDocumentNumber");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSpareCategoryYDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "RegistrationFeeAmount");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSpareCategoryYCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "RegisterNumber");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSpareCategoryXDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "PaymentReferenceNumber");

            migrationBuilder.RenameColumn(
                name: "ProfessionalSpareCategoryXCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "OwnerIdDocumentType");

            migrationBuilder.RenameColumn(
                name: "ProfessionalPassengerCategoryDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "OwnerIdDocumentNumber");

            migrationBuilder.RenameColumn(
                name: "ProfessionalPassengerCategoryCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ConvenienceFeeAmount");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "VinOrChassis",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "VehicleRestrictionDescription");

            migrationBuilder.RenameColumn(
                name: "VehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "VehicleRestrictionCode");

            migrationBuilder.RenameColumn(
                name: "TotalRegistrationFeeAmount",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalvalidFromDate");

            migrationBuilder.RenameColumn(
                name: "TitleHolderIdDocumentType",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSuspendedToDate");

            migrationBuilder.RenameColumn(
                name: "TitleHolderIdDocumentNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSuspendedFromDate");

            migrationBuilder.RenameColumn(
                name: "RegistrationFeeAmount",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSpareCategoryYDescription");

            migrationBuilder.RenameColumn(
                name: "RegisterNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSpareCategoryYCode");

            migrationBuilder.RenameColumn(
                name: "PaymentReferenceNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSpareCategoryXDescription");

            migrationBuilder.RenameColumn(
                name: "OwnerIdDocumentType",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalSpareCategoryXCode");

            migrationBuilder.RenameColumn(
                name: "OwnerIdDocumentNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalPassengerCategoryDescription");

            migrationBuilder.RenameColumn(
                name: "ConvenienceFeeAmount",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                newName: "ProfessionalPassengerCategoryCode");

            migrationBuilder.AddColumn<string>(
                name: "Age",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BirthDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BusinessOrSurname",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicenceValidFrom",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DrivingLicencedateOfFirstIssue",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdDocumentNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdDocumentTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "IdDocumentTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Initials",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerCertificateNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceSpeciallyAdaptedVehicleRequired",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceStatusCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceStatusDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceTypeCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceTypeDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LearnerLicenceValidFrom",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceAuthorisationDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCardcardIssueNumber",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCarddateOfFirstIssue",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceCardvalidTo",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceRestrictionCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceRestrictionDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NatureOfPersonCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "NatureOfPersonDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PopulationGroupCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PopulationGroupDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDangerousGoodsCategoryCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDangerousGoodsCategoryDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalDateAuthorised",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalExpiryDate",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalGoodsCategoryCode",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ProfessionalGoodsCategoryDescription",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
