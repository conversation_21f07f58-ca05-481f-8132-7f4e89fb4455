// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    [DbContext(typeof(RTMCContext))]
    [Migration("20220817154839_FixDriverInformationColumns")]
    partial class FixDriverInformationColumns
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("RTMC")
                .HasAnnotation("ProductVersion", "3.1.1")
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.Audit", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTimeOffset>("AuditDateTimeUtc")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("KeyValues")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("NewValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OldValues")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TableName")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("User")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.HasKey("Id");

                    b.ToTable("Audit");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.CustomProperty", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PropertyValue")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CustomProperty");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCDriverInformationDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("Age")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<string>("BirthDate")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("BusinessOrSurname")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DrivingLicenceNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("DrivingLicenceTypeCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("DrivingLicenceTypeDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("DrivingLicenceValidFrom")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("DrivingLicencedateOfFirstIssue")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("IdDocumentNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("IdDocumentTypeCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("IdDocumentTypeDescription")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("Initials")
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<string>("LearnerCertificateNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LearnerLicenceSpeciallyAdaptedVehicleRequired")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("LearnerLicenceStatusCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("LearnerLicenceStatusDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("LearnerLicenceTypeCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("LearnerLicenceTypeDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("LearnerLicenceValidFrom")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LicenceAuthorisationDate")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LicenceCardcardIssueNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LicenceCarddateOfFirstIssue")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LicenceCardvalidTo")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("LicenceRestrictionCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("LicenceRestrictionDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NatureOfPersonCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("NatureOfPersonDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("PopulationGroupCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("PopulationGroupDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("ProfessionalDangerousGoodsCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalDangerousGoodsCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalDateAuthorised")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalGoodsCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalGoodsCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalPassengerCategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalPassengerCategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryXCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryXDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryYCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSpareCategoryYDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSuspendedFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalSuspendedToDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ProfessionalvalidFromDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleRestrictionCode")
                        .HasColumnType("nvarchar(20)")
                        .HasMaxLength(20);

                    b.Property<string>("VehicleRestrictionDescription")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.HasKey("Id");

                    b.ToTable("DriverInformationDetail");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCRequest", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("Date")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("IntegrationType")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("Locality")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NetworkAddress")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)")
                        .HasMaxLength(50);

                    b.Property<string>("RequestObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("User")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("WorkStation")
                        .IsRequired()
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.HasKey("Id");

                    b.ToTable("Request");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCResponse", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<DateTimeOffset>("Date")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NatisResponseObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<long>("ReqId")
                        .HasColumnType("bigint");

                    b.Property<string>("ResponseObject")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ResponseStatus")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Response");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCVehicleDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("AxlesDriven")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AxlesTotal")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CategoryCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CategoryDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("DescriptionCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DescriptionDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DrivenCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("DrivenDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngineDisplacement")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EngineNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FuelTypeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("GVM")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceChangeDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceExpiryDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LifeStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("LifeStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainColourCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MainColourDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MakeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MakeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelNameCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModelNameDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NetPower")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NoOfWheels")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallHeight")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallLength")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("OverallWidth")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PrePreviousLicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PreviousLicenceNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegisterNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationTypeCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RegistrationTypeDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoadworthyTestDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceStatusCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapClearanceStatusDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("SapMarkDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Tare")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleCertificateNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateDate")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VehicleStateDescription")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("VinOrChassis")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("VehicleDetail");
                });

            modelBuilder.Entity("WeBuyCars.eNatis.RTMC.Core.Entities.RTMCVehicleOwnerVerificationDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("CreatedBy")
                        .IsRequired()
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset>("CreatedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<bool>("Deleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiedBy")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<DateTimeOffset?>("ModifiedOn")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("OwnerConfirmation")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("OwnerInitials")
                        .HasColumnType("nvarchar(200)")
                        .HasMaxLength(200);

                    b.Property<string>("OwnerSurnameofBusiness")
                        .HasColumnType("nvarchar(500)")
                        .HasMaxLength(500);

                    b.Property<string>("PersonDocumentNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("PersonDocumentTypeCode")
                        .HasColumnType("nvarchar(10)")
                        .HasMaxLength(10);

                    b.Property<string>("PersonDocumentTypeDescription")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("TitleHolderConfirmation")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("VehicleLicenseNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("VehicleRegistrationNumber")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.Property<string>("VehicleVinorChassis")
                        .HasColumnType("nvarchar(100)")
                        .HasMaxLength(100);

                    b.HasKey("Id");

                    b.ToTable("VehicleOwnerVerificationDetail");
                });
#pragma warning restore 612, 618
        }
    }
}
