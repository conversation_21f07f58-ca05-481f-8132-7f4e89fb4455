using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddClientMessageId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "ClientMessageId",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.AddColumn<Guid>(
                name: "ClientMessageId",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "uniqueidentifier",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "ClientMessageId",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "ClientMessageId",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");
        }
    }
}
