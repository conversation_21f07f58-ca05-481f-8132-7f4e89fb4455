using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddRTMCSettings : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "OwnerTitleHolderTransferDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    DrivingLicenceNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LicenceCarddateOfFirstIssue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LicenceCardcardIssueNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LicenceCardvalidTo = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    IdDocumentTypeCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IdDocumentTypeDescription = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IdDocumentNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Initials = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    BusinessOrSurname = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    BirthDate = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Age = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    NatureOfPersonCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    NatureOfPersonDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    PopulationGroupCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PopulationGroupDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    LicenceRestrictionCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LicenceRestrictionDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DrivingLicencedateOfFirstIssue = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DrivingLicenceTypeCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    DrivingLicenceTypeDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    LicenceAuthorisationDate = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DrivingLicenceValidFrom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    VehicleRestrictionCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    VehicleRestrictionDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    LearnerCertificateNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LearnerLicenceTypeCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LearnerLicenceTypeDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    LearnerLicenceStatusCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    LearnerLicenceStatusDescription = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    LearnerLicenceValidFrom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    LearnerLicenceSpeciallyAdaptedVehicleRequired = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ProfessionalDateAuthorised = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalDangerousGoodsCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalDangerousGoodsCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalGoodsCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalGoodsCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalPassengerCategoryCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalPassengerCategoryDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryXCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryXDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryYCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSpareCategoryYDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalvalidFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalExpiryDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSuspendedFromDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ProfessionalSuspendedToDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OwnerTitleHolderTransferDetail", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Settings",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    BusinessRegistrationNumber = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Username = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Password = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Settings", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OwnerTitleHolderTransferDetail",
                schema: "RTMC");

            migrationBuilder.DropTable(
                name: "Settings",
                schema: "RTMC");
        }
    }
}
