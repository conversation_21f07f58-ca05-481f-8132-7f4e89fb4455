using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class RectifyNCOFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PurchaserIdDocument",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "PurchaserName");

            migrationBuilder.RenameColumn(
                name: "FirstRegistrationLiabiltyDate",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "FirstRegistrationLiabilityDate");

            migrationBuilder.RenameColumn(
                name: "FirstLicenceLiabiltyDate",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "FirstLicenceLiabilityDate");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "PurchaserName",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "PurchaserIdDocument");

            migrationBuilder.RenameColumn(
                name: "FirstRegistrationLiabilityDate",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "FirstRegistrationLiabiltyDate");

            migrationBuilder.RenameColumn(
                name: "FirstLicenceLiabilityDate",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                newName: "FirstLicenceLiabiltyDate");
        }
    }
}
