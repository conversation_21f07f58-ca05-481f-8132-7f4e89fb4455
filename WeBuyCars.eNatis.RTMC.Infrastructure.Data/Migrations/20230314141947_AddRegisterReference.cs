using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddRegisterReference : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "Reference",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleOwnerRegistrationDetail_Reference",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail",
                column: "Reference",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VehicleOwnerRegistrationDetail_Reference",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");

            migrationBuilder.DropColumn(
                name: "Reference",
                schema: "RTMC",
                table: "VehicleOwnerRegistrationDetail");
        }
    }
}
