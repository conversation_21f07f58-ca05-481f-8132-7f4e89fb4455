using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class FixedRegQualifierAuthLicenceColumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "RegistrationQualifier",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegistrationQualifierDescription");

            migrationBuilder.RenameColumn(
                name: "RegAuthorityOfLicensing",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegistrationQualifierCode");

            migrationBuilder.RenameColumn(
                name: "RegAuthorityOfLicenceNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegAuthorityOfLicensingName");

            migrationBuilder.AddColumn<string>(
                name: "PrePrePreviousLicenceNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthorityOfLicenceNumberCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthorityOfLicenceNumberName",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthorityOfLicensingCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "PrePrePreviousLicenceNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthorityOfLicenceNumberCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthorityOfLicenceNumberName",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthorityOfLicensingCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.RenameColumn(
                name: "RegistrationQualifierDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegistrationQualifier");

            migrationBuilder.RenameColumn(
                name: "RegistrationQualifierCode",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegAuthorityOfLicensing");

            migrationBuilder.RenameColumn(
                name: "RegAuthorityOfLicensingName",
                schema: "RTMC",
                table: "VehicleDetail",
                newName: "RegAuthorityOfLicenceNumber");
        }
    }
}
