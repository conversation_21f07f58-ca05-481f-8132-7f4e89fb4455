using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class DriverOwnerTableCreate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<string>(
                name: "WorkStation",
                schema: "RTMC",
                table: "Request",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "NetworkAddress",
                schema: "RTMC",
                table: "Request",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AlterColumn<string>(
                name: "Locality",
                schema: "RTMC",
                table: "Request",
                maxLength: 100,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "nvarchar(max)");

            migrationBuilder.AddColumn<string>(
                name: "IntegrationType",
                schema: "RTMC",
                table: "Request",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateTable(
                name: "DriverInformationDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    DrivingLicenceNumber = table.Column<string>(maxLength: 100, nullable: true),
                    LicenceCarddateOfFirstIssue = table.Column<string>(maxLength: 100, nullable: true),
                    LicenceCardcardIssueNumber = table.Column<string>(maxLength: 100, nullable: true),
                    LicenceCardvalidTo = table.Column<string>(maxLength: 10, nullable: true),
                    IdDocumentTypeCode = table.Column<string>(maxLength: 20, nullable: true),
                    IdDocumentTypeDescription = table.Column<string>(maxLength: 200, nullable: true),
                    IdDocumentNumber = table.Column<string>(maxLength: 100, nullable: true),
                    Initials = table.Column<string>(maxLength: 50, nullable: true),
                    BusinessOrSurname = table.Column<string>(maxLength: 200, nullable: true),
                    BirthDate = table.Column<string>(maxLength: 100, nullable: true),
                    Age = table.Column<string>(nullable: true),
                    NatureOfPersonCode = table.Column<string>(maxLength: 20, nullable: true),
                    NatureOfPersonDescription = table.Column<string>(maxLength: 200, nullable: true),
                    PopulationGroupCode = table.Column<string>(maxLength: 20, nullable: true),
                    PopulationGroupDescription = table.Column<string>(maxLength: 200, nullable: true),
                    LicenceRestrictionCode = table.Column<string>(maxLength: 20, nullable: true),
                    LicenceRestrictionDescription = table.Column<string>(maxLength: 200, nullable: true),
                    DrivingLicencedateOfFirstIssue = table.Column<string>(maxLength: 100, nullable: true),
                    DrivingLicenceTypeCode = table.Column<string>(maxLength: 20, nullable: true),
                    DrivingLicenceTypeDescription = table.Column<string>(maxLength: 200, nullable: true),
                    LicenceAuthorisationDate = table.Column<string>(maxLength: 100, nullable: true),
                    DrivingLicenceValidFrom = table.Column<string>(maxLength: 100, nullable: true),
                    VehicleRestrictionCode = table.Column<string>(maxLength: 20, nullable: true),
                    VehicleRestrictionDescription = table.Column<string>(maxLength: 400, nullable: true),
                    LearnerCertificateNumber = table.Column<string>(maxLength: 100, nullable: true),
                    LearnerLicenceTypeCode = table.Column<string>(maxLength: 20, nullable: true),
                    LearnerLicenceTypeDescription = table.Column<string>(maxLength: 400, nullable: true),
                    LearnerLicenceStatusCode = table.Column<string>(maxLength: 20, nullable: true),
                    LearnerLicenceStatusDescription = table.Column<string>(maxLength: 400, nullable: true),
                    LearnerLicenceValidFrom = table.Column<string>(maxLength: 100, nullable: true),
                    LearnerLicenceSpeciallyAdaptedVehicleRequired = table.Column<string>(maxLength: 200, nullable: true),
                    ProfessionalDateAuthorised = table.Column<string>(nullable: true),
                    ProfessionalDangerousGoodsCategoryCode = table.Column<string>(nullable: true),
                    ProfessionalDangerousGoodsCategoryDescription = table.Column<string>(nullable: true),
                    ProfessionalGoodsCategoryCode = table.Column<string>(nullable: true),
                    ProfessionalGoodsCategoryDescription = table.Column<string>(nullable: true),
                    ProfessionalPassengerCategoryCode = table.Column<string>(nullable: true),
                    ProfessionalPassengerCategoryDescription = table.Column<string>(nullable: true),
                    ProfessionalSpareCategoryXCode = table.Column<string>(nullable: true),
                    ProfessionalSpareCategoryXDescription = table.Column<string>(nullable: true),
                    ProfessionalSpareCategoryYCode = table.Column<string>(nullable: true),
                    ProfessionalSpareCategoryYDescription = table.Column<string>(nullable: true),
                    ProfessionalvalidFromDate = table.Column<string>(nullable: true),
                    ProfessionalExpiryDate = table.Column<string>(nullable: true),
                    ProfessionalSuspendedFromDate = table.Column<string>(nullable: true),
                    ProfessionalSuspendedToDate = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DriverInformationDetail", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "VehicleOwnerVerificationDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    PersonDocumentTypeCode = table.Column<string>(maxLength: 10, nullable: true),
                    PersonDocumentTypeDescription = table.Column<string>(maxLength: 100, nullable: true),
                    PersonDocumentNumber = table.Column<string>(maxLength: 100, nullable: true),
                    TitleHolderConfirmation = table.Column<string>(maxLength: 100, nullable: true),
                    OwnerSurnameofBusiness = table.Column<string>(maxLength: 500, nullable: true),
                    OwnerInitials = table.Column<string>(maxLength: 200, nullable: true),
                    OwnerConfirmation = table.Column<string>(maxLength: 200, nullable: true),
                    VehicleRegistrationNumber = table.Column<string>(maxLength: 100, nullable: true),
                    VehicleLicenseNumber = table.Column<string>(maxLength: 100, nullable: true),
                    VehicleVinorChassis = table.Column<string>(maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleOwnerVerificationDetail", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "DriverInformationDetail",
                schema: "RTMC");

            migrationBuilder.DropTable(
                name: "VehicleOwnerVerificationDetail",
                schema: "RTMC");

            migrationBuilder.DropColumn(
                name: "IntegrationType",
                schema: "RTMC",
                table: "Request");

            migrationBuilder.AlterColumn<string>(
                name: "WorkStation",
                schema: "RTMC",
                table: "Request",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldMaxLength: 100);

            migrationBuilder.AlterColumn<string>(
                name: "NetworkAddress",
                schema: "RTMC",
                table: "Request",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<string>(
                name: "Locality",
                schema: "RTMC",
                table: "Request",
                type: "nvarchar(max)",
                nullable: false,
                oldClrType: typeof(string),
                oldMaxLength: 100);
        }
    }
}
