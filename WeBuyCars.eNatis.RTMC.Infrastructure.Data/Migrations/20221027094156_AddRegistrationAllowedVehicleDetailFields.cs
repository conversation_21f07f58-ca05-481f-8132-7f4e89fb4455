using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddRegistrationAllowedVehicleDetailFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AdministrationMarkIndicator",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(1)",
                maxLength: 1,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FirstRegistrationDate",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationAllowed",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(1)",
                maxLength: 1,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdministrationMarkIndicator",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "FirstRegistrationDate",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegistrationAllowed",
                schema: "RTMC",
                table: "VehicleDetail");
        }
    }
}
