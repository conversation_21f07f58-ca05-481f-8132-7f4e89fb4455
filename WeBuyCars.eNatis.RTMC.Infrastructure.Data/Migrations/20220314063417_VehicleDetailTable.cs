using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class VehicleDetailTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "VehicleDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    AxlesDriven = table.Column<string>(nullable: true),
                    AxlesTotal = table.Column<string>(nullable: true),
                    CategoryCode = table.Column<string>(nullable: true),
                    CategoryDescription = table.Column<string>(nullable: true),
                    DescriptionCode = table.Column<string>(nullable: true),
                    DescriptionDescription = table.Column<string>(nullable: true),
                    DrivenCode = table.Column<string>(nullable: true),
                    DrivenDescription = table.Column<string>(nullable: true),
                    EngineDisplacement = table.Column<string>(nullable: true),
                    EngineNumber = table.Column<string>(nullable: true),
                    FuelTypeCode = table.Column<string>(nullable: true),
                    FuelTypeDescription = table.Column<string>(nullable: true),
                    GVM = table.Column<string>(nullable: true),
                    LicenceChangeDate = table.Column<string>(nullable: true),
                    LicenceExpiryDate = table.Column<string>(nullable: true),
                    LicenceNumber = table.Column<string>(nullable: true),
                    LifeStatusCode = table.Column<string>(nullable: true),
                    LifeStatusDescription = table.Column<string>(nullable: true),
                    MainColourCode = table.Column<string>(nullable: true),
                    MainColourDescription = table.Column<string>(nullable: true),
                    MakeCode = table.Column<string>(nullable: true),
                    MakeDescription = table.Column<string>(nullable: true),
                    ModelNameCode = table.Column<string>(nullable: true),
                    ModelNameDescription = table.Column<string>(nullable: true),
                    NetPower = table.Column<string>(nullable: true),
                    NoOfWheels = table.Column<string>(nullable: true),
                    OverallHeight = table.Column<string>(nullable: true),
                    OverallLength = table.Column<string>(nullable: true),
                    OverallWidth = table.Column<string>(nullable: true),
                    PrePreviousLicenceNumber = table.Column<string>(nullable: true),
                    PreviousLicenceNumber = table.Column<string>(nullable: true),
                    RegisterNumber = table.Column<string>(nullable: true),
                    RegistrationTypeCode = table.Column<string>(nullable: true),
                    RegistrationTypeDescription = table.Column<string>(nullable: true),
                    VehicleCertificateNumber = table.Column<string>(nullable: true),
                    RoadworthyStatusCode = table.Column<string>(nullable: true),
                    RoadworthyStatusDescription = table.Column<string>(nullable: true),
                    RoadworthyStatusDate = table.Column<string>(nullable: true),
                    RoadworthyTestDate = table.Column<string>(nullable: true),
                    SapClearanceDate = table.Column<string>(nullable: true),
                    SapClearanceStatusCode = table.Column<string>(nullable: true),
                    SapClearanceStatusDescription = table.Column<string>(nullable: true),
                    SapMarkCode = table.Column<string>(nullable: true),
                    SapMarkDescription = table.Column<string>(nullable: true),
                    SapMarkDate = table.Column<string>(nullable: true),
                    Tare = table.Column<string>(nullable: true),
                    VehicleStateCode = table.Column<string>(nullable: true),
                    VehicleStateDescription = table.Column<string>(nullable: true),
                    VehicleStateDate = table.Column<string>(nullable: true),
                    VinOrChassis = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleDetail", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VehicleDetail",
                schema: "RTMC");
        }
    }
}
