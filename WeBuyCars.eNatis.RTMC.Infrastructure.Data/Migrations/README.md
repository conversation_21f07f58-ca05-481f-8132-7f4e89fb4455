# Migration Tips

Please note that an external migration assembly is used.  Refer to the [Using a Separate Project](https://docs.microsoft.com/en-us/ef/core/managing-schemas/migrations/projects) article for more information.  Here are some notes on how to use migrations in this solution.


## Change Environment to decide where to deploy DB changes to

export ASPNETCORE_ENVIRONMENT=Development

## Make sure you're in the web api root folder before executing any of the command

```
cd ./WeBuyCars.eNatis.RTMC.Api
```

## Add a migration
```
dotnet ef migrations add InitialDbMigration --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 

dotnet ef migrations add ChangedReqIDtoLong --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 

dotnet ef migrations add IncludeReferenceFields --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 

dotnet ef migrations add UpdatedRTMCSettings --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 
dotnet ef migrations add AddRegisterReference --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data

dotnet ef migrations add UpdateNCOFields --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 
dotnet ef migrations add AddNCOReferenceField --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 
dotnet ef migrations add RectifyNCOFields --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data 
dotnet ef migrations add VehicleHistoryLog --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
dotnet ef migrations add RemoveVehicleHistoryRequestId --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
dotnet ef migrations add AddVehicleHistoryRequestId --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data    
dotnet ef migrations add AddClientMessageId --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data    
dotnet ef migrations add UpdatePasswordType --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data  
dotnet ef migrations add RemoveOldPassword --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
dotnet ef migrations add UpdateNCOAndRegistrationFields --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data   
dotnet ef migrations add IncreaseBarcodeSize --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
dotnet ef migrations add DateOfLiabilityForRegistration --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
dotnet ef migrations add AddSettingsFlags --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data



```

## Remove a migration
```
dotnet ef migrations remove --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
```

## Update the database
```
dotnet ef database update --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
```

## Revert the database
```
dotnet ef database update <migration name> --project ../WeBuyCars.eNatis.RTMC.Infrastructure.Data
```

## References
[Migrations: Using a Separate Project](https://docs.microsoft.com/en-us/ef/core/managing-schemas/migrations/projects)n



<!-- For New Projects -->

dotnet new tool-manifest

dotnet tool install dotnet-ef