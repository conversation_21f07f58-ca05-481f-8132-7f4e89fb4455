using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class InitialDbMigration : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "RTM<PERSON>");

            migrationBuilder.CreateTable(
                name: "<PERSON><PERSON>",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<Guid>(nullable: false),
                    TableName = table.Column<string>(maxLength: 200, nullable: false),
                    AuditDateTimeUtc = table.Column<DateTimeOffset>(nullable: false),
                    KeyValues = table.Column<string>(maxLength: 200, nullable: true),
                    OldValues = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    NewValues = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    User = table.Column<string>(maxLength: 200, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Audit", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomProperty",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    PropertyName = table.Column<string>(nullable: false),
                    PropertyValue = table.Column<string>(nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomProperty", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Request",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(maxLength: 200, nullable: true),
                    User = table.Column<string>(nullable: true),
                    WorkStation = table.Column<string>(nullable: true),
                    Locality = table.Column<string>(nullable: true),
                    NetworkAddress = table.Column<string>(nullable: true),
                    RequestObject = table.Column<string>(nullable: true),
                    Date = table.Column<DateTimeOffset>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Request", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Response",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Deleted = table.Column<bool>(nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(nullable: true),
                    CreatedBy = table.Column<string>(nullable: true),
                    ModifiedBy = table.Column<string>(nullable: true),
                    ReqId = table.Column<int>(nullable: false),
                    ResponseStatus = table.Column<string>(nullable: false),
                    ResponseObject = table.Column<string>(nullable: false),
                    Date = table.Column<DateTimeOffset>(nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Response", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Audit",
                schema: "RTMC");

            migrationBuilder.DropTable(
                name: "CustomProperty",
                schema: "RTMC");

            migrationBuilder.DropTable(
                name: "Request",
                schema: "RTMC");

            migrationBuilder.DropTable(
                name: "Response",
                schema: "RTMC");
        }
    }
}
