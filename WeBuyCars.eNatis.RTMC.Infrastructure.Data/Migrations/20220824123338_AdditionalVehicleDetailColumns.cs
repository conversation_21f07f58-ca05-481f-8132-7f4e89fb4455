using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AdditionalVehicleDetailColumns : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "CapacitySitting",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CapacityStanding",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfExportCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfExportDescripiton",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfImportCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CountryOfImportDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataOwnerCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DataOwnerDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "DifferentialNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EconomicSectorCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "EconomicSectorDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExaminerName",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExaminerNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExemptionCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ExemptionDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "FirstLicensingDate",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "GearboxNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceFee",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LicenceLiabilityDate",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ModelNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Overdue",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "PreviousVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthorityOfLicenceNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegAuthorityOfLicensing",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationDate",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationQualifier",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RegistrationQualifierDate",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RoadUseIndicator",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SapClearanceReasonCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "SapClearanceReasonDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Timestamp",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransmissionCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TransmissionDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleUsageCode",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VehicleUsageDescription",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VtsName",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "VtsNumber",
                schema: "RTMC",
                table: "VehicleDetail",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CapacitySitting",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "CapacityStanding",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "CountryOfExportCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "CountryOfExportDescripiton",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "CountryOfImportCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "CountryOfImportDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "DataOwnerCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "DataOwnerDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "DifferentialNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "EconomicSectorCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "EconomicSectorDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "ExaminerName",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "ExaminerNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "ExemptionCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "ExemptionDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "FirstLicensingDate",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "GearboxNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "LicenceFee",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "LicenceLiabilityDate",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "ModelNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "Overdue",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "PreviousVehicleCertificateNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthorityOfLicenceNumber",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegAuthorityOfLicensing",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegistrationDate",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegistrationQualifier",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RegistrationQualifierDate",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "RoadUseIndicator",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "SapClearanceReasonCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "SapClearanceReasonDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "Timestamp",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "TransmissionCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "TransmissionDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "VehicleUsageCode",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "VehicleUsageDescription",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "VtsName",
                schema: "RTMC",
                table: "VehicleDetail");

            migrationBuilder.DropColumn(
                name: "VtsNumber",
                schema: "RTMC",
                table: "VehicleDetail");
        }
    }
}
