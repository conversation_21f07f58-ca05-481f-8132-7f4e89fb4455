using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class UpdateNCOFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OwnerTitleHolderTransferDetail",
                schema: "RTMC");

            migrationBuilder.CreateTable(
                name: "VehicleOwnerOnlineNCODetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RegAuthority = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    RegisterNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VinOrChassis = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    EngineNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Make = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ModelName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleCategory = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Driven = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleDescription = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Tare = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FirstLicenceLiabiltyDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleLifeStatus = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SellerIdDocumentType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SellerIdDocumentNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SellerCountryOfIssue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    SellerName = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PurchaserIdDocumentType = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PurchaserIdDocument = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PurchaserIdDocumentNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    PurchaserCountryOfIssue = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    FirstRegistrationLiabiltyDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IssueDate = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    IssuedBy = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    VehicleCertificateNumber = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Barcode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    UserGroupCode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    DateTime = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Watermark = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_VehicleOwnerOnlineNCODetail", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "VehicleOwnerOnlineNCODetail",
                schema: "RTMC");

            migrationBuilder.CreateTable(
                name: "OwnerTitleHolderTransferDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    Barcode = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    ConvenienceFeeAmount = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    DateAndTime = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Driven = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    EngineNumber = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    FirstLicenceLiabilityDate = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    FirstRegistrationLiabilityDate = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IssueDate = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    IssuedBy = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    Make = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    ModelName = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    PaymentReferenceNumber = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    PurchaserCountryOfIssue = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    PurchaserIdDocumentNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    PurchaserIdDocumentType = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    PurchaserName = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    RegAuthority = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    RegisterNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    RegistrationFeeAmount = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    SellerCountryOfIssue = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    SellerIdDocumentNumber = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    SellerIdDocumentType = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    SellerName = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    Tare = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    TotalRegistrationFeeAmount = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    UserGroupCode = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: true),
                    VehicleCategory = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    VehicleCertificateNumber = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    VehicleDescription = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    VehicleLifeStatus = table.Column<string>(type: "nvarchar(60)", maxLength: 60, nullable: true),
                    VinOrChassis = table.Column<string>(type: "nvarchar(40)", maxLength: 40, nullable: true),
                    WaterMark = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OwnerTitleHolderTransferDetail", x => x.Id);
                });
        }
    }
}
