using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class IncludeReferenceFields : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "Reference",
                schema: "RTMC",
                table: "Response",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("cd19f880-acea-45b8-8b99-85326d88ded1"));

            migrationBuilder.AddColumn<Guid>(
                name: "Reference",
                schema: "RTMC",
                table: "Request",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("5b0f9f9f-7225-4e07-8a79-60bc1e6bda4a"));

            migrationBuilder.CreateTable(
                name: "OwnershipHistoryDetail",
                schema: "RTMC",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    RequestId = table.Column<int>(type: "int", nullable: false),
                    VinOrChassis = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    RegisterNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    LicenseNumber = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    EntityName = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    IdentificationNumber = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    OwnershipStatus = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    InsuranceCompany = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true),
                    OwnershipType = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    OwnershipDate = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    Deleted = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    CreatedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: false),
                    ModifiedOn = table.Column<DateTimeOffset>(type: "datetimeoffset", nullable: true),
                    CreatedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    ModifiedBy = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OwnershipHistoryDetail", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Response_Reference",
                schema: "RTMC",
                table: "Response",
                column: "Reference");

            migrationBuilder.CreateIndex(
                name: "IX_Request_Reference",
                schema: "RTMC",
                table: "Request",
                column: "Reference");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "OwnershipHistoryDetail",
                schema: "RTMC");

            migrationBuilder.DropIndex(
                name: "IX_Response_Reference",
                schema: "RTMC",
                table: "Response");

            migrationBuilder.DropIndex(
                name: "IX_Request_Reference",
                schema: "RTMC",
                table: "Request");

            migrationBuilder.DropColumn(
                name: "Reference",
                schema: "RTMC",
                table: "Response");

            migrationBuilder.DropColumn(
                name: "Reference",
                schema: "RTMC",
                table: "Request");
        }
    }
}
