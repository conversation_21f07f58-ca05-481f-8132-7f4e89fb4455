using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Migrations
{
    public partial class AddNCOReferenceField : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "Reference",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                type: "uniqueidentifier",
                nullable: false,
                defaultValueSql: "NEWID()");

            migrationBuilder.CreateIndex(
                name: "IX_VehicleOwnerOnlineNCODetail_Reference",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail",
                column: "Reference",
                unique: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_VehicleOwnerOnlineNCODetail_Reference",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");

            migrationBuilder.DropColumn(
                name: "Reference",
                schema: "RTMC",
                table: "VehicleOwnerOnlineNCODetail");
        }
    }
}
