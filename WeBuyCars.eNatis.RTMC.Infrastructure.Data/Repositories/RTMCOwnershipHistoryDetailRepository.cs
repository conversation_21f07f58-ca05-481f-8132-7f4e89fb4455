using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCOwnershipHistoryDetailRepository : Repository<RTMCOwnershipHistoryDetail>, IRTMCOwnershipHistoryDetailRepository
    {
        #region Constructors

        public RTMCOwnershipHistoryDetailRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCOwnershipHistoryDetail AddRTMCOwnershipHistoryDetail(RTMCOwnershipHistoryDetail entity)
        {
            return _context.OwnershipHistoryDetails.Add(entity).Entity;
        }

        public bool AddRTMCOwnershipHistoryDetailRange(List<RTMCOwnershipHistoryDetail> entity)
        {
            var result = false;
            _context.OwnershipHistoryDetails.AddRange(entity);
            result = true;
            return result;
        }

        public async Task<List<RTMCOwnershipHistoryDetail>> Where(Expression <Func<RTMCOwnershipHistoryDetail, bool>> predicate)
        {
            return await _context.OwnershipHistoryDetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCOwnershipHistoryDetail> FirstOrDefaultAsync(Expression <Func<RTMCOwnershipHistoryDetail, bool>> predicate)
        {
            return await _context.OwnershipHistoryDetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCOwnershipHistoryDetail> LastOrDefaultAsync(Expression <Func<RTMCOwnershipHistoryDetail, bool>> predicate)
        {
            return await _context.OwnershipHistoryDetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

        public async Task<RTMCOwnershipHistoryDetail> UpdateRTMCOwnershipHistoryDetail(RTMCOwnershipHistoryDetail entity)
        {
            _context.Entry(entity).State = EntityState.Modified;
            return entity;
        }


    }
}