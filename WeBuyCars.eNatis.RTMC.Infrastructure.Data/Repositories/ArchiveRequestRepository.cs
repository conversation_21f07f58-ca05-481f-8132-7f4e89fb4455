using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class ArchiveRequestRepository : Repository<ArchivedRequest>, IArchiveRequestRepository
    {
        #region Constructors

        public ArchiveRequestRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public ArchivedRequest AddArchiveRequest(ArchivedRequest entity)
        {
            return _context.ArchivedRequests.Add(entity).Entity;
        }

    }
}