using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCDriverInformationDetailRepository : Repository<RTMCDriverInformationDetail>, IRTMCDriverInformationDetailRepository
    {
        #region Constructors

        public RTMCDriverInformationDetailRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCDriverInformationDetail AddRTMCDriverInformationDetail(RTMCDriverInformationDetail entity)
        {
            return _context.DriverInformationDetails.Add(entity).Entity;
        }

        public async Task<List<RTMCDriverInformationDetail>> Where(Expression <Func<RTMCDriverInformationDetail, bool>> predicate)
        {
            return await _context.DriverInformationDetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCDriverInformationDetail> FirstOrDefaultAsync(Expression <Func<RTMCDriverInformationDetail, bool>> predicate)
        {
            return await _context.DriverInformationDetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCDriverInformationDetail> LastOrDefaultAsync(Expression <Func<RTMCDriverInformationDetail, bool>> predicate)
        {
            return await _context.DriverInformationDetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }


    }
}