using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;
namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCResponseRepository : Repository<RTMCResponse>, IRTMCResponseRepository
    {
        #region Constructors

        public RTMCResponseRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCResponse AddRTMCResponse(RTMCResponse entity)
        {
            return _context.Responses.Add(entity).Entity;
        }

        public async Task<List<RTMCResponse>> Where(Expression <Func<RTMCResponse, bool>> predicate)
        {
            return await _context.Responses
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCResponse> FirstOrDefaultAsync(Expression <Func<RTMCResponse, bool>> predicate)
        {
            return await _context.Responses.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCResponse> LastOrDefaultAsync(Expression <Func<RTMCResponse, bool>> predicate)
        {
            return await _context.Responses.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

    }
}