using System;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class Repository<T> where T : Entity
    {
        public readonly RTMCContext _context;

        public IUnitOfWork UnitOfWork => _context;

        public Repository(RTMCContext context)
        {
            _context = context ?? throw new System.ArgumentNullException(nameof(context));
        }
        public T Add(T entity)
        {
            return _context.Set<T>().Add(entity).Entity;
        }
        public Task<bool> ExistsAsync(Expression<Func<T, bool>> predicate)
        {
            return _context.Set<T>().AnyAsync(predicate);
        }
        public void Remove(T entity)
        {
            _context.Entry(entity).State = EntityState.Deleted;
        }
        public void Update(T entity)
        {
            _context.Entry(entity).State = EntityState.Modified;
        }
    }
}