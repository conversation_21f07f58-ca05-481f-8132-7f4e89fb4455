using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.ReadonlyEntities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class ReportRepository : IReportRepository
    {
        public readonly RTMCContext _context;

        public IUnitOfWork UnitOfWork => _context;

        public ReportRepository(RTMCContext context)
        {
            _context = context ?? throw new System.ArgumentNullException(nameof(context));
        }

        public async Task<List<OnlineTransactionReportRecord>> GetOnlineTransactionReportRecords(string NamedTimeRange)
        {
            var records = await _context.Set<OnlineTransactionReportRecord>()
                .FromSqlRaw("EXECUTE RTMC.GetOnlineTransactions @NamedTimeRange = {0}",NamedTimeRange)
                .ToListAsync();

            return records;
        }
    }
}