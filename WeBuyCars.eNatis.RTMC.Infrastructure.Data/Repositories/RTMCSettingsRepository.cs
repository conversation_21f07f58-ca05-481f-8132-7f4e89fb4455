using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCSettingsRepository : Repository<RTMCSettings>, IRTMCSettingsRepository
    {
        #region Constructors

        public RTMCSettingsRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCSettings AddRTMCSettings(RTMCSettings entity)
        {
            return _context.Settings.Add(entity).Entity;
        }

        public RTMCSettings UpdateRTMCSettings(RTMCSettings entity)
        {
            _context.Entry(entity).State = EntityState.Modified;
            return _context.Settings.Update(entity).Entity;
        }

        public async Task<List<RTMCSettings>> Where(Expression <Func<RTMCSettings, bool>> predicate)
        {
            return await _context.Settings
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCSettings> FirstOrDefaultAsync(Expression <Func<RTMCSettings, bool>> predicate)
        {
            return await _context.Settings.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCSettings> LastOrDefaultAsync(Expression <Func<RTMCSettings, bool>> predicate)
        {
            return await _context.Settings.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

        public async Task<RTMCSettings> LastOrDefaultNoTrackingAsync(Expression <Func<RTMCSettings, bool>> predicate)
        {
            return await _context.Settings.AsNoTracking().Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }


    }
}