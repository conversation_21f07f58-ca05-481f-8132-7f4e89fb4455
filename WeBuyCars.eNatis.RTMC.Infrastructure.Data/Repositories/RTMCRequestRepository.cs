using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCRequestRepository : Repository<RTMCRequest>, IRTMCRequestRepository
    {
        #region Constructors

        public RTMCRequestRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCRequest AddRTMCRequest(RTMCRequest entity)
        {
            return _context.Requests.Add(entity).Entity;
        }

        public async Task<List<RTMCRequest>> Where(Expression <Func<RTMCRequest, bool>> predicate)
        {
            return await _context.Requests
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCRequest> FirstOrDefaultAsync(Expression <Func<RTMCRequest, bool>> predicate)
        {
            return await _context.Requests.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCRequest> LastOrDefaultAsync(Expression <Func<RTMCRequest, bool>> predicate)
        {
            return await _context.Requests.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

    }
}