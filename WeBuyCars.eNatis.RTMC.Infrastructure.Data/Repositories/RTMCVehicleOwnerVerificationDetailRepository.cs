using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleOwnerVerificationDetailRepository : Repository<RTMCVehicleOwnerVerificationDetail>, IRTMCVehicleOwnerVerificationDetailRepository
    {
        #region Constructors

        public RTMCVehicleOwnerVerificationDetailRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleOwnerVerificationDetail AddRTMCVehicleOwnerVerificationDetail(RTMCVehicleOwnerVerificationDetail entity)
        {
            return _context.VehicleOwnershipVerificationDetails.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleOwnerVerificationDetail>> Where(Expression <Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnershipVerificationDetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCVehicleOwnerVerificationDetail> FirstOrDefaultAsync(Expression <Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnershipVerificationDetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleOwnerVerificationDetail> LastOrDefaultAsync(Expression <Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnershipVerificationDetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

    }
}