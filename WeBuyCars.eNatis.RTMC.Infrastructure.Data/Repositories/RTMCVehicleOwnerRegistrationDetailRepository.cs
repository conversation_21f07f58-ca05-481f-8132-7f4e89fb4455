using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleOwnerRegistrationDetailRepository : Repository<RTMCVehicleOwnerRegistrationDetail>, IRTMCVehicleOwnerRegistrationDetailRepository
    {
        #region Constructors

        public RTMCVehicleOwnerRegistrationDetailRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleOwnerRegistrationDetail AddRTMCVehicleOwnerRegistrationDetail(RTMCVehicleOwnerRegistrationDetail entity)
        {
            return _context.VehicleOwnerRegistrationDetails.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleOwnerRegistrationDetail>> Where(Expression <Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnerRegistrationDetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCVehicleOwnerRegistrationDetail> FirstOrDefaultAsync(Expression <Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnerRegistrationDetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleOwnerRegistrationDetail> LastOrDefaultAsync(Expression <Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate)
        {
            return await _context.VehicleOwnerRegistrationDetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

    }
}