using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class ArchiveResponseRepository : Repository<ArchivedResponse>, IArchiveResponseRepository
    {
        #region Constructors

        public ArchiveResponseRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public ArchivedResponse AddArchiveRequest(ArchivedResponse entity)
        {
            return _context.ArchivedResponses.Add(entity).Entity;
        }

    }
}