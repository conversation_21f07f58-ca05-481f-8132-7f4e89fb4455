using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleOwnerOnlineNCORepository : Repository<RTMCVehicleOwnerOnlineNCODetail>, IRTMCVehicleOwnerOnlineNCORepository
    {
        #region Constructors

        public RTMCVehicleOwnerOnlineNCORepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleOwnerOnlineNCODetail AddRTMCVehicleOwnerOnlineNCODetail(RTMCVehicleOwnerOnlineNCODetail entity)
        {
            return _context.VehicleOwnerOnlineNCODetails.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleOwnerOnlineNCODetail>> Where(Expression <Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate)
        {
            return await _context.VehicleOwnerOnlineNCODetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCVehicleOwnerOnlineNCODetail> FirstOrDefaultAsync(Expression <Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate)
        {
            return await _context.VehicleOwnerOnlineNCODetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleOwnerOnlineNCODetail> LastOrDefaultAsync(Expression <Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate)
        {
            return await _context.VehicleOwnerOnlineNCODetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }

    }
}