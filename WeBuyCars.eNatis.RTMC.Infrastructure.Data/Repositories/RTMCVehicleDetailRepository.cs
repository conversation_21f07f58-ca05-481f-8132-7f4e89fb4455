using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories
{
    public class RTMCVehicleDetailRepository : Repository<RTMCVehicleDetail>, IRTMCVehicleDetailRepository
    {
        #region Constructors

        public RTMCVehicleDetailRepository(RTMCContext context)
            : base(context)
        {
        }

        #endregion

        public RTMCVehicleDetail AddRTMCVehicleDetail(RTMCVehicleDetail entity)
        {
            return _context.VehicleDetails.Add(entity).Entity;
        }

        public async Task<List<RTMCVehicleDetail>> Where(Expression <Func<RTMCVehicleDetail, bool>> predicate)
        {
            return await _context.VehicleDetails
            .Where(predicate)
            .AsNoTracking()
            .ToListAsync();
        }

        public async Task<RTMCVehicleDetail> FirstOrDefaultAsync(Expression <Func<RTMCVehicleDetail, bool>> predicate)
        {
            return await _context.VehicleDetails.Where(predicate).OrderBy(x => x.Id).FirstOrDefaultAsync();
        }

        public async Task<RTMCVehicleDetail> LastOrDefaultAsync(Expression <Func<RTMCVehicleDetail, bool>> predicate)
        {
            return await _context.VehicleDetails.Where(predicate).OrderBy(x => x.Id).LastOrDefaultAsync();
        }


    }
}