using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.Context
{
    public class RTMCContext : DbContext, IUnitOfWork
    {
        public const string DEFAULT_SCHEMA = "RTMC";

        #region Fields

        readonly IHttpContextAccessor _httpContextAccessor;

        readonly string _user;

        #endregion

        #region Constructors

        public RTMCContext(IHttpContextAccessor httpContextAccessor, DbContextOptions<RTMCContext> options) : base(options)
        {
            _httpContextAccessor = httpContextAccessor;
            _user = _httpContextAccessor.HttpContext?.User?.GetEmail() ?? _httpContextAccessor.HttpContext?.User?.GetClient() ?? "System";
        }

        #endregion

        #region Properties

        public DbSet<Audit> Audits { get; set; }
        public DbSet<RTMCRequest> Requests { get; set; }
        public DbSet<RTMCResponse> Responses {get;set;}
        public DbSet<RTMCVehicleDetail> VehicleDetails {get;set;}
        public DbSet<RTMCVehicleOwnerVerificationDetail> VehicleOwnershipVerificationDetails {get;set;}
        public DbSet<RTMCDriverInformationDetail> DriverInformationDetails {get;set;}
        public DbSet<ArchivedRequest> ArchivedRequests { get; set; }
        public DbSet<ArchivedResponse> ArchivedResponses { get; set; }
        public DbSet<RTMCSettings> Settings { get; set; }
        public DbSet<RTMCVehicleOwnerRegistrationDetail> VehicleOwnerRegistrationDetails { get; set; }
        public DbSet<RTMCOwnershipHistoryDetail> OwnershipHistoryDetails { get; set; }
        public DbSet<RTMCVehicleOwnerOnlineNCODetail> VehicleOwnerOnlineNCODetails { get; set; }


        #endregion

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.HasDefaultSchema(RTMCContext.DEFAULT_SCHEMA);
            modelBuilder.ApplyConfigurationsFromAssembly(Assembly.GetExecutingAssembly());

            modelBuilder.ApplyConfiguration(new RTMCRequestConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCResponseConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCVehicleDetailConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCVehicleOwnerVerificationDetailConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCDriverInformationDetailConfiguration());
            modelBuilder.ApplyConfiguration(new ArchivedRequestConfiguration());
            modelBuilder.ApplyConfiguration(new ArchivedResponseConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCSettingsConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCVehicleOwnerRegistrationDetailConfiguration());
            modelBuilder.ApplyConfiguration(new RTMCOwnershipHistoryDetailConfiguration());
            
        }

        public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default(CancellationToken))
        {
            try
            {
                if (cancellationToken.IsCancellationRequested)
                {
                    return 0;
                }

                // var auditEntries = OnBeforeSaveChanges();
                ApplyChanges();
                var result = await base.SaveChangesAsync();
                // await OnAfterSaveChanges(auditEntries);
                return result;
            }
            catch (Exception ex)
            {
                throw new InfrastructureException(ex);
            }
        }

        public override int SaveChanges()
        {
            try
            {
                // var auditEntries = OnBeforeSaveChanges();
                ApplyChanges();
                var result = base.SaveChanges();
                // OnAfterSaveChanges(auditEntries).GetAwaiter().GetResult();
                return result;
            }
            catch (Exception ex)
            {
                throw new InfrastructureException(ex);
            }
        }

        void ApplyChanges()
        {
            foreach (EntityEntry<Entity> entryEntity in ChangeTracker.Entries<Entity>())
            {
                switch (entryEntity.State)
                {
                    case EntityState.Added:
                        entryEntity.Entity.CreatedBy = string.IsNullOrWhiteSpace(entryEntity.Entity.CreatedBy) ? _user : entryEntity.Entity.CreatedBy;;
                        entryEntity.Entity.CreatedOn = DateTimeOffset.UtcNow;
                        break;
                    case EntityState.Modified:
                        entryEntity.Entity.ModifiedBy = _user;
                        entryEntity.Entity.ModifiedOn = DateTimeOffset.UtcNow;
                        break;
                    case EntityState.Deleted:
                        entryEntity.State = EntityState.Modified;
                        entryEntity.Entity.ModifiedBy = _user;
                        entryEntity.Entity.ModifiedOn = DateTimeOffset.UtcNow;
                        entryEntity.Entity.Deleted = true;
                        break;
                }
            }
        }

        // List<AuditEntry> OnBeforeSaveChanges()
        // {
        //     ChangeTracker.DetectChanges();
        //     var auditEntries = new List<AuditEntry>();
        //     foreach (var entry in ChangeTracker.Entries())
        //     {
        //         if (entry.Entity is Audit || entry.State == EntityState.Detached || entry.State == EntityState.Unchanged)
        //         {
        //             continue;
        //         }

        //         var auditEntry = new AuditEntry(entry)
        //         {
        //             TableName = entry.Metadata.GetTableName(),
        //             User = _user
        //         };
        //         auditEntries.Add(auditEntry);

        //         foreach (var property in entry.Properties)
        //         {
        //             if (property.IsTemporary)
        //             {
        //                 // value will be generated by the database, get the value after saving
        //                 auditEntry.TemporaryProperties.Add(property);
        //                 continue;
        //             }

        //             string propertyName = property.Metadata.Name;
        //             if (property.Metadata.IsPrimaryKey())
        //             {
        //                 auditEntry.KeyValues[propertyName] = property.CurrentValue;
        //                 continue;
        //             }

        //             switch (entry.State)
        //             {
        //                 case EntityState.Added:
        //                     auditEntry.NewValues[propertyName] = property.CurrentValue;
        //                     break;

        //                 case EntityState.Deleted:
        //                     auditEntry.OldValues[propertyName] = property.OriginalValue;
        //                     break;

        //                 case EntityState.Modified:
        //                     if (property.IsModified)
        //                     {
        //                         auditEntry.OldValues[propertyName] = property.OriginalValue;
        //                         auditEntry.NewValues[propertyName] = property.CurrentValue;
        //                     }
        //                     break;
        //             }
        //         }
        //     }

        //     // Save audit entities that have all the modifications
        //     foreach (var auditEntry in auditEntries.Where(_ => !_.HasTemporaryProperties))
        //     {
        //         Audits.Add(auditEntry.ToAudit());
        //     }

        //     // Keep a list of entries where the value of some properties are unknown at this step
        //     return auditEntries.Where(_ => _.HasTemporaryProperties).ToList();
        // }

        // Task OnAfterSaveChanges(List<AuditEntry> auditEntries)
        // {
        //     if (auditEntries == null || auditEntries.Count == 0)
        //     {
        //         return Task.CompletedTask;
        //     }

        //     foreach (var auditEntry in auditEntries)
        //     {
        //         // Get the final value of the temporary properties
        //         foreach (var prop in auditEntry.TemporaryProperties)
        //         {
        //             if (prop.Metadata.IsPrimaryKey())
        //             {
        //                 auditEntry.KeyValues[prop.Metadata.Name] = prop.CurrentValue;
        //             }
        //             else
        //             {
        //                 auditEntry.NewValues[prop.Metadata.Name] = prop.CurrentValue;
        //             }
        //         }

        //         // Save the Audit entry
        //         Audits.Add(auditEntry.ToAudit());
        //     }

        //     return SaveChangesAsync();
        // }
    }
}


