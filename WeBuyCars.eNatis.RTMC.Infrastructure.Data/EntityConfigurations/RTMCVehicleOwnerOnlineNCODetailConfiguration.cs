using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCVehicleOwnerOnlineNCODetailConfiguration : IEntityTypeConfiguration<RTMCVehicleOwnerOnlineNCODetail>
    {

        public void Configure(EntityTypeBuilder<RTMCVehicleOwnerOnlineNCODetail> builder)
        {

            builder.ToTable("VehicleOwnerOnlineNCODetail");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.EncryptedVehicleCertificateNumber)
                .HasMaxLength(512)
                .IsRequired();

            builder.Property(i => i.EncryptedBarcode)
                .HasMaxLength(1024)
                .IsRequired();                

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);
                
            builder.Property(i => i.Reference)
                .IsRequired()
                .HasDefaultValueSql("NEWID()");

            builder.HasIndex(i => i.Reference)
                .IsUnique();

                

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}