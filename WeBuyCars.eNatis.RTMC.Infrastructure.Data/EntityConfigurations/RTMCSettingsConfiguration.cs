using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCSettingsConfiguration : IEntityTypeConfiguration<RTMCSettings>
    {

        public void Configure(EntityTypeBuilder<RTMCSettings> builder)
        {

            builder.ToTable("Settings");

            builder.Property(i => i.BusinessRegistrationNumber)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.Username)
                .HasMaxLength(200)
                .IsRequired();

            // builder.Property(i => i.Password)
            //     .HasMaxLength(200)
            //     .IsRequired();

            builder.Property(i => i.EncryptedPassword)
                .HasMaxLength(512)
                .IsRequired();

            builder.Property(i => i.OwnerDocumentNumber)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ProxyDocumentNumber)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.RepresentativeDocumentNumber)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.HasLogin);

            builder.Property(i => i.CanOnlineReg);

            builder.Property(i => i.CanOnlineRelease);

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}