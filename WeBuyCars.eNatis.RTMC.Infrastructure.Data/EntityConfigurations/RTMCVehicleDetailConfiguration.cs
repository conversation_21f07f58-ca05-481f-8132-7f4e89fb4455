using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCVehicleDetailConfiguration : IEntityTypeConfiguration<RTMCVehicleDetail>
    {

        public void Configure(EntityTypeBuilder<RTMCVehicleDetail> builder)
        {

            builder.ToTable("VehicleDetail");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.Property(i => i.RegistrationAllowed)
                .HasMaxLength(1);

            builder.Property(i => i.AdministrationMarkIndicator)
                .HasMaxLength(1);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}