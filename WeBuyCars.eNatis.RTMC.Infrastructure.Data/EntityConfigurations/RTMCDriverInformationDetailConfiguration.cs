using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCDriverInformationDetailConfiguration : IEntityTypeConfiguration<RTMCDriverInformationDetail>
    {

        public void Configure(EntityTypeBuilder<RTMCDriverInformationDetail> builder)
        {

            builder.ToTable("DriverInformationDetail");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);                

            builder.Property(i => i.DrivingLicenceNumber)
                .HasMaxLength(100);

            builder.Property(i => i.LicenceCarddateOfFirstIssue)
                .HasMaxLength(100);

            builder.Property(i => i.LicenceCardcardIssueNumber)
                .HasMaxLength(100);

            builder.Property(i => i.LicenceCardvalidTo)
                .HasMaxLength(100);

            builder.Property(i => i.IdDocumentTypeCode)
                .HasMaxLength(20);

            builder.Property(i => i.IdDocumentTypeDescription)
                .HasMaxLength(200);

            builder.Property(i => i.IdDocumentNumber)
                .HasMaxLength(100);

            builder.Property(i => i.Initials)
                .HasMaxLength(50);

            builder.Property(i => i.BusinessOrSurname)
                .HasMaxLength(200);

            builder.Property(i => i.BirthDate)
                .HasMaxLength(100);

            builder.Property(i => i.Age)
                .HasMaxLength(50);

            builder.Property(i => i.NatureOfPersonCode)
                .HasMaxLength(20);

            builder.Property(i => i.NatureOfPersonDescription)
                .HasMaxLength(500);

            builder.Property(i => i.PopulationGroupCode)
                .HasMaxLength(20);

            builder.Property(i => i.PopulationGroupDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LicenceRestrictionCode)
                .HasMaxLength(20);

            builder.Property(i => i.LicenceRestrictionDescription)
                .HasMaxLength(500);

            builder.Property(i => i.DrivingLicencedateOfFirstIssue)
                .HasMaxLength(100);

            builder.Property(i => i.DrivingLicenceTypeCode)
                .HasMaxLength(20);

            builder.Property(i => i.DrivingLicenceTypeDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LicenceAuthorisationDate)
                .HasMaxLength(100);

            builder.Property(i => i.DrivingLicenceValidFrom)
                .HasMaxLength(100);

            builder.Property(i => i.VehicleRestrictionCode)
                .HasMaxLength(20);

            builder.Property(i => i.VehicleRestrictionDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LearnerCertificateNumber)
                .HasMaxLength(100);

            builder.Property(i => i.LearnerLicenceTypeCode)
                .HasMaxLength(20);

            builder.Property(i => i.LearnerLicenceTypeDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LearnerLicenceStatusCode)
                .HasMaxLength(20);

            builder.Property(i => i.LearnerLicenceStatusDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LearnerLicenceStatusDescription)
                .HasMaxLength(500);

            builder.Property(i => i.LearnerLicenceValidFrom)
                .HasMaxLength(100);

            builder.Property(i => i.LearnerLicenceSpeciallyAdaptedVehicleRequired)
                .HasMaxLength(200);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}