using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCResponseConfiguration : IEntityTypeConfiguration<RTMCResponse>
    {

        public void Configure(EntityTypeBuilder<RTMCResponse> builder){

            builder.ToTable("Response");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.ReqId)
                .IsRequired();

            builder.Property(i => i.ResponseStatus)
                .IsRequired();
        
            builder.Property(i => i.NatisResponseObject)
                .IsRequired();

            builder.Property(i => i.ResponseObject)
                .IsRequired();

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.Reference)
                .IsRequired()
                .HasDefaultValueSql("NEWID()");

            builder.HasIndex(i => i.Reference)
                .IsUnique();

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);


        }
    }

}


