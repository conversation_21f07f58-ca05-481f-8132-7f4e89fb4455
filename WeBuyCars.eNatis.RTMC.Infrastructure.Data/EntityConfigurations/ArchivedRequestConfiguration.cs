using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class ArchivedRequestConfiguration : IEntityTypeConfiguration<ArchivedRequest>
    {

        public void Configure(EntityTypeBuilder<ArchivedRequest> builder)
        {

            builder.ToTable("ArchivedRequest");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.User)
                .HasMaxLength(200);

            builder.Property(i => i.RequestObject)
                .IsRequired();

            builder.Property(i => i.IntegrationType)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}