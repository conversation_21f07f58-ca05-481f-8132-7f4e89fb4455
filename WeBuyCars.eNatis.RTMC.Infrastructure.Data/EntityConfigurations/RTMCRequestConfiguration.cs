using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCRequestConfiguration : IEntityTypeConfiguration<RTMCRequest>
    {

        public void Configure(EntityTypeBuilder<RTMCRequest> builder)
        {

            builder.ToTable("Request");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Locality)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.WorkStation)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.NetworkAddress)
                .HasMaxLength(50)
                .IsRequired();

            builder.Property(i => i.RequestObject)
                .IsRequired();

            builder.Property(i => i.IntegrationType)
                .HasMaxLength(100)
                .IsRequired();

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.Property(i => i.Reference)
                .IsRequired()
                .HasDefaultValueSql("NEWID()");

            builder.HasIndex(i => i.Reference)
                .IsUnique();

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}