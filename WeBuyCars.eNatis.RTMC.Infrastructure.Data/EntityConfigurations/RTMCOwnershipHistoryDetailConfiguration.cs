using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCOwnershipHistoryDetailConfiguration : IEntityTypeConfiguration<RTMCOwnershipHistoryDetail>
    {

        public void Configure(EntityTypeBuilder<RTMCOwnershipHistoryDetail> builder)
        {

            builder.ToTable("OwnershipHistoryDetail");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.Property(i => i.RequestId)
                .IsRequired(true);

            builder.Property(i => i.VinOrChassis)
                .HasMaxLength(50);

            builder.Property(i => i.RegisterNumber)
                .HasMaxLength(50);

            builder.Property(i => i.LicenseNumber)
                .HasMaxLength(50);
        
            builder.Property(i => i.EntityName)
                .HasMaxLength(200);
        
            builder.Property(i => i.IdentificationNumber)
                .HasMaxLength(100);

            builder.Property(i => i.OwnershipStatus)
                .HasMaxLength(100);

            builder.Property(i => i.InsuranceCompany)
                .HasMaxLength(200);

            builder.Property(i => i.OwnershipType)
                .HasMaxLength(100);

            builder.Property(i => i.OwnershipDate)
                .HasMaxLength(100);

            builder.HasIndex(i => new { i.RegisterNumber, i.IdentificationNumber, i.OwnershipType, i.OwnershipDate })
                .IsUnique();

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}