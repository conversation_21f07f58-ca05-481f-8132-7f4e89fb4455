using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class ArchivedResponseConfiguration : IEntityTypeConfiguration<ArchivedResponse>
    {

        public void Configure(EntityTypeBuilder<ArchivedResponse> builder){

            builder.ToTable("ArchivedResponse");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.ReqId)
                .IsRequired();

            builder.Property(i => i.ResponseObject)
                .IsRequired();

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);


        }
    }

}


