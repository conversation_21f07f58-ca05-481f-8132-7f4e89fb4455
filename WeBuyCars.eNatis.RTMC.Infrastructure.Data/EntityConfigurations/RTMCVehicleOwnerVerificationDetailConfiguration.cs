using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{

    public class RTMCVehicleOwnerVerificationDetailConfiguration : IEntityTypeConfiguration<RTMCVehicleOwnerVerificationDetail>
    {

        public void Configure(EntityTypeBuilder<RTMCVehicleOwnerVerificationDetail> builder)
        {

            builder.ToTable("VehicleOwnerVerificationDetail");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.Property(i => i.PersonDocumentTypeCode)
                .HasMaxLength(10);

            builder.Property(i => i.PersonDocumentTypeDescription)
                .HasMaxLength(100);

            builder.Property(i => i.PersonDocumentNumber)
                .HasMaxLength(100);

            builder.Property(i => i.TitleHolderConfirmation)
                .HasMaxLength(100);
        
            builder.Property(i => i.OwnerSurnameofBusiness)
                .HasMaxLength(500);
        
            builder.Property(i => i.OwnerInitials)
                .HasMaxLength(200);

            builder.Property(i => i.OwnerConfirmation)
                .HasMaxLength(200);

            builder.Property(i => i.VehicleRegistrationNumber)
                .HasMaxLength(100);

            builder.Property(i => i.VehicleLicenseNumber)
                .HasMaxLength(100);

            builder.Property(i => i.VehicleVinorChassis)
                .HasMaxLength(100);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);

        }
        
    }

}