using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class CustomPropertyEntityConfiguration : IEntityTypeConfiguration<CustomProperty>
    {
        public void Configure(EntityTypeBuilder<CustomProperty> builder)
        {
            builder.ToTable("CustomProperty");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.PropertyName)
                .IsRequired();

            builder.Property(i => i.PropertyValue)
                .IsRequired(false);  

            builder.Property(i => i.Deleted)
                .HasDefaultValue(false);

            builder.Property(i => i.CreatedOn)
                .IsRequired();

            builder.Property(i => i.ModifiedOn)
                .IsRequired(false);

            builder.Property(i => i.CreatedBy)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.ModifiedBy)
                .HasMaxLength(200);

            builder.HasQueryFilter(m => EF.Property<bool>(m, "Deleted") == false);
        }
    }
}