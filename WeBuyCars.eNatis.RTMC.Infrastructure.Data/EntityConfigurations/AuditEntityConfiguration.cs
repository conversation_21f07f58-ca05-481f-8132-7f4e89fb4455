using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Data.EntityConfigurations
{
    public class AuditEntityConfiguration : IEntityTypeConfiguration<Audit>
    {
        public void Configure(EntityTypeBuilder<Audit> builder)
        {
            builder.ToTable("Audit");

            builder.HasKey(i => i.Id);

            builder.Property(i => i.TableName)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.User)
                .HasMaxLength(200)
                .IsRequired();

            builder.Property(i => i.AuditDateTimeUtc)                
                .IsRequired();

            builder.Property(i => i.KeyValues)
                .HasMaxLength(200);

            builder.Property(i => i.OldValues)
                .HasColumnType("nvarchar(max)");

            builder.Property(i => i.NewValues)
                .HasColumnType("nvarchar(max)");
        }
    }
}
