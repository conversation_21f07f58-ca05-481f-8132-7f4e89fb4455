
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30204.135
MinimumVisualStudioVersion = 15.0.26124.0
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WeBuyCars.eNatis.RTMC.Infrastructure.Natis", "WeBuyCars.eNatis.RTMC.Infrastructure.Natis\WeBuyCars.eNatis.RTMC.Infrastructure.Natis.csproj", "{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WeBuyCars.eNatis.RTMC.Infrastructure.Data", "WeBuyCars.eNatis.RTMC.Infrastructure.Data\WeBuyCars.eNatis.RTMC.Infrastructure.Data.csproj", "{82C66204-D1DF-4176-B9C1-DC992F589952}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WeBuyCars.eNatis.RTMC.Core", "WeBuyCars.eNatis.RTMC.Core\WeBuyCars.eNatis.RTMC.Core.csproj", "{C1353AA1-9936-414A-A9DF-65A62C64CE1D}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WeBuyCars.eNatis.RTMC.Api", "WeBuyCars.eNatis.RTMC.Api\WeBuyCars.eNatis.RTMC.Api.csproj", "{B578E613-EFF1-44FA-838A-75B8E07F6DFB}"
EndProject

Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{815159E3-3C2D-46F6-906E-5165086E044C}"
	ProjectSection(SolutionItems) = preProject
		.gitignore = .gitignore
		pipeline.yml = pipeline.yml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|x64.Build.0 = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Debug|x86.Build.0 = Debug|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|Any CPU.Build.0 = Release|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|x64.ActiveCfg = Release|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|x64.Build.0 = Release|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|x86.ActiveCfg = Release|Any CPU
		{DB8BE9A3-B9BF-4341-AA9E-0750FDF4C6AB}.Release|x86.Build.0 = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|x64.Build.0 = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Debug|x86.Build.0 = Debug|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|Any CPU.Build.0 = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|x64.ActiveCfg = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|x64.Build.0 = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|x86.ActiveCfg = Release|Any CPU
		{C1353AA1-9936-414A-A9DF-65A62C64CE1D}.Release|x86.Build.0 = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|x64.Build.0 = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Debug|x86.Build.0 = Debug|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|Any CPU.Build.0 = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|x64.ActiveCfg = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|x64.Build.0 = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|x86.ActiveCfg = Release|Any CPU
		{B578E613-EFF1-44FA-838A-75B8E07F6DFB}.Release|x86.Build.0 = Release|Any CPU

		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|x64.ActiveCfg = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|x64.Build.0 = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|x86.ActiveCfg = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Debug|x86.Build.0 = Debug|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|Any CPU.Build.0 = Release|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|x64.ActiveCfg = Release|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|x64.Build.0 = Release|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|x86.ActiveCfg = Release|Any CPU
		{82C66204-D1DF-4176-B9C1-DC992F589952}.Release|x86.Build.0 = Release|Any CPU



	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3B443D45-AF36-4D01-A1C2-7F57197B19A2}
	EndGlobalSection
EndGlobal
