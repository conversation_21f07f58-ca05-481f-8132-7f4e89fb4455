using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions
{

    [AttributeUsage(AttributeTargets.Method, AllowMultiple = true)]
    public class TimeframeAccessAttribute : Attribute, IAsyncAuthorizationFilter
    {
        private readonly TimeSpan _startTimeUtc;
        private readonly TimeSpan _endTimeUtc;
        private readonly string _customMessage;

        public TimeframeAccessAttribute(string startTimeUtc, string endTimeUtc, string customMessage)
        {
            _startTimeUtc = TimeSpan.Parse(startTimeUtc);
            _endTimeUtc = TimeSpan.Parse(endTimeUtc);
            _customMessage = customMessage;
        }

        public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
        {
            var currentTimeUtc = DateTime.UtcNow.TimeOfDay;
            if (currentTimeUtc < _startTimeUtc || currentTimeUtc > _endTimeUtc)
            {
                context.Result = new ContentResult
                {
                    StatusCode = StatusCodes.Status503ServiceUnavailable,
                    Content = _customMessage,
                    ContentType = "text/plain"
                };
            }
        }
    }

}
