using System;
using System.ComponentModel;
using System.Reflection;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using WeBuyCars.eNatis.RTMC.Api.Configurations;

namespace WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions
{
    public static class ServiceExtension
    {
        /// <summary>
        /// Registers the ENatis integration services in the Microsoft.Extensions.DependencyInjection.IServiceCollection.
        /// </summary>
        /// <param name="services">The Microsoft.Extensions.DependencyInjection.IServiceCollection to add services to.</param>
        /// <param name="configuration">Application configuration properties.</param>
        /// <returns>The same service collection so that multiple calls can be chained.</returns>
        public static IServiceCollection AddServiceExtensions(this IServiceCollection services, IConfiguration configuration)
        {
            //Enable End Point Settings
            services.Configure<EnableEndPointOptions>(configuration.GetSection("EnableEndPoints"));
            //Get Server Environment
            services.Configure<ServerEnvironmentOptions>(configuration.GetSection("ServerEnvironment"));

            //Automapper
            services.AddAutoMapper(Assembly.GetExecutingAssembly());

            return services;
        }

        public static string ToDescription(this Enum value)
        {
            var da = (DescriptionAttribute[])(value.GetType().GetField(value.ToString())).GetCustomAttributes(typeof(DescriptionAttribute), false);
            return da.Length > 0 ? da[0].Description : value.ToString();
        }

    }
}
