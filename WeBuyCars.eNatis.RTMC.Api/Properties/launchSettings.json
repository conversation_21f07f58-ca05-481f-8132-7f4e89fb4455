{"iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:41595", "sslPort": 44362}}, "$schema": "http://json.schemastore.org/launchsettings.json", "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "WeBuyCars.eNatis.RTMC.Api_LOCAL": {"commandName": "Project", "launchBrowser": true, "launchUrl": "index.html", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Local"}}, "WeBuyCars.eNatis.RTMC.Api_DEV": {"commandName": "Project", "launchBrowser": true, "launchUrl": "index.html", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "WeBuyCars.eNatis.RTMC.Api_STAGING": {"commandName": "Project", "launchBrowser": true, "launchUrl": "index.html", "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Staging"}}, "WeBuyCars.eNatis.RTMC.Api": {"commandName": "Project", "launchBrowser": true, "applicationUrl": "https://localhost:5001;http://localhost:5000", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Production"}}}}