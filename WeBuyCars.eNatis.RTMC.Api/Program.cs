using System;
using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.Hosting;
using WeBuyCars.Core.Extensions;
using Serilog;

namespace WeBuyCars.eNatis.RTMC.Api
{
	public class Program
	{
		public static void Main(string[] args)
		{

			try
			{
				CreateHostBuilder(args).Build().Run();
			}
			catch (Exception ex)
			{
                Log.Error(ex, "Stopped program because of exception");

			}
			finally
			{
                Log.CloseAndFlush();
			}
		}

		public static IHostBuilder CreateHostBuilder(string[] args) =>
		    Host.CreateDefaultBuilder(args)
                .ConfigureWebHostDefaults(webBuilder =>
                {
                    webBuilder.ConfigureAppConfiguration((_, config) =>
                    {
                        config.AddAzureAppConfigurations();
                    });
                    webBuilder.UseStartup<Startup>();
                }).ConfigureLogging((context, logging) =>
                {
                    logging.ConfigureSerilog(context);
                })
                .UseSerilog();

	}
}
