using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberDecryptionRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberDecryptionResponse;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.NCO;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.Registration;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.ArchivedInformation
{
    public class ArchivedInformationService
    {

        readonly ILogger<ArchivedInformationService> _logger;
        readonly IRTMCVehicleDetailRepository _rTMCVehicleDetailRepository;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly SharedServices _sharedServices;
        readonly RTMCVehicleDetailedInformationService _rTMCVehicleDetailedInformationService;
        readonly IRTMCVehicleOwnerOnlineNCORepository _rTMCVehicleOwnerOnlineNCORepository;
        readonly IRTMCVehicleOwnerRegistrationDetailRepository _rTMCVehicleOwnerRegistrationDetailRepository;
        //private readonly IDecryptionService _decryptionService;
        //private readonly IRSACryptoService _rsaEncryptDecryptService;
        private readonly IAesEncryptionDecryptionService _aesEncryptionDecryptionService;

        readonly IMapper _mapper;

        #region ctor
            
        public ArchivedInformationService(
            ILogger<ArchivedInformationService> logger,
            IRTMCVehicleDetailRepository rTMCVehicleDetailRepository,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCVehicleDetailedInformationService rTMCVehicleDetailedInformationService,
            IRTMCVehicleOwnerOnlineNCORepository rTMCVehicleOwnerOnlineNCORepository,
            IRTMCVehicleOwnerRegistrationDetailRepository rTMCVehicleOwnerRegistrationDetailRepository,
            // IDecryptionService decryptionService,
            // IRSACryptoService rsaEncryptDecryptService,
            IAesEncryptionDecryptionService aesEncryptionDecryptionService,
            IMapper mapper
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _rTMCVehicleDetailRepository = rTMCVehicleDetailRepository ?? throw new System.ArgumentNullException(nameof(rTMCVehicleDetailRepository));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCVehicleDetailedInformationService = rTMCVehicleDetailedInformationService ?? throw new ArgumentNullException(nameof(rTMCVehicleDetailedInformationService));
            _rTMCVehicleOwnerOnlineNCORepository = rTMCVehicleOwnerOnlineNCORepository ?? throw new ArgumentNullException(nameof(_rTMCVehicleOwnerOnlineNCORepository));
            _rTMCVehicleOwnerRegistrationDetailRepository = rTMCVehicleOwnerRegistrationDetailRepository ?? throw new ArgumentNullException(nameof(rTMCVehicleOwnerRegistrationDetailRepository));
            _aesEncryptionDecryptionService = aesEncryptionDecryptionService ?? throw new ArgumentNullException(nameof(aesEncryptionDecryptionService));
            // _rsaEncryptDecryptService = rsaEncryptDecryptService ?? throw new ArgumentNullException(nameof(rsaEncryptDecryptService));
            //_decryptionService = decryptionService ?? throw new ArgumentNullException(nameof(decryptionService));
        }

        #endregion

        #region public methods

        public async Task<VehicleDetailArchivedResponse> GetVehicleHistoricInformationAsync(VehicleDetailRequest vehicleRequest, TimeSpan? cacheTimeToLive, string environmentName)
        {

            VehicleDetailArchivedResponse vehicleDetailArchiveResponse = new VehicleDetailArchivedResponse();

            vehicleRequest.MessageId = Guid.NewGuid();

            try{

                //Log Request
                var dbVehicleRequest = await _sharedServices.SaveRequest("GetVehicleHistoricInformationAsync",vehicleRequest);

                Expression<Func<RTMCVehicleDetail, bool>> exp = null;

                if(vehicleRequest.Vin != null)
                {
                    exp = (x => x.VinOrChassis == vehicleRequest.Vin);
                }
                
                if(vehicleRequest.RegisterNumber != null)
                {
                    exp = (x => x.RegisterNumber == vehicleRequest.RegisterNumber);
                }
                
                if(vehicleRequest.LicenceNumber != null)
                {
                    exp = (x => x.LicenceNumber == vehicleRequest.LicenceNumber);
                }

                if(vehicleRequest.EngineNumber != null)
                {
                    exp = (x => x.EngineNumber == vehicleRequest.EngineNumber);
                }

                //Retrieve archived Record
                var rTMCVehicleDetail  = await _rTMCVehicleDetailRepository.LastOrDefaultAsync(exp);

                if(rTMCVehicleDetail == null)
                {
                    rTMCVehicleDetail = await GetVehicleInformationfromRTMC(vehicleRequest);
                }

                if(cacheTimeToLive != null)
                {

                    var cacheTimeToLiveValue = cacheTimeToLive.Value;
                    //Determine the Difference between current Date and Last Record to decide if a New Call must be made to get Vehicle Information                
                    var recordDate = rTMCVehicleDetail.CreatedOn;

                    var cachedRecordTime = recordDate.Add(cacheTimeToLiveValue);

                    if(cachedRecordTime < DateTimeOffset.Now)
                    {

                        //Retrieve New Natis Record
                        var vehicleDetailResponse = await _rTMCVehicleDetailedInformationService.GetVehicleDetailInformationAsync(vehicleRequest, environmentName);

                        //Map Vehicle Detail Response to RTMCObject
                        vehicleDetailArchiveResponse = _mapper.Map<VehicleDetailArchivedResponse>(vehicleDetailResponse);
                    }else
                    {
                         vehicleDetailArchiveResponse = _mapper.Map<VehicleDetailArchivedResponse>(rTMCVehicleDetail);
                    }
                }else
                {
                     vehicleDetailArchiveResponse = _mapper.Map<VehicleDetailArchivedResponse>(rTMCVehicleDetail);
                }

                //Vehicle was not found
                if(vehicleDetailArchiveResponse.RegisterNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Vehicle Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    vehicleDetailArchiveResponse.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbVehicleRequest, vehicleDetailArchiveResponse);

            }catch(Exception ex)
            {
                _logger.LogError("ArchivedInformationService : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());     
                throw;                
            }

            return vehicleDetailArchiveResponse;
        }

        public async Task<List<VehicleDetailArchivedResponse>> GetVehicleHistoricInformationListAsync(VehicleDetailRequest vehicleRequest)
        {
            List<VehicleDetailArchivedResponse> vehicleDetailResponseList = new List<VehicleDetailArchivedResponse>();

            //Log Request
            var dbVehicleRequest = await _sharedServices.SaveRequest("GetVehicleHistoricInformationListAsync",vehicleRequest);

            // //Get List of Records for Vehicle Detail

            Expression<Func<RTMCVehicleDetail, bool>> exp = null;

            if(vehicleRequest.Vin != null)
            {
                exp = (x => x.VinOrChassis == vehicleRequest.Vin);
            }
            
            if(vehicleRequest.RegisterNumber != null)
            {
                exp = (x => x.RegisterNumber == vehicleRequest.RegisterNumber);
            }
            
            if(vehicleRequest.LicenceNumber != null)
            {
                exp = (x => x.LicenceNumber == vehicleRequest.LicenceNumber);
            }

            if(vehicleRequest.EngineNumber != null)
            {
                exp = (x => x.EngineNumber == vehicleRequest.EngineNumber);
            }

            var rTMCVehicleDetailList  = await _rTMCVehicleDetailRepository.Where(exp);

            //Map VehicleDetailResponse = RTMCVehicleDetail
            vehicleDetailResponseList = _mapper.Map<List<VehicleDetailArchivedResponse>>(rTMCVehicleDetailList);

            //Log Response
            await _sharedServices.SaveResponse(dbVehicleRequest,vehicleDetailResponseList);

            return vehicleDetailResponseList;
        }

        public async Task<VehicleDetailArchivedResponse> GetVehicleDetailedRequestByReferenceAsync(VehicleDetailArchivedByReferenceRequest vehicleDetailArchivedByReferenceRequest){

            VehicleDetailArchivedResponse vehicleDetailArchivedResponse = new VehicleDetailArchivedResponse();

            try{

                //Log Request
                var dbVehicleRequest = await _sharedServices.SaveRequest("GetVehicleDetailedRequestByReferenceAsync",vehicleDetailArchivedByReferenceRequest);

                Expression<Func<RTMCResponse, bool>> exp = null;

                if(vehicleDetailArchivedByReferenceRequest.Reference != null)
                {
                    exp = (x => x.Reference == vehicleDetailArchivedByReferenceRequest.Reference);
                }

                //Retrieve archived Record
                var rTMCVehicleDetailResponse  = await _rTMCResponseRepository.LastOrDefaultAsync(exp);

                vehicleDetailArchivedResponse = JsonConvert.DeserializeObject<VehicleDetailArchivedResponse>(rTMCVehicleDetailResponse.ResponseObject);

                //Vehicle was not found
                if(vehicleDetailArchivedResponse.RegisterNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Vehicle Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    vehicleDetailArchivedResponse.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbVehicleRequest, vehicleDetailArchivedResponse);

            }catch(Exception ex)
            {
                _logger.LogError("ArchivedInformationService : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());     
                vehicleDetailArchivedResponse.BaseResponse = new BaseResponse(){
                    Status = "Failed"
                    , Message = "Exception for Vehicle Detail Request Exception : " + ex.ToString()
                    , StatusCode = "WBC001"
                };                
            }

            return vehicleDetailArchivedResponse;
        }

        public async Task<ControlNumberDecryptionResponse> DecryptControlNumberAsync(ControlNumberDecryptionRequest controlNumberDecryptionRequest)
        {
            var result = new ControlNumberDecryptionResponse();
            var baseResponse = new BaseResponse();

            if(!controlNumberDecryptionRequest.MessageId.HasValue){
                //Create GUID for MessageId
                Guid messageId = Guid.NewGuid();
                controlNumberDecryptionRequest.MessageId = messageId;
            }

            try{

                //Assign Request GUID
                baseResponse.Reference = controlNumberDecryptionRequest.MessageId.Value;

                //Log Request
                var dbcControlNumberDecryptionRequest = await _sharedServices.SaveRequest("DecryptControlNumberAsync",controlNumberDecryptionRequest);

                var decryptedValue = "";
                
                //Decrypt
                // decryptedValue = _decryptionService.DecryptFromByte(controlNumberDecryptionRequest.EncryptedControlNumber);
                decryptedValue = _sharedServices.ConvertByteArrayToString(_aesEncryptionDecryptionService.Decrypt(controlNumberDecryptionRequest.EncryptedControlNumber));
                result.ControlNumber = decryptedValue;

                baseResponse.Successful = true;
                baseResponse.Message = "Successfully Decrypted Control Number";
                result.BaseResponse = baseResponse;

                //Log Response
                await _sharedServices.SaveResponse(dbcControlNumberDecryptionRequest,"Decrypted Value was sent to Caller");

            }catch(Exception ex)
            {
                baseResponse.Successful = false;
                baseResponse.Message = "Failed to Decrypt the ControlNumber Value presented";
                result.BaseResponse = baseResponse;

                _logger.LogError("ArchivedInformationService : Exception in DecryptControlNumberAsync | Failed to Decrypt Control Number | Control Number : " + controlNumberDecryptionRequest.EncryptedControlNumber + " : Exception :" + ex.ToString());
            }
            return result;
        }

        public async Task<NCOArchivedRequestByReferenceResponse> GetNCODecryptedInformationRequestByReferenceAsync(NCOArchivedRequestByReferenceRequest nCOArchivedRequestByReferenceRequest){

            NCOArchivedRequestByReferenceResponse vehicleOwnerOnlineNCODetailResponse = new NCOArchivedRequestByReferenceResponse();

            try{

                //Log Request
                var dbVehicleRequest = await _sharedServices.SaveRequest("GetNCODecryptedInformationRequestByReferenceAsync",nCOArchivedRequestByReferenceRequest);

                Expression<Func<RTMCVehicleOwnerOnlineNCODetail, bool>> exp = null;

                if(nCOArchivedRequestByReferenceRequest.Reference != null)
                {
                    exp = (x => x.Reference == nCOArchivedRequestByReferenceRequest.Reference);
                }

                //Retrieve NCO Record
                var rTMCVehicleOwnerOnlineNCODetail  = await _rTMCVehicleOwnerOnlineNCORepository.LastOrDefaultAsync(exp);

                vehicleOwnerOnlineNCODetailResponse = _mapper.Map<NCOArchivedRequestByReferenceResponse>(rTMCVehicleOwnerOnlineNCODetail);

                if(rTMCVehicleOwnerOnlineNCODetail.EncryptedBarcode != null)
                {
                    //Decrypt the Barcode
                    //vehicleOwnerOnlineNCODetailResponse.Barcode = _decryptionService.DecryptFromByte(rTMCVehicleOwnerOnlineNCODetail.EncryptedBarcode);
                    vehicleOwnerOnlineNCODetailResponse.Barcode = _sharedServices.ConvertByteArrayToString(_aesEncryptionDecryptionService.Decrypt(rTMCVehicleOwnerOnlineNCODetail.EncryptedBarcode));
                    vehicleOwnerOnlineNCODetailResponse.Barcode = _sharedServices.Base64Decode(vehicleOwnerOnlineNCODetailResponse.Barcode);

                }

                if(rTMCVehicleOwnerOnlineNCODetail.EncryptedVehicleCertificateNumber != null)
                {
                    //Decrypt the Control Number
                    //vehicleOwnerOnlineNCODetailResponse.VehicleCertificateNumber = _decryptionService.DecryptFromByte(rTMCVehicleOwnerOnlineNCODetail.EncryptedVehicleCertificateNumber);
                    vehicleOwnerOnlineNCODetailResponse.VehicleCertificateNumber = _sharedServices.ConvertByteArrayToString(_aesEncryptionDecryptionService.Decrypt(rTMCVehicleOwnerOnlineNCODetail.EncryptedVehicleCertificateNumber));
                }

                //Registration Liability Date Format
                vehicleOwnerOnlineNCODetailResponse.DateOfLiabilityForRegistration = _sharedServices.ConvertToYYYYMMDD(vehicleOwnerOnlineNCODetailResponse.DateOfLiabilityForRegistration);

                //Vehicle was not found
                if(vehicleOwnerOnlineNCODetailResponse.RegisterNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "NCO Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    vehicleOwnerOnlineNCODetailResponse.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbVehicleRequest, vehicleOwnerOnlineNCODetailResponse);

            }catch(Exception ex)
            {
                _logger.LogError("ArchivedInformationService : Exception in GetNCODecryptedInformationRequestByReferenceAsync : Exception :" + ex.ToString());     
                vehicleOwnerOnlineNCODetailResponse.BaseResponse = new BaseResponse(){
                    Status = "Failed"
                    , Message = "Exception for NCO Request Exception : " + ex.ToString()
                    , StatusCode = "WBC001"
                };                
            }

            return vehicleOwnerOnlineNCODetailResponse;
        }

        public async Task<RegistrationArchivedRequestByReferenceResponse> GetVehicleOwnerRegistrationByReferenceAsync(RegistrationArchivedRequestByReferenceRequest registrationArchivedRequestByReferenceRequest){

            RegistrationArchivedRequestByReferenceResponse registrationArchivedRequestByReferenceResponse = new RegistrationArchivedRequestByReferenceResponse();

            try{

                //Log Request
                var dbVehicleRequest = await _sharedServices.SaveRequest("GetVehicleOwnerRegistrationByReferenceAsync",registrationArchivedRequestByReferenceRequest);

                Expression<Func<RTMCVehicleOwnerRegistrationDetail, bool>> exp = null;

                if(registrationArchivedRequestByReferenceRequest.Reference != null)
                {
                    exp = (x => x.Reference == registrationArchivedRequestByReferenceRequest.Reference);
                }

                //Retrieve Registration Record
                var registrationArchivedRequestByReferenceDetail  = await _rTMCVehicleOwnerRegistrationDetailRepository.LastOrDefaultAsync(exp);

                registrationArchivedRequestByReferenceResponse = _mapper.Map<RegistrationArchivedRequestByReferenceResponse>(registrationArchivedRequestByReferenceDetail);

                //Decrypt the Control Number
                //Leave this Encrypted
                //registrationArchivedRequestByReferenceResponse.VehicleCertificateNumber = _decryptionService.Decrypt(registrationArchivedRequestByReferenceResponse.VehicleCertificateNumber);

                //Vehicle was not found
                if(registrationArchivedRequestByReferenceResponse.RegisterNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "NCO Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    registrationArchivedRequestByReferenceResponse.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbVehicleRequest, registrationArchivedRequestByReferenceResponse);

            }catch(Exception ex)
            {
                _logger.LogError("ArchivedInformationService : Exception in GetVehicleOwnerRegistrationByReferenceAsync : Exception :" + ex.ToString());     
                registrationArchivedRequestByReferenceResponse.BaseResponse = new BaseResponse(){
                    Status = "Failed"
                    , Message = "Exception for NCO Request Exception : " + ex.ToString()
                    , StatusCode = "WBC001"
                };                
            }

            return registrationArchivedRequestByReferenceResponse;
        }

        #endregion

        #region private methods
        private async Task<RTMCVehicleDetail> GetVehicleInformationfromRTMC(VehicleDetailRequest vehicleRequest)
        {
            RTMCVehicleDetail rTMCVehicleDetail = new RTMCVehicleDetail();
            try{

                Guid auditLogId = Guid.NewGuid();
                //Retrieve New Record from RTMC
                var natisVehicleRequest = _mapper.Map<NatisGetVehicleDetailedRequest>(vehicleRequest);
                var natisVehicleDetail = await _natisIntegrationService.GetVehicleDetailedQuery(natisVehicleRequest, auditLogId);
                rTMCVehicleDetail = _mapper.Map<RTMCVehicleDetail>(natisVehicleDetail);

            }catch(Exception ex)
            {  
                _logger.LogWarning("GetVehicleHistoricInformationAsync : GetVehicleInformationfromRTMC | Error getting Vehicle Detail Information from RTMC : vehicleRequest : " + vehicleRequest.ToString() + "| Exception : " + ex);
            }

            return rTMCVehicleDetail;
        }

        #endregion

    }

}