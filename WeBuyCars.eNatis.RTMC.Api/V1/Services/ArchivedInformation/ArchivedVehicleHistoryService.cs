using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.ArchivedInformation
{
    public class ArchivedVehicleHistoryService
    {

        readonly ILogger<ArchivedVehicleHistoryService> _logger;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly SharedServices _sharedServices;
        readonly RTMCVehicleDetailedInformationService _rTMCVehicleDetailedInformationService;
        readonly IRTMCOwnershipHistoryDetailRepository _rTMCOwnershipHistoryDetailRepository;

        readonly IMapper _mapper;
    

        #region ctor
            
        public ArchivedVehicleHistoryService(
            ILogger<ArchivedVehicleHistoryService> logger,
            IRTMCVehicleDetailRepository rTMCVehicleDetailRepository,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCVehicleDetailedInformationService rTMCVehicleDetailedInformationService,
            IRTMCOwnershipHistoryDetailRepository rTMCOwnershipHistoryDetailRepository,
            IMapper mapper
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCVehicleDetailedInformationService = rTMCVehicleDetailedInformationService ?? throw new ArgumentNullException(nameof(rTMCVehicleDetailedInformationService));
            _rTMCOwnershipHistoryDetailRepository = rTMCOwnershipHistoryDetailRepository ?? throw new ArgumentNullException(nameof(rTMCOwnershipHistoryDetailRepository));
        }

        #endregion

        #region public methods

        public async Task<OwnershipHistoryDataResponse> GetVehicleOwnershipHistoryAsync(OwnershipHistoryDataArchivedRequest ownershipHistoryDataArchivedRequest)
        {

            OwnershipHistoryDataResponse vehicleOwnershipHistoryArchiveResponse = new OwnershipHistoryDataResponse();

            BaseResponse baseResponse = new BaseResponse();

            try{

                //Validation Check for the Reference Number.
                Expression<Func<RTMCOwnershipHistoryDetail, bool>> requestExpression = (x => x.RequestId == ownershipHistoryDataArchivedRequest.Reference);

                //Retrieve Request Record
                var ownershipHistoryDetailsList  = await _rTMCOwnershipHistoryDetailRepository.Where(requestExpression);

                if(ownershipHistoryDetailsList == null || ownershipHistoryDetailsList.Count == 0)
                {
                    vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "No Records found with that Reference Number", "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                    return vehicleOwnershipHistoryArchiveResponse;
                }
                else
                {

                    //Map Loaded Data to the Response Object
                    var vehicleResponse = new VehicleResponse()
                    {
                        vinOrChassis = ownershipHistoryDetailsList[0].VinOrChassis,
                        registerNumber = ownershipHistoryDetailsList[0].RegisterNumber,
                        licenceNumber = ownershipHistoryDetailsList[0].LicenseNumber
                    };

                    var ownershipHistoryResponseList = new List<OwnershipHistoryResponse>();

                    foreach (var item in ownershipHistoryDetailsList)
                    {

                        var ownerHistoryResponse = new OwnershipHistoryResponse();
                        var ownerResponse = new OwnerResponse();

                        ownerResponse.name = item.EntityName;    

                        if(ownershipHistoryDataArchivedRequest.IncludeSensitiveInformation == true)
                        {
                            ownerResponse.identificationNumber = item.IdentificationNumber;

                        }else
                        {
                            string lastFourDigits = item.IdentificationNumber.Substring(item.IdentificationNumber.Length - 4);
                            ownerResponse.identificationNumber = "*********"+ lastFourDigits;
                        }                       

                        ownerResponse.ownershipStatus = item.OwnershipStatus;
                        ownerResponse.ownershipDate = item.OwnershipDate;
                        ownerResponse.ownershipType = item.OwnershipType;
                        ownerResponse.insuranceCompany = item.InsuranceCompany;

                        ownerHistoryResponse.owner = ownerResponse;

                        ownershipHistoryResponseList.Add(ownerHistoryResponse);
                    }

                    var ownershipHistoryResponseArray = ownershipHistoryResponseList.ToArray();

                    //Add Main Response Objects
                    vehicleOwnershipHistoryArchiveResponse.vehicle = vehicleResponse;
                    vehicleOwnershipHistoryArchiveResponse.ownershipHistory = ownershipHistoryResponseArray;

                    vehicleOwnershipHistoryArchiveResponse.CreatedOn = ownershipHistoryDetailsList[0].CreatedOn;

                    vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Success", true, "Successfully Retrieved Vehicle History Information", "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                    return vehicleOwnershipHistoryArchiveResponse;
                }        

            }catch(Exception ex)
            {
                _logger.LogError("GetVehicleHistoricInformationAsync : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());

                vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "An Error ocurred when trying to Retrieve Vehicle History | Exception : " + ex.ToString(), "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                return vehicleOwnershipHistoryArchiveResponse;                 
            }

        }



        //Retrieve Archived Information
        public async Task<OwnershipHistoryDataResponse> GetVehicleOwnershipHistoryFromRequestAsync(OwnershipHistoryDataArchivedRequest ownershipHistoryDataArchivedRequest)
        {

            OwnershipHistoryDataResponse vehicleOwnershipHistoryArchiveResponse = new OwnershipHistoryDataResponse();

            BaseResponse baseResponse = new BaseResponse();

            try{

                Expression<Func<RTMCRequest, bool>> requestExpression = (x => x.Reference == ownershipHistoryDataArchivedRequest.Reference);
                Expression<Func<RTMCResponse, bool>> responseExpression = (x => x.Reference == ownershipHistoryDataArchivedRequest.Reference);

                //Retrieve Request Record
                var rTMCRequest  = await _rTMCRequestRepository.LastOrDefaultAsync(requestExpression);

                if(rTMCRequest != null)
                {

                    //Verify if the Request Type is the same as the Sames as the Controller Call
                    if(rTMCRequest.IntegrationType == "GetOwnershipHistory")
                    {

                        var rTMCResponse  = await _rTMCResponseRepository.LastOrDefaultAsync(responseExpression);

                        if(rTMCResponse != null)
                        {

                            vehicleOwnershipHistoryArchiveResponse = JsonConvert.DeserializeObject<OwnershipHistoryDataResponse>(rTMCResponse.ResponseObject);

                            vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Success", true, "Successfully Retrieved Vehicle History Information", "WBC001", ownershipHistoryDataArchivedRequest.Reference);

                            foreach (var item in vehicleOwnershipHistoryArchiveResponse.ownershipHistory)
                            {
                                if(ownershipHistoryDataArchivedRequest.IncludeSensitiveInformation != true)
                                {
                                    string lastFourDigits = item.owner.identificationNumber.Substring(item.owner.identificationNumber.Length - 4);
                                    item.owner.identificationNumber = "*********"+ lastFourDigits;
                                }   
                            }


                            //Populate Transaction Time
                            vehicleOwnershipHistoryArchiveResponse.CreatedOn = rTMCResponse.CreatedOn;

                            return vehicleOwnershipHistoryArchiveResponse;  

                        }else
                        {
                            vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Vehicle History Response Information not found", "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                            return vehicleOwnershipHistoryArchiveResponse;
                        }
                    }else
                    {
                        vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Incorrect GUID for Integration Type", "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                        return vehicleOwnershipHistoryArchiveResponse;
                    }

                }else
                {
                    vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Vehicle History Request Information not found", "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                    return vehicleOwnershipHistoryArchiveResponse;
                }           

            }catch(Exception ex)
            {
                _logger.LogError("GetVehicleHistoricInformationAsync : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());

                vehicleOwnershipHistoryArchiveResponse.BaseResponse = PopulateBaseResponse("Failed", false, "An Error ocurred when trying to Retrieve Vehicle History | Exception : " + ex.ToString(), "WBC001", ownershipHistoryDataArchivedRequest.Reference);
                return vehicleOwnershipHistoryArchiveResponse;                 
            }

        }

        #endregion

        #region private methods
        private async Task<RTMCVehicleDetail> GetVehicleInformationfromRTMC(VehicleDetailRequest vehicleRequest)
        {
            RTMCVehicleDetail rTMCVehicleDetail = new RTMCVehicleDetail();
            try{

                Guid auditLogId = Guid.NewGuid();

                //Retrieve New Record from RTMC
                var natisVehicleRequest = _mapper.Map<NatisGetVehicleDetailedRequest>(vehicleRequest);
                var natisVehicleDetail = await _natisIntegrationService.GetVehicleDetailedQuery(natisVehicleRequest , auditLogId);
                rTMCVehicleDetail = _mapper.Map<RTMCVehicleDetail>(natisVehicleDetail);

            }catch(Exception ex)
            {  
                _logger.LogWarning("GetVehicleHistoricInformationAsync : GetVehicleInformationfromRTMC | Error getting Vehicle Detail Information from RTMC : vehicleRequest : " + vehicleRequest.ToString() + "| Exception : " + ex);
            }

            return rTMCVehicleDetail;
        }

        private BaseResponse PopulateBaseResponse(string status, bool successful, string message, string statusCode, Guid reference ){

            BaseResponse baseResponse = new BaseResponse();

            baseResponse.Status = status;
            baseResponse.Successful = successful;
            baseResponse.Message = message;
            baseResponse.StatusCode = statusCode;
            baseResponse.Reference = reference;

            return baseResponse;
        }

        #endregion

    }

}