using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Data.Repositories;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.ArchivedInformation
{
    public class ArchivedOwnerVehicleHistoryService
    {

        readonly ILogger<ArchivedOwnerVehicleHistoryService> _logger;
        readonly SharedServices _sharedServices;
        readonly IRTMCOwnershipHistoryDetailRepository _rTMCOwnershipHistoryDetailRepository;
        readonly IMapper _mapper;
    

        #region ctor
            
        public ArchivedOwnerVehicleHistoryService(
            ILogger<ArchivedOwnerVehicleHistoryService> logger,
            SharedServices sharedServices,
            IRTMCOwnershipHistoryDetailRepository rTMCOwnershipHistoryDetailRepository,
            IMapper mapper
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCOwnershipHistoryDetailRepository = rTMCOwnershipHistoryDetailRepository ?? throw new ArgumentNullException(nameof(rTMCOwnershipHistoryDetailRepository));
        }

        #endregion

        #region public methods

        public async Task<VehiclesOwnedByEntityResponse> GetVehicleOwnershipHistoryByIdentifierNumberAsync(string identificationNumber)
        {

            //Generate New GUID
            Guid messageId = Guid.NewGuid();

            VehiclesOwnedByEntityResponse vehiclesOwnedByEntityArchivedResponse = new VehiclesOwnedByEntityResponse();

            BaseResponse baseResponse = new BaseResponse();

            //Validate Input Request
            if(String.IsNullOrWhiteSpace(identificationNumber))
            {
                vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Identification Number cannot be blank or Empty", "WBC001", messageId);
                return vehiclesOwnedByEntityArchivedResponse;
            }

            if(identificationNumber.Length > 13)
            {
                vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Identification Number cannot be more than 13 Characters", "WBC001", messageId);
                return vehiclesOwnedByEntityArchivedResponse;
            }

            identificationNumber = FormatInputRequest(identificationNumber);

            try{

                //Validation Check for the Identification Number.
                Expression<Func<RTMCOwnershipHistoryDetail, bool>> requestExpression = (x => x.IdentificationNumber == identificationNumber);

                //Retrieve Request Record
                var ownershipHistoryDetailsList  = await _rTMCOwnershipHistoryDetailRepository.Where(requestExpression);

                if(ownershipHistoryDetailsList == null || ownershipHistoryDetailsList.Count == 0)
                {
                    vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Failed", false, "No Records found with that Identification Number", "WBC001", messageId);

                    return vehiclesOwnedByEntityArchivedResponse;
                }

                //Record List Found
                if(ownershipHistoryDetailsList != null)
                {

                    //Convert List to Response Object
                    var ownershipDataList = _mapper.Map<List<VehicleOwnershipInformation>>(ownershipHistoryDetailsList);

                    vehiclesOwnedByEntityArchivedResponse.vehicleOwnershipInformationList = ownershipDataList.ToArray();
                    vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Success", true, "Successfully Retrieved Vehicle History Information", "WBC001", messageId);

                    return vehiclesOwnedByEntityArchivedResponse;  

                }else
                {
                    vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Failed", false, "Vehicle History Request Information not found", "WBC001", messageId);
                    return vehiclesOwnedByEntityArchivedResponse;
                }           

            }catch(Exception ex)
            {
                _logger.LogError("GetVehicleOwnershipHistoryByIdentifierNumberAsync : Exception in GetVehicleOwnershipHistoryByIdentifierNumberAsync : Exception :" + ex.ToString());

                vehiclesOwnedByEntityArchivedResponse.BaseResponse = PopulateBaseResponse("Failed", false, "An Error ocurred when trying to Retrieve Owners Vehicle History | Exception : " + ex.ToString(), "WBC001", messageId);
                return vehiclesOwnedByEntityArchivedResponse;                 
            }

        }


        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="identificationNumber"></param>
        /// <returns></returns>
        private string FormatInputRequest(string identificationNumber){

            if(!String.IsNullOrEmpty(identificationNumber)){

                //Remove Special Characters
                identificationNumber = HelperExtensions.RemoveSpecialCharacters(identificationNumber);
                //UpperCase
                identificationNumber = identificationNumber.ToUpper();
            }
            
            return identificationNumber;
        }

        private BaseResponse PopulateBaseResponse(string status, bool successful, string message, string statusCode, Guid reference ){

            BaseResponse baseResponse = new BaseResponse();

            baseResponse.Status = status;
            baseResponse.Successful = successful;
            baseResponse.Message = message;
            baseResponse.StatusCode = statusCode;
            baseResponse.Reference = reference;

            return baseResponse;
        }

        #endregion

    }

}