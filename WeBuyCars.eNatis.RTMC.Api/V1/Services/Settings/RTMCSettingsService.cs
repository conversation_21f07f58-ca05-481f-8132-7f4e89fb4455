using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using System.Linq.Expressions;
using System.Collections.Generic;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;
using Microsoft.Extensions.Options;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using System.Net.Http.Json;
using System.Net.Http.Headers;
using System.Threading;
using Microsoft.Extensions.Caching.Memory;
using System.IdentityModel.Tokens.Jwt;
using System.Reflection;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings
{
    public class RTMCSettingsService
    {

        readonly ILogger<RTMCSettingsService> _logger;
        readonly IMapper _mapper;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly IRTMCSettingsRepository _rTMCSettingsRepository;
        readonly SharedServices _sharedServices;
        // private readonly IEncryptionService _encryptionService;
        // private readonly IDecryptionService _decryptionService;
        private readonly HttpClient _httpClient;
        readonly eNatisEndPointOptions _endPointOptions;
        readonly eNatisServiceOptions _serviceOptions;
        private readonly IMemoryCache _cache;
        // private readonly IRSACryptoService _rsaEncryptDecryptService;
        private readonly IAesEncryptionDecryptionService _aesEncryptionDecryptionService;
        

        #region ctor
            
        public RTMCSettingsService(
            ILogger<RTMCSettingsService> logger,
            IMapper mapper,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            IRTMCSettingsRepository rTMCSettingsRepository,
            // IEncryptionService encryptionService,
            // IDecryptionService decryptionService,
            IOptionsMonitor<eNatisEndPointOptions> endpointOptions,
            HttpClient httpClient,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            IMemoryCache memoryCache,
            IAesEncryptionDecryptionService aesEncryptionDecryptionService
            // IRSACryptoService rsaEncryptDecryptService

        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _sharedServices = sharedServices ?? throw new System.ArgumentNullException(nameof(sharedServices));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _rTMCSettingsRepository = rTMCSettingsRepository ?? throw new ArgumentNullException(nameof(rTMCSettingsRepository));
            // _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            // _decryptionService = decryptionService ?? throw new ArgumentNullException(nameof(decryptionService));
            _endPointOptions = endpointOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(endpointOptions));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            // _rsaEncryptDecryptService = rsaEncryptDecryptService ?? throw new ArgumentNullException(nameof(rsaEncryptDecryptService));
            _aesEncryptionDecryptionService = aesEncryptionDecryptionService ?? throw new ArgumentNullException(nameof(aesEncryptionDecryptionService));
            _cache = memoryCache;

        }

        #endregion

        #region public methods

        public async Task<RTMCLoginDetailsResponse> GetRTMCLoginSettingsAsync(RTMCLoginDetailsRequest loginDetailRequest)
        {

            RTMCLoginDetailsResponse rTMCLoginDetailsResponse = new RTMCLoginDetailsResponse();

            try{

                //Log Request
                var dbLoginDetailRequest = await _sharedServices.SaveRequest("GetRTMCLoginSettingsAsync",loginDetailRequest);

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(loginDetailRequest.BusinessRegistrationNumber != null)
                {
                    exp = (x => x.BusinessRegistrationNumber == loginDetailRequest.BusinessRegistrationNumber);
                }

                //Retrieve archived Record
                var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(rTMCLoginDetail != null)
                {
                    //Map VehicleDetailResponse = LoginDetail
                    rTMCLoginDetailsResponse = _mapper.Map<RTMCLoginDetailsResponse>(rTMCLoginDetail);
                }

                //Login Details was not found
                if(rTMCLoginDetailsResponse.BusinessRegistrationNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Business Registration Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    rTMCLoginDetailsResponse.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbLoginDetailRequest, rTMCLoginDetailsResponse);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());     
                throw;                
            }

            return rTMCLoginDetailsResponse;
        }

        public async Task<RTMCCredentialsResponse> GetSOAPCredentialsByBRN(string brn){

            RTMCCredentialsResponse result = new RTMCCredentialsResponse();

            var cachedKey = $"RTMCSettings-{brn}";
            var cachedRecord = _cache.Get<RTMCCredentialsResponse>(cachedKey);
            //Check If Cached Record Exists
            if(cachedRecord != null){
                _logger.LogError("RTMCSettingsService : GetSOAPCredentialsByBRN Cache Hit for Key : " +  cachedKey);
                return cachedRecord;
            }

            _logger.LogError("RTMCSettingsService : GetSOAPCredentialsByBRN Cache Missed for Key : " +  cachedKey);
                
             var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultNoTrackingAsync(i => i.BusinessRegistrationNumber == brn && !i.Deleted);

             if(rTMCLoginDetail == null)
             {
                 _logger.LogError("RTMCSettingsService : GetSOAPCredentialsByBRN no BRN Login Details found for BRN : " +  brn);
                return null;
            }

            //rTMCLoginDetail.Password = _decryptionService.Decrypt(rTMCLoginDetail.Password);
            //result.DecryptedPassword = _decryptionService.DecryptFromByte(rTMCLoginDetail.EncryptedPassword);
            //result.DecryptedPassword = _sharedServices.ConvertByteArrayToString(_rsaEncryptDecryptService.Decrypt(rTMCLoginDetail.EncryptedPassword));
            result.DecryptedPassword = _sharedServices.ConvertByteArrayToString(_aesEncryptionDecryptionService.Decrypt(rTMCLoginDetail.EncryptedPassword));

            
            result.Username = rTMCLoginDetail.Username;
            result.BusinessRegistrationNumber = rTMCLoginDetail.BusinessRegistrationNumber;

            _cache.Set(cachedKey, result, TimeSpan.FromMinutes(5));

            return result;

        }

        public async Task<RTMCLoginDetailsResponse> GetRTMCLoginSettingsByBRNAsync(string businessRegistrationNumber)
        {

            RTMCLoginDetailsResponse rTMCLoginDetailsResponse = new RTMCLoginDetailsResponse();

            try{

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(businessRegistrationNumber != null)
                {
                    exp = (x => x.BusinessRegistrationNumber == businessRegistrationNumber);
                }

                //Retrieve archived Record
                var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(rTMCLoginDetail != null)
                {
                    //Map VehicleDetailResponse = LoginDetail
                    rTMCLoginDetailsResponse = _mapper.Map<RTMCLoginDetailsResponse>(rTMCLoginDetail);
                }

                //Login Details was not found
                if(rTMCLoginDetailsResponse.BusinessRegistrationNumber == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Business Registration Information not found"
                        , StatusCode = "WBC001"
                    };
                    
                    rTMCLoginDetailsResponse.BaseResponse = baseResponse;
                }

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());     
                throw;                
            }

            return rTMCLoginDetailsResponse;
        }

        public async Task<List<RTMCLoginDetailsListResponse>> GetRTMCLoginSettingsListAsync(RTMCLoginDetailsListRequest loginDetailListRequest)
        {
            List<RTMCLoginDetailsListResponse> rTMCLoginDetailsListResponse = new List<RTMCLoginDetailsListResponse>();

            try
            {

                //Log Request
                var dbLoginDetailRequest = await _sharedServices.SaveRequest("GetRTMCLoginSettingsListAsync",loginDetailListRequest);

                // //Get List of Records for Settings Detail

                Expression<Func<RTMCSettings, bool>> exp = null;

                exp = (x => x.Deleted != true);
                
                var rTMCLoginDetailList  = await _rTMCSettingsRepository.Where(exp);

                //Map SettingsResponse = RTMCSettings
                rTMCLoginDetailsListResponse = _mapper.Map<List<RTMCLoginDetailsListResponse>>(rTMCLoginDetailList);

                //Log Response
                await _sharedServices.SaveResponse(dbLoginDetailRequest,rTMCLoginDetailsListResponse);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetRTMCLoginSettingsListAsync : Exception :" + ex.ToString());     
                throw;
            }

            return rTMCLoginDetailsListResponse;
        }

        public async Task<RTMCLoginDetailsResponse> CreateRTMCLoginSettingsAsync(RTMCLoginDetails loginDetails){

            RTMCLoginDetailsResponse result = new RTMCLoginDetailsResponse();

            try
            {

                //Log Request
                var dbLoginDetailCreateRequest = await _sharedServices.SaveRequest("CreateRTMCLoginSettingsAsync",loginDetails);

                //Convert RTMCLoginDetails to RTMCSettings
                var dbSettingsRequest = _mapper.Map<RTMCSettings>(loginDetails);

                //Check for Duplicate
                var duplicate = await DuplicateCheck(loginDetails);

                if(duplicate)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Unable to create Login Settings, duplicate record"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };
                    
                    _logger.LogError("RTMCSettingsService : Exception in CreateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);
                    result.BaseResponse = baseResponse;
                    return result;
                }

                //Encrypt Password
                //dbSettingsRequest.Password = loginDetails.Password != null ? _encryptionService.Encrypt(loginDetails.Password) : loginDetails.Password;


                //dbSettingsRequest.EncryptedPassword = _encryptionService.EncryptToByteArray(loginDetails.Password);
                //dbSettingsRequest.EncryptedPassword = _rsaEncryptDecryptService.Encrypt(_sharedServices.ConvertStringToByteArray(loginDetails.Password));
                dbSettingsRequest.EncryptedPassword = _aesEncryptionDecryptionService.Encrypt(_sharedServices.ConvertStringToByteArray(loginDetails.Password));            

                //Save Login Request to DB
                var dbSettings = _rTMCSettingsRepository.AddRTMCSettings(dbSettingsRequest);
                var requestResult = _rTMCSettingsRepository.UnitOfWork.SaveChanges();

                //Login Details was not found
                if(requestResult == 0)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Unable to create Login Settings"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };
                    
                    _logger.LogError("RTMCSettingsService : Exception in CreateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);
                    result.BaseResponse = baseResponse;
                }

                if(requestResult == 1)
                {
                    //Map RTMC Settings back to Response Object
                    result = _mapper.Map<RTMCLoginDetailsResponse>(dbSettings);

                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Success"
                        , Message = "Created Login Settings Record"
                        , StatusCode = "WBC000"
                        , Successful = true
                    };
                    
                    result.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbLoginDetailCreateRequest, result);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in CreateRTMCLoginSettingsAsync : Exception :" + ex.ToString() + " | Request Object : " + loginDetails);

                throw;
            }

            return result;
        }

        public async Task<RTMCLoginDetailsResponse> UpdateRTMCLoginSettingsAsync(RTMCLoginDetails loginDetails){

            RTMCLoginDetailsResponse result = new RTMCLoginDetailsResponse();

            try
            {

                //Log Request
                var dbLoginDetailCreateRequest = await _sharedServices.SaveRequest("UpdateRTMCLoginSettingsAsync",loginDetails);

                //Blank Id passed in with object
                if(loginDetails.Id == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Id field must be provided for an Update on RTMC Settings"
                        , StatusCode = "WBC003"
                        , Successful = false
                    };

                    _logger.LogError("RTMCSettingsService : Exception in UpdateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);

                    result.BaseResponse = baseResponse;
                    return result;
                }

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(loginDetails.Id != null)
                {
                    exp = (x => x.Id == loginDetails.Id && x.Deleted == false);
                }

                //Retrieve archived Record
                var dbSettingsRequest  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(dbSettingsRequest == null)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Failed to find Login Settings for request, cannot update"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };

                    _logger.LogError("RTMCSettingsService : Exception in UpdateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);
                    
                    result.BaseResponse = baseResponse;
                    return result; 
                }

                //Check for Duplicate
                var duplicate = await DuplicateCheck(loginDetails);

                if(duplicate)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Unable to Update Login Settings, it will create a duplicate record"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };

                    _logger.LogError("RTMCSettingsService : Exception in UpdateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);
                    
                    result.BaseResponse = baseResponse;
                    return result;
                }

                //Map Input Request with DB Object
                dbSettingsRequest.BusinessRegistrationNumber = loginDetails.BusinessRegistrationNumber;
                dbSettingsRequest.Username = loginDetails.Username;
                //dbSettingsRequest.Password = loginDetails.Password != null ? _encryptionService.Encrypt(loginDetails.Password) : loginDetails.Password;

                //dbSettingsRequest.EncryptedPassword = _encryptionService.EncryptToByteArray(loginDetails.Password);
                //dbSettingsRequest.EncryptedPassword = _rsaEncryptDecryptService.Encrypt(_sharedServices.ConvertStringToByteArray(loginDetails.Password));
                dbSettingsRequest.EncryptedPassword = _aesEncryptionDecryptionService.Encrypt(_sharedServices.ConvertStringToByteArray(loginDetails.Password));            

                dbSettingsRequest.OwnerDocumentNumber = loginDetails.OwnerDocumentNumber;
                dbSettingsRequest.ProxyDocumentNumber = loginDetails.ProxyDocumentNumber;
                dbSettingsRequest.RepresentativeDocumentNumber = loginDetails.RepresentativeDocumentNumber;

                //Save Login Request to DB
                var dbSettings = _rTMCSettingsRepository.UpdateRTMCSettings(dbSettingsRequest);
                var requestResult = _rTMCSettingsRepository.UnitOfWork.SaveChanges();

                //Login Details was not found
                if(requestResult == 0)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Unable to Update Login Settings"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };
                    
                    _logger.LogError("RTMCSettingsService : Exception in UpdateRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails);

                    result.BaseResponse = baseResponse;
                }

                if(requestResult == 1)
                {
                    //Map RTMC Settings back to Response Object
                    result = _mapper.Map<RTMCLoginDetailsResponse>(dbSettings);

                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Success"
                        , Message = "Updated Login Settings Record"
                        , StatusCode = "WBC000"
                        , Successful = true
                    };
                    
                    result.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbLoginDetailCreateRequest, result);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in UpdateRTMCLoginSettingsAsync : Exception :" + ex.ToString());

                throw;
            }

            return result;
        }

        public async Task<RTMCLoginDetailsResponse> DeleteRTMCLoginSettingsAsync(RTMCDeleteRequest loginDetails){

            RTMCLoginDetailsResponse result = new RTMCLoginDetailsResponse();

            try
            {

                //Log Request
                var dbLoginDetailCreateRequest = await _sharedServices.SaveRequest("DeleteRTMCLoginSettingsAsync",loginDetails);

                //Blank Id passed in with object
                if(loginDetails.Id == null)
                {
                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Failed"
                        , Message = "Id field must be provided for Delete on RTMC Settings"
                        , StatusCode = "WBC003"
                        , Successful = false
                    };

                    _logger.LogError("RTMCSettingsService : Exception in DeleteRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails.Id);

                    result.BaseResponse = baseResponse;
                    return result;
                }

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(loginDetails.Id != null)
                {
                    exp = (x => x.Id == loginDetails.Id);
                }

                //Retrieve archived Record
                var dbSettingsRequest  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(dbSettingsRequest == null)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Failed to find Login Settings for request, cannot Delete"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };

                    _logger.LogError("RTMCSettingsService : Exception in DeleteRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails.Id);
                    
                    result.BaseResponse = baseResponse;
                    return result; 
                }

                //Map Input Request with DB Object
                dbSettingsRequest.Deleted = true;

                //Save Login Request to DB
                var dbSettings = _rTMCSettingsRepository.UpdateRTMCSettings(dbSettingsRequest);
                var requestResult = _rTMCSettingsRepository.UnitOfWork.SaveChanges();

                //Login Details was not found
                if(requestResult == 0)
                {
                    BaseResponse baseResponse = new BaseResponse()
                    {
                        Status = "Failed"
                        , Message = "Unable to Delete Login Settings"
                        , StatusCode = "WBC001"
                        , Successful = false
                    };
                    
                    _logger.LogError("RTMCSettingsService : Exception in DeleteRTMCLoginSettingsAsync : Error :" + baseResponse.Message + " | Request Object : " + loginDetails.Id);

                    result.BaseResponse = baseResponse;
                }

                if(requestResult == 1)
                {
                    //Map RTMC Settings back to Response Object
                    result = _mapper.Map<RTMCLoginDetailsResponse>(dbSettings);

                    BaseResponse baseResponse = new BaseResponse(){
                        Status = "Success"
                        , Message = "Deleted Login Settings Record"
                        , StatusCode = "WBC000"
                        , Successful = true
                    };
                    
                    result.BaseResponse = baseResponse;
                }

                //Log Response
                await _sharedServices.SaveResponse(dbLoginDetailCreateRequest, result);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in DeleteRTMCLoginSettingsAsync : Exception :" + ex.ToString());

                throw;
            }

            return result;
        }

        public async Task<AccessToken> GetAccessTokenAsync()
        {
            AccessToken accessToken = new AccessToken();

            try
            {

                var restTokenURL = _endPointOptions.RESTToken;

                var username = _serviceOptions.PayloadUsername;
                var password = _serviceOptions.PayloadPassword;


                HttpClient httpClient = new HttpClient();

                httpClient.DefaultRequestHeaders.Add($"Authorization", $"Basic {_sharedServices.Base64Encode($"{username}:{password}")}");
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));

                HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(restTokenURL,null).ConfigureAwait(false);

                var responseMessage = httpResponseMessage.Content.ReadAsStringAsync().Result;

                accessToken = JsonConvert.DeserializeObject<AccessToken>(responseMessage);

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetVehicleHistoricInformationAsync : Exception :" + ex.ToString());     
                throw;  
            }

            return accessToken;
        }

        public async Task<AccessToken> GetAccessTokenByBRNAsync(string businessRegistrationNumber)
        {

            //Testing new RSA Encryption and Decryption Methods

            //var password = "%RC238%173%4988A001%1%498800000019%BBB005Y%Sedan (closed top) / Sedan (toe-kap)%TOYOTA%COROLLA%AHT81111111111111%AHT81111111111111111%New / Nuut%2023-07-04%04%F151307720094%WE BUY CARS ONLINE%TEST%TEST%TEST%";

            // var password = "TESTER01";

            // var dbRecordByteArray =  Encoding.UTF8.GetBytes(password);

            // var encryptedPassword = _aesEncryptionDecryptionService.Encrypt(dbRecordByteArray);

            // var encryptedPasswordString = Encoding.UTF8.GetString(encryptedPassword);

            // var decryptedPassword = _aesEncryptionDecryptionService.Decrypt(encryptedPassword);

            // var decryptedPasswordString = Encoding.UTF8.GetString(decryptedPassword);

            //Error handling
            if(String.IsNullOrWhiteSpace(businessRegistrationNumber))
            {
                _logger.LogError("RTMCSettingsService : GetAccessTokenByBRNAsync : BRN Number cannot be blank");   
                throw new Exception();
            }

            AccessToken accessToken = new AccessToken();
            AccessTokenCache accessTokenCache = new AccessTokenCache();

            try
            {

                var cachedRecord = false;
                var dbRecord = false;
                var cachedRecordValid = false;

                //Check if there is a BRN in the cache
                cachedRecord = await ValidateCachedRecordExist(businessRegistrationNumber);

                if(cachedRecord)
                {
                    //If Cache Record Exist, validate if the Token is still Active
                    cachedRecordValid = await ValidateCacheActive(businessRegistrationNumber);

                    if(cachedRecordValid)
                    {
                        //Return Existing AccessToken back
                        accessToken = await RetrieveToken(businessRegistrationNumber);

                    }else{

                        //Generate New Token and Update the Record, dont need to hit DB for Credentials, just use Cached ones
                        accessTokenCache = await GenerateAccessTokenCache(businessRegistrationNumber);

                        //Update Cache with new Access Token
                        await UpdateCacheToken(businessRegistrationNumber, accessTokenCache);
                        accessToken = accessTokenCache.AccessToken;

                    }                    

                }else{
                    //If Cache Record Does Not Exist, validate if the BRN is actually in the DB.
                    dbRecord = await ValidateExistingBRNDBRecord(businessRegistrationNumber);
                    if(dbRecord){
                        //If DB record Exist, Generate Token
                        accessTokenCache = await GenerateAccessTokenDB(businessRegistrationNumber);

                        //Encrypt Password
                        //accessTokenCache = await EncryptAccessTokenCachePassword(accessTokenCache);

                        //Add Cache with new Access Token
                        await UpdateCacheToken(businessRegistrationNumber, accessTokenCache);
                        accessToken = accessTokenCache.AccessToken;
                    }else
                    {
                        //Return Error as the BRN Record is not in the DB
                        _logger.LogError("RTMCSettingsService : Error in GetAccessTokenByBRNAsync : The BRN record does not exist in the Database");
                        throw new Exception();
                    }

                }


            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetAccessTokenByBRNAsync : Exception : " + ex.ToString());     
                throw;  
            }

            return accessToken;
        }

        public async Task<AccessTokenCache> GetAccessTokenCacheByBRNAsync(string businessRegistrationNumber)
        {
            
            //Error handling
            if(String.IsNullOrWhiteSpace(businessRegistrationNumber))
            {
                _logger.LogError("RTMCSettingsService : GetAccessTokenByBRNAsync : BRN Number cannot be blank");   
                throw new Exception();
            }
            AccessTokenCache accessTokenCache = new AccessTokenCache();

            try
            {

                var cachedRecord = false;
                var dbRecord = false;
                var cachedRecordValid = false;

                //Check if there is a BRN in the cache
                cachedRecord = await ValidateCachedRecordExist(businessRegistrationNumber);

                if(cachedRecord)
                {
                    //If Cache Record Exist, validate if the Token is still Active
                    cachedRecordValid = await ValidateCacheActive(businessRegistrationNumber);

                    if(cachedRecordValid)
                    {
                        //Return Existing AccessToken back
                        accessTokenCache = await RetrieveAccessTokenCache(businessRegistrationNumber);

                    }else{

                        //Generate New Token and Update the Record, dont need to hit DB for Credentials, just use Cached ones
                        accessTokenCache = await GenerateAccessTokenCache(businessRegistrationNumber);

                        //Update Cache with new Access Token
                        await UpdateCacheToken(businessRegistrationNumber, accessTokenCache);

                    }                    

                }else{
                    //If Cache Record Does Not Exist, validate if the BRN is actually in the DB.
                    dbRecord = await ValidateExistingBRNDBRecord(businessRegistrationNumber);
                    if(dbRecord){
                        //If DB record Exist, Generate Token
                        accessTokenCache = await GenerateAccessTokenDB(businessRegistrationNumber);

                        //Encrypt Password
                        //accessTokenCache = await EncryptAccessTokenCachePassword(accessTokenCache);

                        //Add Cache with new Access Token
                        await UpdateCacheToken(businessRegistrationNumber, accessTokenCache);
                    }else
                    {
                        //Return Error as the BRN Record is not in the DB
                        _logger.LogError("RTMCSettingsService : Error in GetAccessTokenByBRNAsync : The BRN record does not exist in the Database");
                        throw new Exception();
                    }

                }


            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in GetAccessTokenByBRNAsync : BRN : " + businessRegistrationNumber + " Exception : " + ex.ToString());     
                throw;  
            }

            return accessTokenCache;
        }

        public async Task<bool> ValidateExistingBRNDBRecord(string businessRegistrationNumber){
            var result = false;

            try{

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(businessRegistrationNumber != null)
                {
                    exp = (x => x.BusinessRegistrationNumber == businessRegistrationNumber && x.Deleted != true);
                }
                var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(rTMCLoginDetail != null)
                {
                    result = true;
                }

            }catch(Exception ex)
            {
                _logger.LogError($"RTMCSettingsService : Exception in ValidateExistingBRNDBRecord : Unable to validate if the BRN Record exist in the DB for {businessRegistrationNumber} : Exception : " + ex.ToString());     
                throw;  
            }

            return result;
        }

        public async Task<bool> ClearCache()
        {
            var result = false;

            try
            {

                var cacheEntriesCollectionDefinition = typeof(MemoryCache).GetProperty("EntriesCollection", BindingFlags.NonPublic | BindingFlags.Instance);
                var cacheEntriesCollection = cacheEntriesCollectionDefinition.GetValue(_cache) as dynamic;

                List<object> cacheKeys = new List<object>();

                if (cacheEntriesCollection != null)
                {
                    foreach (var cacheItem in cacheEntriesCollection)
                    {
                        object key = cacheItem.GetType().GetProperty("Key").GetValue(cacheItem);
                        cacheKeys.Add(key);
                    }
                }

                foreach (var entry in cacheKeys)
                {
                    _cache.Remove(entry);
                }
                result = true;
            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Error in ClearCache : Cannot Clear Cache : Exception : " + ex.ToString());                
            }
            return result;
        }

        #endregion

        #region private methods

        private async Task<bool> ValidateCachedRecordExist(string businessRegistrationNumber){
            
            var result = false;
            try
            {
                var cacheKey = businessRegistrationNumber;

                if(_cache.TryGetValue(cacheKey, out AccessTokenCache entry)){
                    result = true;
                }

            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Error in ValidateCachedRecordExist : Cannot Validate Cached Record : Exception : " + ex.ToString());
            }

            return result;
        }

        private async Task<bool> ValidateCacheActive(string businessRegistrationNumber){

            var result = false;
            var validatedCached = false;
            var cacheKey = businessRegistrationNumber;

            try{

                if(_cache.TryGetValue(cacheKey, out AccessTokenCache entry))
                {

                    //Decrypt the Access Token from the cached key and see if the Token is still Valid
                    var handler = new JwtSecurityTokenHandler();

                    //Double check if the Access Token have a value
                    if(entry.AccessToken?.access_token != null)
                    {
                        var jsonToken = handler.ReadJwtToken(entry.AccessToken.access_token);

                        var validTo = jsonToken.ValidTo;
                        var validToUTC = DateTime.SpecifyKind(validTo, DateTimeKind.Local);

                        //Current DateTime 
                        var currentDateTime = DateTime.SpecifyKind(DateTime.UtcNow, DateTimeKind.Local);
                        var currentDateTimeBuffer = currentDateTime.AddSeconds(30);                    

                        if(currentDateTimeBuffer < validToUTC)
                        {
                            result = true;
                        }
                    }else
                    {
                        result = false;
                    }

                }else
                {
                    _logger.LogWarning("RTMCSettingsService : Problem in ValidateCacheActive : Cannot find a non-expired Cached Record for " + businessRegistrationNumber);     
                    result = false;
                }
            }catch(Exception ex)
            {
                _logger.LogError("RTMCSettingsService : Exception in ValidateCacheActive : Cannot Validate if Cached Record is still Active :  Exception : " + ex.ToString());     
                result = false; 
            }

            return result;
        }

        private async Task<AccessTokenCache> GenerateAccessTokenCache(string businessRegistrationNumber)
        {
            AccessTokenCache accessTokenCache = new AccessTokenCache();

            try
            {

                var restTokenURL = _endPointOptions.RESTToken;
                var cacheKey = businessRegistrationNumber;
                //Get Cached Record
                AccessTokenCache entry = null;

                if(_cache.TryGetValue(cacheKey, out entry)){

                    if(entry != null)
                    {
                        AccessToken accessToken = new AccessToken();
                        accessToken = entry.AccessToken;

                        var username = entry.Username;
                        var password = entry.Password;
                        //var password = _decryptionService.Decrypt(entry.Password);

                        HttpClient httpClient = new HttpClient();

                        httpClient.DefaultRequestHeaders.Add($"Authorization", $"Basic {_sharedServices.Base64Encode($"{username}:{password}")}");
                        httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));
                        httpClient.Timeout = TimeSpan.FromSeconds(10);

                        HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(restTokenURL,null).ConfigureAwait(false);

                        var responseMessage = httpResponseMessage.Content.ReadAsStringAsync().Result;

                        accessToken = JsonConvert.DeserializeObject<AccessToken>(responseMessage);

                        accessTokenCache.AccessToken = accessToken;
                        accessTokenCache.Username = username;
                        accessTokenCache.Password = password;
                        accessTokenCache.BusinessRegistrationNumber = businessRegistrationNumber;
                    }else
                    {
                        _logger.LogWarning($"RTMCSettingsService : Log Entry Content was NULL");  
                    }
                }else
                {
                    _logger.LogError($"RTMCSettingsService : Unable to generate new CacheToken in GenerateAccessTokenCache for {businessRegistrationNumber}");     
                }

            }catch(Exception ex)
            {
                _logger.LogError($"RTMCSettingsService : Exception in GenerateAccessTokenCache for {businessRegistrationNumber} : Exception :" + ex.ToString());     
                throw;  
            }

            return accessTokenCache;
        }

        private async Task<AccessTokenCache> UpdateCacheToken(string businessRegistrationNumber, AccessTokenCache accessToken){

            try
            {
                
                //Check if Cached Record exist and Update it
                var cacheKey = businessRegistrationNumber;

                _cache.Set(cacheKey, accessToken);

            }catch(Exception ex)
            {
                _logger.LogError($"RTMCSettingsService : Exception in UpdateCacheToken : Cannot Create/Update Cached Token for {businessRegistrationNumber} :  Exception : " + ex.ToString());     
                throw;
            }
            
            return accessToken;
        }

        private async Task<AccessToken> RetrieveToken (string businessRegistrationNumber)
        {
            AccessToken accessToken = new AccessToken();
            var cacheKey = businessRegistrationNumber;

            try{

                if(_cache.TryGetValue(cacheKey, out AccessTokenCache entry)){

                    accessToken = entry.AccessToken;
                    _logger.LogWarning($"RTMCSettingsService : RetrieveToken : Access Token for BRN : {businessRegistrationNumber} | AccessToken : + " + accessToken.ToString());  
                }

            }catch(Exception ex){
                _logger.LogError($"RTMCSettingsService : RetrieveToken : Failed to retrieve Access Token for BRN : {businessRegistrationNumber} | Exception : + " + ex.ToString());  
            }
            return accessToken;
        }

        private async Task<AccessTokenCache> RetrieveAccessTokenCache (string businessRegistrationNumber)
        {
            AccessTokenCache accessTokenCache = new AccessTokenCache();
            var cacheKey = businessRegistrationNumber;

            if(_cache.TryGetValue(cacheKey, out AccessTokenCache entry)){

                accessTokenCache = entry;
            }else
            {
                _logger.LogError($"RTMCSettingsService : Empty Access Token Cache Record in RetrieveAccessTokenCache for {businessRegistrationNumber}");    
            }

            return accessTokenCache;
        }

        private async Task<AccessTokenCache> GenerateAccessTokenDB(string businessRegistrationNumber)
        {
            AccessTokenCache accessTokenCache = new AccessTokenCache();
            AccessToken accessToken = new AccessToken();

            try
            {

                var restTokenURL = _endPointOptions.RESTToken;
                var cacheKey = businessRegistrationNumber;
                //Get Cached Record

                Expression<Func<RTMCSettings, bool>> exp = null;

                if(businessRegistrationNumber != null)
                {
                    exp = (x => x.BusinessRegistrationNumber == businessRegistrationNumber && x.Deleted != true);
                }
                var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

                if(rTMCLoginDetail == null)
                {
                    _logger.LogError($"RTMCSettingsService : GenerateAccessTokenDB : No Records found for BRN : {businessRegistrationNumber}");  
                    throw new Exception();
                }

                //Decrypt Password

                var username = rTMCLoginDetail.Username;
                var password = "";
                try
                {
                    // password = _decryptionService.Decrypt(rTMCLoginDetail.EncryptedPassword.ToString());
                    // password = _decryptionService.DecryptFromByte(rTMCLoginDetail.EncryptedPassword);

                    // password = _sharedServices.ConvertByteArrayToString(_rsaEncryptDecryptService.Decrypt(rTMCLoginDetail.EncryptedPassword));
                    password = _sharedServices.ConvertByteArrayToString(_aesEncryptionDecryptionService.Decrypt(rTMCLoginDetail.EncryptedPassword));                    

                }catch(Exception ex)
                {
                    //_logger.LogError($"RTMCSettingsService : GenerateAccessTokenDB : Failed to Decrypt Password BRN : {businessRegistrationNumber} | Password : {rTMCLoginDetail.EncryptedPassword} | Exception : {ex.ToString()}"); 
                    throw; 
                }

                HttpClient httpClient = new HttpClient();

                httpClient.DefaultRequestHeaders.Add($"Authorization", $"Basic {_sharedServices.Base64Encode($"{username}:{password}")}");
                httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));
                httpClient.Timeout = TimeSpan.FromSeconds(10); 

                HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(restTokenURL,null).ConfigureAwait(false);

                var responseMessage = httpResponseMessage.Content.ReadAsStringAsync().Result;

                accessToken = JsonConvert.DeserializeObject<AccessToken>(responseMessage);

                accessTokenCache.AccessToken = accessToken;
                accessTokenCache.Username = username;
                accessTokenCache.Password = password;
                accessTokenCache.BusinessRegistrationNumber = businessRegistrationNumber;

            }catch(Exception ex)
            {
                _logger.LogError($"RTMCSettingsService : Exception in GenerateAccessTokenCache for {businessRegistrationNumber} : Exception :" + ex.ToString());     
                throw;  
            }

            return accessTokenCache;
        }

        //Backup Methods, Should be removed after testing
        // private async Task<AccessToken> GenerateAccessTokenbck(string businessRegistrationNumber)
        // {
        //     AccessToken accessToken = new AccessToken();

        //     try
        //     {

        //         var restTokenURL = _endPointOptions.RESTToken;

        //         Expression<Func<RTMCSettings, bool>> exp = null;

        //         if(businessRegistrationNumber != null)
        //         {
        //             exp = (x => x.BusinessRegistrationNumber == businessRegistrationNumber && x.Deleted != true);
        //         }
        //         var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

        //         if(rTMCLoginDetail == null)
        //         {
        //             _logger.LogError($"RTMCSettingsService : GetAccessTokenByBRNAsync : No Records found for BRN : {businessRegistrationNumber}");  
        //             throw new Exception();
        //         }

        //         //Decrypt Password

        //         var username = rTMCLoginDetail.Username;
        //         var password = _decryptionService.Decrypt(rTMCLoginDetail.EncryptedPassword.ToString());

        //         HttpClient httpClient = new HttpClient();

        //         httpClient.DefaultRequestHeaders.Add($"Authorization", $"Basic {_sharedServices.Base64Encode($"{username}:{password}")}");
        //         httpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/x-www-form-urlencoded"));

        //         HttpResponseMessage httpResponseMessage = await httpClient.PostAsync(restTokenURL,null).ConfigureAwait(false);

        //         var responseMessage = httpResponseMessage.Content.ReadAsStringAsync().Result;

        //         accessToken = JsonConvert.DeserializeObject<AccessToken>(responseMessage);

        //     }catch(Exception ex)
        //     {
        //         _logger.LogError($"RTMCSettingsService : Exception in GetVehicleHistoricInformationAsync for {businessRegistrationNumber} : Exception :" + ex.ToString());     
        //         throw;  
        //     }

        //     return accessToken;
        // }

        //Check for duplicates
        private async Task<bool> DuplicateCheck(RTMCLoginDetails request)
        {
            bool result = true;

            Expression<Func<RTMCSettings, bool>> exp = null;

            if(request.BusinessRegistrationNumber != null)
            {
                exp = (x => x.BusinessRegistrationNumber == request.BusinessRegistrationNumber && x.Deleted == false);
            }

            //Retrieve archived Record
            var rTMCLoginDetail  = await _rTMCSettingsRepository.LastOrDefaultAsync(exp);

            if(rTMCLoginDetail != null)
            {
                if(rTMCLoginDetail.Id != request.Id)
                {
                    result = true;
                }else
                {
                    result = false;
                }
                
            }else
            {
                result = false;
            }

            return result;
        }

        #endregion

    }

}