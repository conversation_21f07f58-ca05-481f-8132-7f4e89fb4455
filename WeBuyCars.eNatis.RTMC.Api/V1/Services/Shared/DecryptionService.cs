using System;
using Microsoft.Extensions.Configuration;
using WeBuyCars.eNatis.RTMC.Core.Constants;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    public class DecryptionService : IDecryptionService
    {
        private readonly string _pubKey;

        public DecryptionService(IConfiguration configuration)
        {
            _pubKey = configuration.GetSection(Constants.Encryption.PublicKey).Value;
        }

        public string Decrypt(string value) => EncryptDecrypt.DecryptStringWithPublicKey(Convert.FromBase64String(value), _pubKey);
        public string DecryptFromByte(byte[] value) => EncryptDecrypt.DecryptStringWithPublicKey(value, _pubKey);
    }
}
