using System.Security.Cryptography;
using WeBuyCars.eNatis.RTMC.Core.Constants;
using Microsoft.Extensions.Configuration;
using System.Text;
using System;
using System.IO;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    public class AesEncryptionDecryptionService : IAesEncryptionDecryptionService
    {
        private readonly string _pvtKey;

        private byte[] key;
        private byte[] iv;

        public AesEncryptionDecryptionService(IConfiguration configuration)
        {
            // rsaProvider = new RSACryptoServiceProvider();
            _pvtKey = configuration.GetSection(Constants.Encryption.PrivateKey).Value;
            SetPrivateKey(_pvtKey);
            //iv = GenerateRandomIV();
            iv = Convert.FromBase64String("ydyHe679EmD/G1mfyyLNzQ==");

        }

        public byte[] Encrypt(byte[] input)
        {
            if (key == null)
            {
                throw new InvalidOperationException("Private key has not been set. Call SetPrivateKey method first.");
            }

            using (Aes aes = Aes.Create())
            {
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7;

                using (MemoryStream memoryStream = new MemoryStream())
                {
                    using (CryptoStream cryptoStream = new CryptoStream(memoryStream, aes.CreateEncryptor(), CryptoStreamMode.Write))
                    {
                        cryptoStream.Write(input, 0, input.Length);
                        cryptoStream.FlushFinalBlock();
                    }

                    return memoryStream.ToArray();
                }
            }
        }

        public byte[] Decrypt(byte[] input)
        {
            using (Aes aes = Aes.Create())
            {
                aes.Key = key;
                aes.IV = iv;
                aes.Padding = PaddingMode.PKCS7;

                using (var decryptor = aes.CreateDecryptor(aes.Key, aes.IV))
                {
                    using (var msDecrypt = new MemoryStream(input))
                    {
                        using (var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read))
                        {
                            using (var msPlain = new MemoryStream())
                            {
                                csDecrypt.CopyTo(msPlain);
                                return msPlain.ToArray();
                            }
                        }
                    }
                }
            }
        }
        public string DecryptToString(byte[] input)
        {
            byte[] decryptedBytes = Decrypt(input);
            return Encoding.UTF8.GetString(decryptedBytes);
        }

        //Private Methods
        private byte[] GenerateRandomIV()
        {
            using (Aes aes = Aes.Create())
            {
                aes.GenerateIV();
                return aes.IV;
            }
            
        }

        private void SetPrivateKey(string privateKey)
        {
            using (SHA256 sha256 = SHA256.Create())
            {
                key = sha256.ComputeHash(Encoding.UTF8.GetBytes(privateKey));
            }
        }


    }

}
