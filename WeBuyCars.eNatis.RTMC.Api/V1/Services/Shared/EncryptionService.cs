using System;
using Microsoft.Extensions.Configuration;
using WeBuyCars.eNatis.RTMC.Core.Constants;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    public class EncryptionService : IEncryptionService
    {
        private readonly string _pvtKey;

        public EncryptionService(IConfiguration configuration)
        {
            _pvtKey = configuration.GetSection(Constants.Encryption.PrivateKey).Value;
        }

        public string Encrypt(string value) => Convert.ToBase64String(EncryptDecrypt.EncryptStringWithPrivateKey(value, _pvtKey));
        public byte[] EncryptToByteArray(string value) => (EncryptDecrypt.EncryptStringWithPrivateKey(value, _pvtKey));
  
    }
}
