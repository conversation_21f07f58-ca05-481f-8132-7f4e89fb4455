using System;
using System.Linq;
using System.Numerics;
using System.Security.Cryptography;
using System.Text;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    internal static class EncryptDecrypt
    {
        private const int _blockSize = 110;

        public static byte[] EncryptStringWithPrivateKey(string sample, string keyXml)
        {
            using (var RSA = new RSACryptoServiceProvider())
            {
                RSA.FromXmlString(keyXml);
                var rtn = RSA.PrivateEncryption(sample);
                return rtn;
            }
        }

        private static byte[] AddPadding(byte[] data)
        {
            var rnd = new Random();
            var paddings = new byte[4];
            rnd.NextBytes(paddings);
            paddings[0] = (byte)(paddings[0] | 128);

            var results = new byte[data.Length + 4];

            Array.Copy(paddings, results, 4);
            Array.Copy(data, 0, results, 4, data.Length);
            return results;
        }
        private static byte[] PrivateEncryption(this RSACryptoServiceProvider rsa, string data)
        {
            var ByteConverter = new UnicodeEncoding();
            return PrivateEncryption(rsa, ByteConverter.GetBytes(data));
        }

        private static BigInteger GetBig(byte[] data)
        {
            var inArr = (byte[])data.Clone();
            Array.Reverse(inArr);  // Reverse the byte order
            var final = new byte[inArr.Length + 1];  // Add an empty byte at the end, to simulate unsigned BigInteger (no negatives!)
            Array.Copy(inArr, final, inArr.Length);

            return new BigInteger(final);
        }

        private static byte[] PrivateEncryption(this RSACryptoServiceProvider rsa, byte[] data)
        {
            if (data == null)
                throw new ArgumentNullException("data");
            if (rsa.PublicOnly)
                throw new InvalidOperationException("Private key is not loaded");

            var rtnData = new byte[0];
            while (data != null)
            {
                var block = GetLeftAndCut(ref data, _blockSize);

                // Add 4 byte padding to the data, and convert to BigInteger struct
                var numData = GetBig(AddPadding(block));
                var rsaParams = rsa.ExportParameters(true);
                var D = GetBig(rsaParams.D);
                var Modulus = GetBig(rsaParams.Modulus);
                var encData = BigInteger.ModPow(numData, D, Modulus);
                var enc = encData.ToByteArray();
                Array.Resize(ref rtnData, rtnData.Length + 1);  // Create space for the length byte
                rtnData[rtnData.Length - 1] = (byte)enc.Length; // Record length of block
                Array.Resize(ref rtnData, rtnData.Length + enc.Length);
                Array.Copy(enc, 0, rtnData, rtnData.Length - enc.Length, enc.Length);
            }

            return rtnData;
        }

        private static byte[] GetLeftAndCut(ref byte[] data, int count)
        {
            var block = data.Take(count).ToArray();
            data = data.Length > count ? data.Skip(count).ToArray() : null;
            return block;
        }

        public static string DecryptStringWithPublicKey(byte[] sample, string keyXml)
        {
            try
            {
                using (var RSA = new RSACryptoServiceProvider())
                {
                    RSA.FromXmlString(keyXml);
                    var ByteConverter = new UnicodeEncoding();
                    var rtn = ByteConverter.GetString(RSA.PublicDecryption(sample));
                    //var rtn = Convert.ToBase64String(RSA.PublicDecryption(sample));
                    return rtn;
                }
            }
            catch { }
            return null;
        }

        public static string GetNewKeys()
        {
            return new RSACryptoServiceProvider().ToXmlString(true);
        }

        public static string GetPublicKey(string privateKey)
        {
            using (RSACryptoServiceProvider RSA = new RSACryptoServiceProvider())
            {
                var ByteConverter = new UnicodeEncoding();
                RSA.FromXmlString(privateKey);
                return RSA.ToXmlString(false);
            }
        }

        private static byte[] PublicDecryption(this RSACryptoServiceProvider rsa, byte[] cipherData)
        {
            if (cipherData == null)
                throw new ArgumentNullException("cipherData");

            var rtnData = new byte[0];
            while (cipherData != null)
            {
                var blockLen = GetLeftAndCut(ref cipherData, 1)[0]; // Get the block size
                var block = GetLeftAndCut(ref cipherData, blockLen);

                var numEncData = new BigInteger(block);
                var rsaParams = rsa.ExportParameters(false);
                var Exponent = GetBig(rsaParams.Exponent);
                var Modulus = GetBig(rsaParams.Modulus);
                var decData = BigInteger.ModPow(numEncData, Exponent, Modulus);
                var data = decData.ToByteArray();
                var result = new byte[data.Length - 1];
                Array.Copy(data, result, result.Length);
                result = RemovePadding(result);
                Array.Reverse(result);
                Array.Resize(ref rtnData, rtnData.Length + result.Length);
                Array.Copy(result, 0, rtnData, rtnData.Length - result.Length, result.Length);
            }
            return rtnData;
        }
        private static byte[] RemovePadding(byte[] data)
        {
            var results = new byte[data.Length - 4];
            Array.Copy(data, results, results.Length);
            return results;
        }
        
    }
}
