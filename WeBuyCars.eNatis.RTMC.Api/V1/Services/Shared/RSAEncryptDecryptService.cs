using System.Security.Cryptography;
using WeBuyCars.eNatis.RTMC.Core.Constants;
using Microsoft.Extensions.Configuration;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    public class RSAEncryptDecryptService : IRSACryptoService
    {
        private RSACryptoServiceProvider rsaProvider;
        private readonly string _pvtKey;

        public RSAEncryptDecryptService(IConfiguration configuration)
        {
            // rsaProvider = new RSACryptoServiceProvider();
            rsaProvider = new RSACryptoServiceProvider();
            _pvtKey = configuration.GetSection(Constants.Encryption.PrivateKey).Value;
            SetPrivateKey(_pvtKey);
        }

        public void SetPrivateKey(string privateKeyXml)
        {
            rsaProvider.FromXmlString(privateKeyXml);
        }

        public byte[] Encrypt(byte[] data)
        {
            byte[] encryptedData = rsaProvider.Encrypt(data, true);
            return encryptedData;
        }

        public byte[] Decrypt(byte[] encryptedData)
        {
            byte[] decryptedData = rsaProvider.Decrypt(encryptedData, true);
            return decryptedData;
        }
        public string DecryptToString(byte[] encryptedData)
        {
            var result = rsaProvider.Decrypt(encryptedData, true).ToString();
            return result;
        }

    }

}
