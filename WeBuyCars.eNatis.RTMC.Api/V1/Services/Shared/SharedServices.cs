
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Extensions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared
{
    public class SharedServices
    {

        readonly ILogger<RTMCVehicleInformationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly IRTMCVehicleDetailRepository _rTMCVehicleDetailRepository;
        readonly IRTMCDriverInformationDetailRepository _rTMCDriverInformationRepository;   
        readonly IRTMCVehicleOwnerVerificationDetailRepository _rTMCVehicleOwnerVerificationDetailRepository;
        readonly IArchiveRequestRepository _archiveRequestRepository;
        readonly IArchiveResponseRepository _archiveResponseRepository;
        readonly IRTMCVehicleOwnerRegistrationDetailRepository _rTMCVehicleOwnerRegistrationDetailRepository;
        readonly IRTMCOwnershipHistoryDetailRepository _rTMCOwnershipHistoryDetailRepository;
        readonly IRTMCVehicleOwnerOnlineNCORepository _rTMCVehicleOwnerOnlineNCORepository;

        #region ctor

        public SharedServices(
            ILogger<RTMCVehicleInformationService> logger,
            IMapper mapper,
            IArchiveRequestRepository archiveRequestRepository,
            IArchiveResponseRepository archiveResponseRepository,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            IRTMCVehicleDetailRepository rTMCVehicleDetailRepository,
            IRTMCDriverInformationDetailRepository rTMCDriverInformationRepository,
            IRTMCVehicleOwnerVerificationDetailRepository rTMCVehicleOwnerVerificationDetailRepository,
            IRTMCOwnershipHistoryDetailRepository rTMCOwnershipHistoryDetailRepository,
            IRTMCVehicleOwnerRegistrationDetailRepository rTMCVehicleOwnerRegistrationDetailRepository,
            IRTMCVehicleOwnerOnlineNCORepository rTMCVehicleOwnerOnlineNCORepository
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _rTMCVehicleDetailRepository = rTMCVehicleDetailRepository ?? throw new ArgumentNullException(nameof(rTMCVehicleDetailRepository));
            _rTMCDriverInformationRepository = rTMCDriverInformationRepository ?? throw new ArgumentNullException(nameof(rTMCDriverInformationRepository));
            _rTMCVehicleOwnerVerificationDetailRepository = rTMCVehicleOwnerVerificationDetailRepository ?? throw new ArgumentNullException(nameof(rTMCVehicleOwnerVerificationDetailRepository));
            _archiveRequestRepository = archiveRequestRepository ?? throw new System.ArgumentNullException(nameof(archiveRequestRepository));
            _archiveResponseRepository = archiveResponseRepository ?? throw new System.ArgumentNullException(nameof(archiveResponseRepository));
            _rTMCVehicleOwnerOnlineNCORepository = rTMCVehicleOwnerOnlineNCORepository ?? throw new System.ArgumentNullException(nameof(rTMCVehicleOwnerOnlineNCORepository));
            _rTMCOwnershipHistoryDetailRepository = rTMCOwnershipHistoryDetailRepository ?? throw new System.ArgumentNullException(nameof(rTMCOwnershipHistoryDetailRepository));
            _rTMCVehicleOwnerRegistrationDetailRepository = rTMCVehicleOwnerRegistrationDetailRepository ?? throw new System.ArgumentNullException(nameof(rTMCVehicleOwnerRegistrationDetailRepository));
        }

        #endregion

        #region Public Methods

        //Save Driver Information
        public async Task<int> SaveDriverDetail(RTMCDriverInformationDetail driverInformationDetail)
        {
            var result = 0;

            //Check if the last Record in the Table contains the latest information
            var existingRecord = await _rTMCDriverInformationRepository.LastOrDefaultAsync(x => x.IdDocumentNumber == driverInformationDetail.IdDocumentNumber && x.DrivingLicenceNumber == driverInformationDetail.DrivingLicenceNumber && x.Deleted != true);

            if(existingRecord == null){
                _logger.LogTrace("SharedService : RTMC | Method : SaveDriverDetail | Write Request to DB : " + driverInformationDetail.ToString());
                var dbDriverInformationDetail = _rTMCDriverInformationRepository.AddRTMCDriverInformationDetail(driverInformationDetail);
                result = _rTMCDriverInformationRepository.UnitOfWork.SaveChanges();
            }else
            {
                if(existingRecord.IdDocumentNumber != driverInformationDetail.IdDocumentNumber)
                {
                    _logger.LogTrace("SharedService : RTMC | Method : SaveDriverDetail | Write Request to DB : " + driverInformationDetail.ToString());
                    var dbDriverInformationDetail = _rTMCDriverInformationRepository.AddRTMCDriverInformationDetail(driverInformationDetail);
                    result = _rTMCDriverInformationRepository.UnitOfWork.SaveChanges();
                }
                result = 1;
            }

            return result;
        }

        //Save Vehicle Information
        public async Task<int> SaveVehicleDetail(RTMCVehicleDetail vehicleDetail)
        {
            var result = 0;

            //Check if the last Record in the Table contains the latest information
            var existingRecord = await _rTMCVehicleDetailRepository.LastOrDefaultAsync(x => x.RegisterNumber == vehicleDetail.RegisterNumber && x.Deleted != true);

            if(existingRecord == null)
            {
                _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleDetail | Write Request to DB : " + vehicleDetail.ToString());
                var dbVehicleDetail = _rTMCVehicleDetailRepository.AddRTMCVehicleDetail(vehicleDetail);
                result = _rTMCVehicleDetailRepository.UnitOfWork.SaveChanges();

            }else
            {
                var vehicleDetailDifferenceCheck = await VehicleDetailDifferenceCheck(vehicleDetail, existingRecord);

                if(!vehicleDetailDifferenceCheck)
                {
                    _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleDetail | Write Request to DB : " + vehicleDetail.ToString());
                    var dbVehicleDetail = _rTMCVehicleDetailRepository.AddRTMCVehicleDetail(vehicleDetail);
                    result = _rTMCVehicleDetailRepository.UnitOfWork.SaveChanges();
                }
                result = 1;
            }

            return result;
        }

        // VehicleOwner Information
        public async Task<int> SaveVehicleOwnerVerificationDetail(RTMCVehicleOwnerVerificationDetail vehicleOwnerVerificationDetail)
        {
            var result = 0;

            //Check if the last Record in the Table contains the latest information
            var existingRecord = await _rTMCVehicleOwnerVerificationDetailRepository.LastOrDefaultAsync(x => x.PersonDocumentNumber == vehicleOwnerVerificationDetail.PersonDocumentNumber && x.Deleted != true);

            if(existingRecord == null)
            {
                _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleOwnerDetail | Write Request to DB : " + vehicleOwnerVerificationDetail.ToString());
                var dbVehicleOwnerVerificationDetail = _rTMCVehicleOwnerVerificationDetailRepository.AddRTMCVehicleOwnerVerificationDetail(vehicleOwnerVerificationDetail);
                result = _rTMCVehicleOwnerVerificationDetailRepository.UnitOfWork.SaveChanges();
            }else
            {
                if(existingRecord != vehicleOwnerVerificationDetail)
                {
                _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleOwnerDetail | Write Request to DB : " + vehicleOwnerVerificationDetail.ToString());
                var dbVehicleOwnerVerificationDetail = _rTMCVehicleOwnerVerificationDetailRepository.AddRTMCVehicleOwnerVerificationDetail(vehicleOwnerVerificationDetail);
                result = _rTMCVehicleOwnerVerificationDetailRepository.UnitOfWork.SaveChanges();
                }
            }

            return result;
        }

        //Save Vehicle Owner Registration Information
        public async Task<int> SaveVehicleOwnerRegistrationDetail(RTMCVehicleOwnerRegistrationDetail vehicleOwnerRegistrationDetail)
        {
            var result = 0;

            _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleOwnerRegistrationDetail | Write Request to DB : " + vehicleOwnerRegistrationDetail.ToString());
            var dbVehicleOwnerRegistrationDetail = _rTMCVehicleOwnerRegistrationDetailRepository.AddRTMCVehicleOwnerRegistrationDetail(vehicleOwnerRegistrationDetail);
            result = _rTMCVehicleOwnerRegistrationDetailRepository.UnitOfWork.SaveChanges();

            return result;
        }

        //Save Ownership History Information
        public async Task<int> SaveOwnershipHistoryDetail(RTMCOwnershipHistoryDetail ownershipDetail)
        {
            var result = 0;

            try
            {
                //Check if a Duplicate Record does not exist for the Record that wants to be inserted
                var duplicate = await CheckDuplicateVehicleHistoryCheck(ownershipDetail);

                if(!duplicate)
                {
                    //Insert new Record in to Ownership Table
                    _logger.LogTrace("SharedService : RTMC | Method : SaveOwnershipHistoryDetail | Write Request to DB : " + ownershipDetail.ToString());
                    var dbownershipDetail = _rTMCOwnershipHistoryDetailRepository.AddRTMCOwnershipHistoryDetail(ownershipDetail);
                    
                }
                else
                {
                    //Flag previous Ownership History Record as Previous
                    await UpdateOwnershipStatus(ownershipDetail);
                }
                result = _rTMCOwnershipHistoryDetailRepository.UnitOfWork.SaveChanges();

            }catch(Exception ex)
            {
                _logger.LogTrace("SharedService : RTMC | Method : SaveOwnershipHistoryDetail | Failed to Write Request to DB for Vehicle :" + ownershipDetail.VinOrChassis +  " : Exception" + ex.ToString());
            }
            
            return result;
        }

        //Save Vehicle Owner Online NCO Information
        public async Task<int> SaveVehicleOwnerOnlineNCODetail(RTMCVehicleOwnerOnlineNCODetail vehicleOwnerOnlineNCODetail)
        {
            var result = 0;

            _logger.LogTrace("SharedService : RTMC | Method : SaveVehicleOwnerOnlineNCODetail | Write Request to DB : " + vehicleOwnerOnlineNCODetail.ToString());
            _rTMCVehicleOwnerOnlineNCORepository.AddRTMCVehicleOwnerOnlineNCODetail(vehicleOwnerOnlineNCODetail);
            result = _rTMCVehicleOwnerOnlineNCORepository.UnitOfWork.SaveChanges();

            return result;
        }

        //Get DocumentType Enum
        public async Task<IEnumerable<EnumDTO>> GetEnumList(string enumType)
        {

            List<EnumDTO> enumList = new List<EnumDTO>();

            switch(enumType)
            {
                case "DocumentType" :
                    enumList = Enum<DocumentTypeEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList();
                break;
                case "MotorVehicleState" :
                    enumList = Enum<MotorVehicleStateEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList();
                break;
                case "NatureOfOwnership":
                    enumList = Enum<NatureOfOwnershipEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList();
                break;
                case "RegistrationReason":
                    enumList = Enum<RegistrationReasonEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList();
                break;
                case "VehicleUsage":
                    enumList = Enum<VehicleUsageEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList();
                break;
                default :
                    enumList.AddRange(Enum<DocumentTypeEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList());
                    enumList.AddRange(Enum<MotorVehicleStateEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList());
                    enumList.AddRange(Enum<NatureOfOwnershipEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList());
                    enumList.AddRange(Enum<RegistrationReasonEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList());
                    enumList.AddRange(Enum<VehicleUsageEnum>.GetAllValuesAsIEnumerable().Select(d => new EnumDTO(d)).ToList());

                    break;

            }

            return enumList;

        }

        public int SaveRTMCRequest(RTMCRequest request){
            
            int result = 0;
            //Write Request to DB
            _logger.LogTrace("SharedService : RTMC | Method : SaveRTMCRequest | Write Request to DB : " + request.ToString());
            request = _rTMCRequestRepository.AddRTMCRequest(request);
            result = _rTMCRequestRepository.UnitOfWork.SaveChanges();
            return result;
        }

        public int SaveRTMCResponse(long reqId, string user, string natisResponseObject, string responseObject, string status, Guid auditLogId){

            //Initiate Log and Response Object
            int result = 0;
            RTMCResponse response = new RTMCResponse();

            try
            {

                response.CreatedOn = DateTime.Now;
                response.CreatedBy = user;
                response.ReqId = reqId;
                response.NatisResponseObject = natisResponseObject;
                response.ResponseObject = responseObject;
                response.ResponseStatus = status;
                response.Reference = auditLogId;

                //Write Response to DB
                _logger.LogTrace("SharedService : RTMC | Method : SaveRTMCResponse | Write Response to DB : " + response.ToString());

                response = _rTMCResponseRepository.AddRTMCResponse(response);
                result = _rTMCResponseRepository.UnitOfWork.SaveChanges();
            }catch(Exception ex)
            {
                _logger.LogWarning("SharedService : RTMC | Method : SaveRTMCResponse | Write Response to DB : " + response.ToString() + " | Exception : " + ex.ToString());
                _logger.LogWarning("SharedService : RTMC | Method : SaveRTMCResponse | Write Response to DB : " + response.ToString() + " | InnerException : " + ex.InnerException.ToString());
            }

            return result;
        }

        public async Task<ArchivedRequest> SaveRequest(string integrationType,Object loginDetailRequest)
        {        
            try
            {
                ArchivedRequest dbArchivedRequest = new ArchivedRequest();
                dbArchivedRequest.RequestObject = loginDetailRequest.ToString();
                dbArchivedRequest.IntegrationType = integrationType;
                dbArchivedRequest.CreatedOn = DateTime.Now;

                //Write Request to DB
                dbArchivedRequest = _archiveRequestRepository.AddArchiveRequest(dbArchivedRequest);
                var requestResult = _archiveRequestRepository.UnitOfWork.SaveChanges();

                return dbArchivedRequest;

            }catch(Exception ex)
            {
                _logger.LogError("SharedServices | SaveRequest | Failed to Save Request object to DB : Exception = " + ex);
                throw new DomainException("Failed to Save Request Object to DB : Exception = " + ex);
            }
        }

        public async Task<bool> SaveResponse(ArchivedRequest loginDetailRequest, Object archivedResponse)
        {
            
            bool result = false;

            try
            {

                var vehicleDetailResponseListString = JsonConvert.SerializeObject(archivedResponse);
                ArchivedResponse dbArchivedResponse = new ArchivedResponse();
                dbArchivedResponse.ReqId = loginDetailRequest.Id;
                dbArchivedResponse.ResponseObject = vehicleDetailResponseListString;
                dbArchivedResponse.CreatedOn = DateTime.Now;

                //Write Response to DB
                dbArchivedResponse = _archiveResponseRepository.AddArchiveRequest(dbArchivedResponse);
                var requestResult = _archiveResponseRepository.UnitOfWork.SaveChanges();

                if(requestResult > 0)
                {
                    result = true;
                }
            
            }catch(Exception ex)
            {
                _logger.LogError("SharedServices | SaveResponse | Failed to Save Repsonse object to DB : Exception = " + ex);
                throw new DomainException("Failed to Save Response Object to DB : Exception = " + ex);
            }

            return result;
        }

        public string ConvertToYYYYMMDD(string inputDate)
        {
            DateTime parsedDate;
            if (DateTime.TryParse(inputDate, out parsedDate))
            {
                return parsedDate.ToString("yyyy-MM-dd");
            }
            else
            {
                return inputDate;
            }
        }

        public string ConvertToYYYYMMDDHHMMSS(string inputDateTime)
        {
            DateTime parsedDateTime;
            if (DateTime.TryParse(inputDateTime, out parsedDateTime))
            {
                return parsedDateTime.ToString("yyyy-MM-dd HH:mm:ss");
            }
            else
            {
                throw new ArgumentException("Invalid date and time format.");
            }
        }
        //Encode Text
        public string Base64Encode(string textToEncode)
        {
            byte[] textAsBytes = Encoding.UTF8.GetBytes(textToEncode);
            return Convert.ToBase64String(textAsBytes);
        }

        //Decode Text
        public string Base64Decode(string textToDecode)
        {
            byte[] textAsBytes = Convert.FromBase64String(textToDecode);
            return Encoding.UTF8.GetString(textAsBytes);
        }

        public class DateFormatConverter : IsoDateTimeConverter
        {
            public DateFormatConverter(string format)
            {
                DateTimeFormat = format;
            }
        }

        public byte[] ConvertStringToByteArray(string inputString)
        {
            Encoding encoding = Encoding.UTF8;
            return encoding.GetBytes(inputString);
        }

        public string ConvertByteArrayToString(byte[] inputByteArray)
        {
            return Encoding.UTF8.GetString(inputByteArray);
        }

        #endregion

        #region Private Methods

        //Compare Vehicle Detailed Fields
        private async Task<bool> VehicleDetailDifferenceCheck(RTMCVehicleDetail vehicleDetail, RTMCVehicleDetail existingVehicleDetail){

            var result = false;

            if((existingVehicleDetail.EngineNumber == vehicleDetail.EngineNumber) 
            && (existingVehicleDetail.LicenceNumber == vehicleDetail.LicenceNumber) 
            && (existingVehicleDetail.RegisterNumber == vehicleDetail.RegisterNumber) 
            && (existingVehicleDetail.VinOrChassis == vehicleDetail.VinOrChassis)
            && (existingVehicleDetail.DescriptionCode == vehicleDetail.DescriptionCode)
            && (existingVehicleDetail.DrivenCode == vehicleDetail.DrivenCode)
            && (existingVehicleDetail.LicenceChangeDate == vehicleDetail.LicenceChangeDate)
            && (existingVehicleDetail.LicenceExpiryDate == vehicleDetail.LicenceExpiryDate)
            && (existingVehicleDetail.LicenceNumber == vehicleDetail.LicenceNumber)
            && (existingVehicleDetail.LifeStatusCode == vehicleDetail.LifeStatusCode)
            && (existingVehicleDetail.MainColourCode == vehicleDetail.MainColourCode)
            && (existingVehicleDetail.MakeCode == vehicleDetail.MakeCode)
            && (existingVehicleDetail.ModelNameCode == vehicleDetail.ModelNameCode)
            && (existingVehicleDetail.PrePreviousLicenceNumber == vehicleDetail.PrePreviousLicenceNumber)
            && (existingVehicleDetail.PreviousLicenceNumber == vehicleDetail.PreviousLicenceNumber)
            && (existingVehicleDetail.RegistrationTypeCode == vehicleDetail.RegistrationTypeCode)
            && (existingVehicleDetail.VehicleCertificateNumber == vehicleDetail.VehicleCertificateNumber)
            && (existingVehicleDetail.RoadworthyStatusCode == vehicleDetail.RoadworthyStatusCode)
            && (existingVehicleDetail.RoadworthyStatusDate == vehicleDetail.RoadworthyStatusDate)
            && (existingVehicleDetail.RoadworthyTestDate == vehicleDetail.RoadworthyTestDate)
            && (existingVehicleDetail.SapClearanceDate == vehicleDetail.SapClearanceDate)
            && (existingVehicleDetail.SapClearanceStatusCode == vehicleDetail.SapClearanceStatusCode)
            && (existingVehicleDetail.SapMarkCode == vehicleDetail.SapMarkCode)
            && (existingVehicleDetail.SapMarkDate == vehicleDetail.SapMarkDate)
            && (existingVehicleDetail.VehicleStateCode == vehicleDetail.VehicleStateCode)
            && (existingVehicleDetail.VehicleStateDate == vehicleDetail.VehicleStateDate)
            && (existingVehicleDetail.LicenceLiabilityDate == vehicleDetail.LicenceLiabilityDate)
            && (existingVehicleDetail.RegAuthorityOfLicensingCode == vehicleDetail.RegAuthorityOfLicensingCode)
            && (existingVehicleDetail.RegAuthorityOfLicenceNumberCode == vehicleDetail.RegAuthorityOfLicenceNumberCode)
            && (existingVehicleDetail.RegistrationDate == vehicleDetail.RegistrationDate)
            && (existingVehicleDetail.DataOwnerCode == vehicleDetail.DataOwnerCode)
            && (existingVehicleDetail.TransmissionCode == vehicleDetail.TransmissionCode)
            && (existingVehicleDetail.GearboxNumber == vehicleDetail.GearboxNumber)
            && (existingVehicleDetail.DifferentialNumber == vehicleDetail.DifferentialNumber)
            && (existingVehicleDetail.FirstLicensingDate == vehicleDetail.FirstLicensingDate)
            && (existingVehicleDetail.CountryOfExportCode == vehicleDetail.CountryOfExportCode)
            && (existingVehicleDetail.CountryOfImportCode == vehicleDetail.CountryOfImportCode)
            && (existingVehicleDetail.ModelNumber == vehicleDetail.ModelNumber)
            && (existingVehicleDetail.SapClearanceReasonCode == vehicleDetail.SapClearanceReasonCode)
            && (existingVehicleDetail.VehicleUsageCode == vehicleDetail.VehicleUsageCode)
            && (existingVehicleDetail.EconomicSectorCode == vehicleDetail.EconomicSectorCode)
            && (existingVehicleDetail.PreviousVehicleCertificateNumber == vehicleDetail.PreviousVehicleCertificateNumber)
            && (existingVehicleDetail.LicenceFee == vehicleDetail.LicenceFee)
            && (existingVehicleDetail.ExaminerNumber == vehicleDetail.ExaminerNumber)
            && (existingVehicleDetail.ExemptionCode == vehicleDetail.ExemptionCode)
            && (existingVehicleDetail.RoadUseIndicator == vehicleDetail.RoadUseIndicator)
            && (existingVehicleDetail.PrePrePreviousLicenceNumber == vehicleDetail.PrePrePreviousLicenceNumber)
            && (existingVehicleDetail.AdministrationMarkIndicator == vehicleDetail.AdministrationMarkIndicator)
            && (existingVehicleDetail.FirstRegistrationDate == vehicleDetail.FirstRegistrationDate)
            && (existingVehicleDetail.RegistrationAllowed == vehicleDetail.RegistrationAllowed)
            && (existingVehicleDetail.CategoryCode == vehicleDetail.CategoryCode))
            {
                result = true;
            }

            return result;

        }


        //Check for Duplicate Vehicle Owner History Records
        private async Task<bool> CheckDuplicateVehicleHistoryCheck(RTMCOwnershipHistoryDetail ownershipDetail){
            var result = false;

            var existingRecord = await _rTMCOwnershipHistoryDetailRepository.LastOrDefaultAsync(x => 
            x.RegisterNumber == ownershipDetail.RegisterNumber && 
            x.IdentificationNumber == ownershipDetail.IdentificationNumber && 
            x.OwnershipType == ownershipDetail.OwnershipType && 
            x.OwnershipDate == ownershipDetail.OwnershipDate && 
            !x.Deleted);

            if(existingRecord != null)
            {
                result = true;
            }

            return result;
        }

        //Flag Current Ownership History Record as Previous based on Input information
        private async Task<int> UpdateOwnershipStatus(RTMCOwnershipHistoryDetail ownershipDetail)
        {
            var result = 0;
            var existingRecord = await _rTMCOwnershipHistoryDetailRepository.LastOrDefaultAsync(x => 
            x.RegisterNumber == ownershipDetail.RegisterNumber 
            && x.IdentificationNumber == ownershipDetail.IdentificationNumber 
            && x.OwnershipType == ownershipDetail.OwnershipType
            && x.OwnershipDate == ownershipDetail.OwnershipDate
            );

            if(existingRecord != null)
            {
                existingRecord.OwnershipStatus = ownershipDetail.OwnershipStatus;
                await _rTMCOwnershipHistoryDetailRepository.UpdateRTMCOwnershipHistoryDetail(existingRecord);
                result = 1;
            }

            return result;
        }



        //Get All Enum 
        public class Enum<T> where T : Enum
        {
            public static IEnumerable<T> GetAllValuesAsIEnumerable()
            {
                return Enum.GetValues(typeof(T)).Cast<T>();
            }
        }

        //Mapping DTO Objects for Enums
        public class EnumDTO
        {
            //public string Key { get { return Convert.ToInt32(_enum).ToString(); } }
            public string Key { get { return _enum.ToString(); } }
            public string Name { get { return _enum.ToDescription(); } }
            private Enum _enum;
            public EnumDTO(Enum inputEnum)
            {
                _enum = inputEnum;
            }
        }

        #endregion

    }

}