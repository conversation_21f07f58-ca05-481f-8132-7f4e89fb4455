using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleDetailed;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCVehicleDetailedInformationService
    {

        readonly ILogger<RTMCVehicleDetailedInformationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly SharedServices _sharedServices;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        readonly IVehicleDetailIntegrationService _vehicleDetailIntegrationService;

        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly IRTMCVehicleDetailRepository _rTMCVehicleDetailRepository;


        #region ctor
            
        public RTMCVehicleDetailedInformationService(
            ILogger<RTMCVehicleDetailedInformationService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            IRTMCVehicleDetailRepository rTMCVehicleDetailRepository,
            SharedServices sharedServices,
            IVehicleDetailIntegrationService vehicleDetailIntegrationService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _vehicleDetailIntegrationService = vehicleDetailIntegrationService ?? throw new ArgumentNullException(nameof(vehicleDetailIntegrationService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _rTMCVehicleDetailRepository = rTMCVehicleDetailRepository ?? throw new ArgumentNullException(nameof(rTMCVehicleDetailRepository));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve Vehicle Detailed Information from Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<VehicleDetailResponse> GetVehicleDetailInformationAsync(VehicleDetailRequest request, string environmentName){

            VehicleDetailResponse result = new VehicleDetailResponse();

            Guid auditLogId = request.MessageId.Value;

            result.BaseResponse = new BaseResponse()
            {
                Successful = false,
                Message = "Failed to retrieve Vehicle Detailed Information",
                Reference = request.MessageId.Value
            };

            try{


                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | Mapping Request Object");
                var dbVehicleDetailRequest = MapRequestObjectToDB(request);

                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | If Check for dbVehicleDetailRequest : " + dbVehicleDetailRequest.ToString());
                if(dbVehicleDetailRequest != null)
                {

                    //Write Request to DB
                    dbVehicleDetailRequest.Reference = request.MessageId.Value;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbVehicleDetailRequest);

                    _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | Map Request Parameter to Natis Vehicle Request");
                        var natisVehicleDetailedRequest = _mapper.Map<NatisGetVehicleDetailedRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            _logger.LogTrace("Service : RTMC | Method : GetVehicleDetailInformationAsync | Send Request Object to Natis");
                            //var natisVehicleDetail = await _natisIntegrationService.GetVehicleDetailedQuery(natisVehicleDetailedRequest);
                            var natisVehicleDetail = await _vehicleDetailIntegrationService.GetVehicleDetailedQuery(auditLogId, natisVehicleDetailedRequest, environmentName);

                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | If check for Natis Response : " + natisVehicleDetail.ToString());
                            if(natisVehicleDetail != null)
                            {
                                //Map Convert 
                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | Convert Natis Response to vehicle Detail Response");
                                var vehicleDetailResponse = _mapper.Map<VehicleDetailResponse>(natisVehicleDetail);

                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | If Check for Mapping vehicleDetailResponse");
                                if(vehicleDetailResponse != null)
                                {

                                    //Serialize Vehicle Detail Response to Json String for DB.
                                    var vehicleDetailResponseString = JsonConvert.SerializeObject(vehicleDetailResponse);
                                    var vehicleDetailString = JsonConvert.SerializeObject(natisVehicleDetail);

                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbVehicleDetailRequest.Id, request.User,vehicleDetailString, vehicleDetailResponseString, "Success", auditLogId);

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {
                                        
                                        //Return Vehicle Detail Response object
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | Return Success Result : " + responseResult.ToString());

                                        //Write Vehicle Information to Log Table
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleDetailInformationAsync | Map Natis Vehicle Information to Log Table");
                                        var vehicleDetail = MapResponseObjectToVehicleDetailedDB(vehicleDetailResponse);

                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Method : GetVehicleAsync | Save Information to Log Table");
                                        var vehicleDetailResult = await _sharedServices.SaveVehicleDetail(vehicleDetail);

                                        if(vehicleDetailResult > 0)
                                        {
                                            result = vehicleDetailResponse;

                                            if(vehicleDetailResponse.BaseResponse.Successful != true)
                                            {
                                                result.BaseResponse.Successful = false;
                                                result.BaseResponse.Reference = auditLogId;
                                            }else
                                            {
                                                result.BaseResponse.Successful = true;
                                                result.BaseResponse.Reference = auditLogId;
                                            }

                                            return result;
                                        }else{
                                            //Error to indicate that the Response Object was not able to be written to the DB
                                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | Unable to save Vehicle Detail to DB : " + request.ToString());
                                            throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to save Vehicle Detail to DB : " + request.ToString());                                           
                                        }
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information

                            _sharedServices.SaveRTMCResponse(dbVehicleDetailRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Error | Method : GetVehicleDetailInformationAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : GetVehicleDetailInformationAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private VehicleDetailRequest FormatInputRequest(VehicleDetailRequest request){

            if(!String.IsNullOrEmpty(request.EngineNumber)){

                //Remove Special Characters
                request.EngineNumber = HelperExtensions.RemoveSpecialCharacters(request.EngineNumber);
                //UpperCase
                request.EngineNumber = request.EngineNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.LicenceNumber)){

                //Remove Special Characters
                request.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenceNumber);
                //UpperCase
                request.LicenceNumber = request.LicenceNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.RegistrationNumber)){

                //Remove Special Characters
                request.RegistrationNumber = HelperExtensions.RemoveSpecialCharacters(request.RegistrationNumber);
                //UpperCase
                request.RegistrationNumber = request.RegistrationNumber.ToUpper();
            }


            if(!String.IsNullOrEmpty(request.RegisterNumber)){

                //Remove Special Characters
                request.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.RegisterNumber);
                //UpperCase
                request.RegisterNumber = request.RegisterNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vin)){

                //Remove Special Characters
                request.Vin = HelperExtensions.RemoveSpecialCharacters(request.Vin);
                //UpperCase
                request.Vin = request.Vin.ToUpper();
            }
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(VehicleDetailRequest request){

            RTMCRequest dbVehicleRequest = new RTMCRequest();
            try
            {
                dbVehicleRequest = _mapper.Map<RTMCRequest>(request);
                dbVehicleRequest.RequestObject = request.ToString();
                dbVehicleRequest.IntegrationType = "GetVehicleDetailed";
                dbVehicleRequest.Date = DateTime.Now;
                dbVehicleRequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | An Exception occurred when mapping MapXMLtoVehicleDetailedResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleDetailedResponse XML Response string to Class : Exception = " + ex);
            }

            return dbVehicleRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCVehicleDetail
        /// </summary>
        /// <param name="vehicleDetailResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCVehicleDetail MapResponseObjectToVehicleDetailedDB(VehicleDetailResponse vehicleDetailResponse){

            RTMCVehicleDetail dbVehicleDetail = new RTMCVehicleDetail();
            try
            {
                dbVehicleDetail = _mapper.Map<RTMCVehicleDetail>(vehicleDetailResponse);
                dbVehicleDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleDetailedInformationService | An Exception occurred when mapping MapResponseObjectToVehicleDetailedDB vehicleDetailResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapResponseObjectToVehicleDetailedDB vehicleDetailResponse to Class : Exception = " + ex);
            }

            return dbVehicleDetail;
        }

        #endregion





    }

}