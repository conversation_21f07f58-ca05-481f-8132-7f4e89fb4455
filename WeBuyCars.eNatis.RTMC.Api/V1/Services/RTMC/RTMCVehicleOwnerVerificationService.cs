using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCVehicleOwnerVerificationService
    {

        readonly ILogger<RTMCVehicleOwnerVerificationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly IVehicleOwnerVerificationIntegrationService _vehicleOwnerVerificationIntegrationService;
        readonly IRTMCVehicleOwnerVerificationDetailRepository _rTMCVehicleOwnerVerificationDetailRepository;
        readonly SharedServices _sharedServices;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        
        #region ctor
            
        public RTMCVehicleOwnerVerificationService(
            ILogger<RTMCVehicleOwnerVerificationService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            IRTMCVehicleDetailRepository rTMCVehicleDetailRepository,
            IRTMCVehicleOwnerVerificationDetailRepository rTMCVehicleOwnerVerificationDetailRepository,
            IVehicleOwnerVerificationIntegrationService vehicleOwnerVerificationIntegrationService,
            SharedServices sharedServices,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _rTMCVehicleOwnerVerificationDetailRepository = rTMCVehicleOwnerVerificationDetailRepository ?? throw new ArgumentNullException(nameof(rTMCVehicleOwnerVerificationDetailRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            _vehicleOwnerVerificationIntegrationService = vehicleOwnerVerificationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerVerificationIntegrationService));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve Title Holder Information from Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<VehicleOwnerVerificationResponse> GetOwnerTitleHolderConfirmationAsync(VehicleOwnerVerificationRequest request, string environmentName){

            VehicleOwnerVerificationResponse result = new VehicleOwnerVerificationResponse();

            Guid auditLogId = request.MessageId.Value;

            result.BaseResponse = new BaseResponse()
            {
                Successful = false,
                Message = "Failed to retrieve Vehicle Owner Verification Information",
                Reference = request.MessageId.Value
            };

            try{

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Initiate Log and Response Object
                VehicleOwnerVerificationResponse vehicleOwnerVerificationResponse = new VehicleOwnerVerificationResponse();

                //Map Request to db object
                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Mapping Request Object");
                var dbVehicleOwnerVerificationRequest = MapRequestObjectToDB(request);

                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |  RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | If Check for dbVehicleRequest : " + dbVehicleOwnerVerificationRequest.ToString());
                if(dbVehicleOwnerVerificationRequest != null)
                {

                    //Write Request to DB
                    dbVehicleOwnerVerificationRequest.Reference = auditLogId;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbVehicleOwnerVerificationRequest);

                    _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |  RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Map Request Parameter to Natis Vehicle Request");
                        var natisVehicleOwnerVerificationRequest = _mapper.Map<NatisGetVehicleOwnerVerificationRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |  RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Send Request Object to Natis");
                            //var natisVehicleOwnerVerification = await _natisIntegrationService.GetOwnerTitleHolderConfirmationQuery(natisVehicleOwnerVerificationRequest);
                            var natisVehicleOwnerVerification = await _vehicleOwnerVerificationIntegrationService.GetVehicleOwnerVerificationQuery(auditLogId, natisVehicleOwnerVerificationRequest, environmentName);

                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |  RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | If check for Natis Response : " + natisVehicleOwnerVerification.ToString());
                            if(natisVehicleOwnerVerification != null)
                            {
                                //Map Convert 
                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Convert Natis Response to vehicle Detail Response");
                                vehicleOwnerVerificationResponse = _mapper.Map<VehicleOwnerVerificationResponse>(natisVehicleOwnerVerification);

                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | If Check for Mapping vehicleOwnerVerificationResponse");
                                if(vehicleOwnerVerificationResponse != null)
                                {

                                    //Serialize Vehicle Detail Response to Json String for DB.
                                    var vehicleDetailResponseString = JsonConvert.SerializeObject(vehicleOwnerVerificationResponse);
                                    var natisVehicleOwnerVerificationString = JsonConvert.SerializeObject(natisVehicleOwnerVerification);

                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbVehicleOwnerVerificationRequest.Id, request.User,natisVehicleOwnerVerificationString, vehicleDetailResponseString, "Success", auditLogId);

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {
                                        
                                        //Return VehicleOwnerVerification Response object
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Return Success Result : " + responseResult.ToString());

                                        var vehicleOwnerVerificationDetail = MapResponseObjectToVehicleOwnerVerificationDetailDB(vehicleOwnerVerificationResponse);

                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |  RTMCVehicleOwnerVerifcationService : RTMC | Method : GetVehicleAsync | Save Information to Log Table");
                                        var vehicleOwnerVerificationDetailResult = await _sharedServices.SaveVehicleOwnerVerificationDetail(vehicleOwnerVerificationDetail);

                                        if(vehicleOwnerVerificationDetailResult > 0)
                                        {
                                            result = vehicleOwnerVerificationResponse;

                                            if(vehicleOwnerVerificationResponse.BaseResponse.Successful != true)
                                            {
                                                result.BaseResponse.Successful = false;
                                                result.BaseResponse.Reference = auditLogId;
                                            }else
                                            {
                                                result.BaseResponse.Successful = true;
                                                result.BaseResponse.Reference = auditLogId;
                                            }

                                            return result;
                                        }else{
                                            //Error to indicate that the Response Object was not able to be written to the DB
                                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to save Vehicle Detail to DB : " + request.ToString());
                                            throw new DomainException("Unable to save Vehicle Detail to DB : " + request.ToString());                                           
                                        }
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information

                            _sharedServices.SaveRTMCResponse(dbVehicleOwnerVerificationRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);


                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException(" Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " |RTMCVehicleOwnerVerifcationService : GetOwnerTitleHolderConfirmationAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods


        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private VehicleOwnerVerificationRequest FormatInputRequest(VehicleOwnerVerificationRequest request){

            if(!String.IsNullOrEmpty(request.DocumentNumber)){

                //Remove Special Characters
                request.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.DocumentNumber);
                //UpperCase
                request.DocumentNumber = request.DocumentNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vin)){

                //Remove Special Characters
                request.Vin = HelperExtensions.RemoveSpecialCharacters(request.Vin);
                //UpperCase
                request.Vin = request.Vin.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.RegisterNumber)){

                //Remove Special Characters
                request.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.RegisterNumber);
                //UpperCase
                request.RegisterNumber = request.RegisterNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.LicenceNumber)){

                //Remove Special Characters
                request.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenceNumber);
                //UpperCase
                request.LicenceNumber = request.LicenceNumber.ToUpper();
            }
            
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(VehicleOwnerVerificationRequest request){

            RTMCRequest dbVehicleVerificationRequest = new RTMCRequest();
            try
            {
                dbVehicleVerificationRequest = _mapper.Map<RTMCRequest>(request);
                dbVehicleVerificationRequest.RequestObject = request.ToString();
                dbVehicleVerificationRequest.IntegrationType = "GetOwnerTitleHolderConfirmation";
                dbVehicleVerificationRequest.Date = DateTime.Now;
                dbVehicleVerificationRequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                 _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : MapRequestObjectToDB | An Exception occurred when mapping VehicleOwnerVerificationRequest to DB Object : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping VehicleOwnerVerificationRequest XML Response string to Class : Exception = " + ex);
            }

            return dbVehicleVerificationRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCVehicleOwnerVerificationDetail
        /// </summary>
        /// <param name="vehicleOwnerVerificationResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCVehicleOwnerVerificationDetail MapResponseObjectToVehicleOwnerVerificationDetailDB(VehicleOwnerVerificationResponse vehicleOwnerVerificationResponse){

            RTMCVehicleOwnerVerificationDetail dbVehicleOwnerVerificationDetail = new RTMCVehicleOwnerVerificationDetail();
            try
            {
                dbVehicleOwnerVerificationDetail = _mapper.Map<RTMCVehicleOwnerVerificationDetail>(vehicleOwnerVerificationResponse);
                dbVehicleOwnerVerificationDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                 _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleOwnerVerifcationService : MapXMLtoVehicleResponse | An Exception occurred when mapping MapResponseObjectToVehicleOwnerVerificationDetailDB vehicleDetailResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping vehicleOwnershipVerificationDetailResponse to Class : Exception = " + ex);
            }

            return dbVehicleOwnerVerificationDetail;
        }

        #endregion





    }

}