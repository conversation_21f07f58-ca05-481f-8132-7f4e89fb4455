using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;
using RTMC;
using MassTransit;
using MassTransit.Transports;
using RTMC.Contracts;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCVehicleOwnerRegistrationService
    {

        readonly ILogger<RTMCVehicleOwnerRegistrationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;

        readonly RTMCSettingsService _rTMCSettingService;
        readonly SharedServices _sharedServices;        
        //private readonly IEncryptionService _encryptionService;
        // private readonly IRSACryptoService _rsaEncryptDecryptService;
        private readonly IAesEncryptionDecryptionService _aesEncryptionDecryptionService;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        readonly IPublishEndpoint _publishEndpoint;

        readonly IVehicleOwnerRegistrationIntegrationService _vehicleOwnerRegistrationIntegrationService;


        #region ctor
            
        public RTMCVehicleOwnerRegistrationService(
            ILogger<RTMCVehicleOwnerRegistrationService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCSettingsService rTMCSettingService,
            //IEncryptionService encryptionService
            // IRSACryptoService rsaEncryptDecryptService
            IAesEncryptionDecryptionService aesEncryptionDecryptionService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions,
            IVehicleOwnerRegistrationIntegrationService vehicleOwnerRegistrationIntegrationService,
            IPublishEndpoint publishEndpoint
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
            //_encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            _aesEncryptionDecryptionService = aesEncryptionDecryptionService ?? throw new ArgumentNullException(nameof(aesEncryptionDecryptionService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            _publishEndpoint = publishEndpoint ?? throw new ArgumentNullException(nameof(publishEndpoint));
            _vehicleOwnerRegistrationIntegrationService = vehicleOwnerRegistrationIntegrationService ?? throw new ArgumentNullException(nameof(vehicleOwnerRegistrationIntegrationService));      
        }

        #endregion

        #region public methods

        /// <summary>
        /// Register Vehicle Owner on Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<VehicleOwnerRegistrationResponse> VehicleOwnerRegistrationAsync(VehicleOwnerRegistrationRequest request, string environmentName){

            Guid auditLogId = request.MessageId.Value;

            VehicleOwnerRegistrationResponse result = new VehicleOwnerRegistrationResponse();
            BaseResponse baseResponse = new BaseResponse();

            var businessRegistrationObject = await _rTMCSettingService.GetRTMCLoginSettingsByBRNAsync(request.BusinessRegistrationNumber);

            if(businessRegistrationObject == null)
            {
                result.BaseResponse.Successful = false;
                result.BaseResponse.Status = "Failed";
                result.BaseResponse.Message = "Unable to retrieve Business Registration Information from Database";
                result.BaseResponse.Reference = auditLogId;
                return result;
            }

            try{

                //Feed BRN Object Information to Request
                request = MapBusinessRegObjectToRequest(request,businessRegistrationObject);

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Initiate Log and Response Object
                VehicleOwnerRegistrationResponse vehicleOwnerRegistrationResponse = new VehicleOwnerRegistrationResponse();

                //Map Request to db object
                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Mapping Request Object");
                var dbVehicleOwnerRegistrationRequest = MapRequestObjectToDB(request);

                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | If Check for dbVehicleOwnerRegistrationRequest : " + dbVehicleOwnerRegistrationRequest.ToString());
                if(dbVehicleOwnerRegistrationRequest != null)
                {

                    //Write Request to DB
                    dbVehicleOwnerRegistrationRequest.Reference = auditLogId;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbVehicleOwnerRegistrationRequest);
                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Save Request Record : " + requestResult.ToString());

                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Map Request Parameter to Natis Registration Request");
                        var natisVehicleOwnerRegistrationRequest = _mapper.Map<NatisVehicleOwnerRegistrationRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Send Request Object to Natis");

                            //Get Credentials for Business Registration Number
                            //var accessTokenCache = await _rTMCSettingService.GetUnencryptedCredentialsByBRNAsync(request.BusinessRegistrationNumber);

                            var soapCredentials = await _rTMCSettingService.GetSOAPCredentialsByBRN(request.BusinessRegistrationNumber);

                            //Override Credentials for testing purposes this needs to be removed for Production
                            // soapCredentials.Username = "4988A001";
                            // soapCredentials.DecryptedPassword = "TESTER01";
                            // natisVehicleOwnerRegistrationRequest.RepresentativeDocumentNumber = "7112015629187";

                            //var natisVehicleOwnerRegistrationInformation = await _natisIntegrationService.RegisterVehicleOwnerQuery(natisVehicleOwnerRegistrationRequest, soapCredentials.Username, soapCredentials.DecryptedPassword, environmentName, auditLogId);
                            var natisVehicleOwnerRegistrationInformation = await _vehicleOwnerRegistrationIntegrationService.RegisterVehicleOwnerQuery(auditLogId,natisVehicleOwnerRegistrationRequest, soapCredentials.Username, soapCredentials.DecryptedPassword, environmentName);

                            if(natisVehicleOwnerRegistrationInformation != null)
                            {

                                //Check if Base Response have failed
                                if(natisVehicleOwnerRegistrationInformation.BaseResponse.Successful == false)
                                {
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Natis Response Failed : " + natisVehicleOwnerRegistrationInformation.BaseResponse.Message);

                                    try{
                                        vehicleOwnerRegistrationResponse = _mapper.Map<VehicleOwnerRegistrationResponse>(natisVehicleOwnerRegistrationInformation);
                                        _sharedServices.SaveRTMCResponse(dbVehicleOwnerRegistrationRequest.Id, request.User,vehicleOwnerRegistrationResponse.ToString(), natisVehicleOwnerRegistrationInformation.BaseResponse.Message, "Failed", auditLogId);
                                    }catch(Exception ex)
                                    {
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Failed to Map Natis Results to Response Object : " + vehicleOwnerRegistrationResponse + " | Exception : " + ex);
                                    }
                                    return vehicleOwnerRegistrationResponse;
                                }
                      
                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Natis Response : " + natisVehicleOwnerRegistrationInformation.ToString());

                                byte[] encryptedVehicleCertificateNumber = null;

                                //Encrypt the Control Number before it gets used.
                                if(natisVehicleOwnerRegistrationInformation.VehicleCertificateNumber != null)
                                {
                                                                
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : Encrypting Vehicle Control Number : " + natisVehicleOwnerRegistrationInformation.VehicleCertificateNumber);
                                    //encryptedVehicleCertificateNumber = _encryptionService.EncryptToByteArray(natisVehicleOwnerRegistrationInformation.VehicleCertificateNumber);
                                    // encryptedVehicleCertificateNumber = _rsaEncryptDecryptService.Encrypt(_sharedServices.ConvertStringToByteArray(natisVehicleOwnerRegistrationInformation.VehicleCertificateNumber));
                                    encryptedVehicleCertificateNumber = _aesEncryptionDecryptionService.Encrypt(_sharedServices.ConvertStringToByteArray(natisVehicleOwnerRegistrationInformation.VehicleCertificateNumber));

                                }

                                //Save Information to Elastic In order to recover if something Broke with writing it to the DB
                                var vehicleOwnerRegistrationResponseString = JsonConvert.SerializeObject(natisVehicleOwnerRegistrationInformation);
                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | RTMC Response Information : " + vehicleOwnerRegistrationResponseString);

                                //Map Convert 
                                vehicleOwnerRegistrationResponse = _mapper.Map<VehicleOwnerRegistrationResponse>(natisVehicleOwnerRegistrationInformation);

                                //Serialize Registration Detail Response to Json String for DB.
                                vehicleOwnerRegistrationResponse.BaseResponse.Reference = auditLogId;

                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | If Check for Mapping dbVehicleOwnerRegistrationResponse");
                                if(vehicleOwnerRegistrationResponse != null)
                                {

                                    var natisVehicleOwnerRegistrationString =JsonConvert.SerializeObject(natisVehicleOwnerRegistrationInformation);

                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbVehicleOwnerRegistrationRequest.Id, request.User,natisVehicleOwnerRegistrationString, vehicleOwnerRegistrationResponseString, "Success", auditLogId);

                                    //Return VehicleOwnerRegistration Response object
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Save Response Record : " + responseResult.ToString());

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {                                        

                                        //Write Vehicle Information to Log Table
                                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Map Natis Vehicle Information to Log Table");
                                        var vehicleOwnerRegistrationDetail = MapResponseObjectToRTMCVehicleOwnerRegistrationDB(vehicleOwnerRegistrationResponse, encryptedVehicleCertificateNumber);

                                        if(!String.IsNullOrWhiteSpace(vehicleOwnerRegistrationDetail.RegisterNumber))
                                        {

                                            //Add Transaction Log Value to Reference Field 
                                            vehicleOwnerRegistrationDetail.Reference = auditLogId;
                                            vehicleOwnerRegistrationDetail.ClientMessageId = request.MessageId;

                                            //Save Transaction Log Record
                                            _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Save Registration Log Record");
                                            var vehicleOwnerRegistrationDetailResult = await _sharedServices.SaveVehicleOwnerRegistrationDetail(vehicleOwnerRegistrationDetail);

                                            if(vehicleOwnerRegistrationDetailResult > 0)
                                            {
                                                result = vehicleOwnerRegistrationResponse;

                                                if(vehicleOwnerRegistrationResponse.BaseResponse.Successful != true)
                                                {
                                                    baseResponse.Successful = false;
                                                    baseResponse.Message = natisVehicleOwnerRegistrationInformation.BaseResponse.Message;
                                                    baseResponse.StatusCode = natisVehicleOwnerRegistrationInformation.BaseResponse.StatusCode;
                                                    baseResponse.Reference = auditLogId;
                                                    result.BaseResponse = baseResponse;

                                                    return result;
                                                }else
                                                {
                                                    result.BaseResponse.Successful = true;
                                                    result.BaseResponse.Reference = auditLogId;

                                                    try
                                                    {
                                                        //Publish Event
                                                        await PublishVehicleOwnerRegistrationEvent(result);
                                                    }catch(Exception ex)
                                                    {
                                                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : Publish MassTransit  | Unable to Publish Mass Transit Event"+ result.ToString());
                                                    }

                                                    return result;
                                                }

                                                
                                            }else{
                                                //Error to indicate that the Response Object was not able to be written to the DB
                                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to save Vehicle Registration Information Detail to DB : " + request.ToString());
                                                baseResponse.Successful = false;
                                                baseResponse.Message = natisVehicleOwnerRegistrationInformation.BaseResponse.Message;
                                                baseResponse.StatusCode = natisVehicleOwnerRegistrationInformation.BaseResponse.StatusCode;
                                                baseResponse.Reference = auditLogId;
                                                result.BaseResponse = baseResponse;     
                                                return result;                                 
                                            }
                                        }else
                                        {
                                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to save Vehicle Registration Information Detail to DB : " + request.ToString());
                                            baseResponse.Successful = false;
                                            baseResponse.Message = natisVehicleOwnerRegistrationInformation.BaseResponse.Message;
                                            baseResponse.StatusCode = natisVehicleOwnerRegistrationInformation.BaseResponse.StatusCode;
                                            baseResponse.Reference = auditLogId;
                                            result.BaseResponse = baseResponse;   
                                            return result; 
                                        }

                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information

                            _sharedServices.SaveRTMCResponse(dbVehicleOwnerRegistrationRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + "| RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException(" Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : VehicleOwnerRegistrationAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private VehicleOwnerRegistrationRequest FormatInputRequest(VehicleOwnerRegistrationRequest request){

            //Owner Information
            if(request.GetOwner() != null){
                //Remove Special Characters
                EntityIdentification owner = request.GetOwner();
                owner.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.GetOwner().DocumentNumber).ToUpper();
                request.SetOwner(owner);
            }

            if(request.GetOwnerProxy() != null){
                //Remove Special Characters
                EntityIdentification ownerProxy = request.GetOwnerProxy();
                ownerProxy.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.GetOwnerProxy().DocumentNumber).ToUpper();
                request.SetOwnerProxy(ownerProxy);
            }

            if(request.GetOwnerRepresentative() != null){
                //Remove Special Characters
                EntityIdentification ownerRepresemtative = request.GetOwnerRepresentative();
                ownerRepresemtative.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.GetOwnerRepresentative().DocumentNumber).ToUpper();
                request.SetOwnerRepresentative(ownerRepresemtative);
            }

            // if(request.OwnerRepresentative != null){
            //     //Remove Special Characters
            //     request.OwnerRepresentative.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.OwnerRepresentative.DocumentNumber);
            //     //UpperCase
            //     request.OwnerRepresentative.DocumentNumber = request.OwnerRepresentative.DocumentNumber.ToUpper();
            // }

            //Vehicle information
            if(request.RegisterNumber != null){
                //Remove Special Characters
                request.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.RegisterNumber);
                //UpperCase
                request.RegisterNumber = request.RegisterNumber.ToUpper();
            }

            if(request.VinOrChassis != null){
                //Remove Special Characters
                request.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(request.VinOrChassis);
                //UpperCase
                request.VinOrChassis = request.VinOrChassis.ToUpper();
            }

            if(request.LicenceNumber != null){
                //Remove Special Characters
                request.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenceNumber);
                //UpperCase
                request.LicenceNumber = request.LicenceNumber.ToUpper();
            }

            
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(VehicleOwnerRegistrationRequest request){

            RTMCRequest dbVehicleOwnerRegistrationRequest = new RTMCRequest();
            try
            {
                dbVehicleOwnerRegistrationRequest = _mapper.Map<RTMCRequest>(request);
                dbVehicleOwnerRegistrationRequest.RequestObject = request.ToString();
                dbVehicleOwnerRegistrationRequest.IntegrationType = "VehicleOwnerRegistration";
                dbVehicleOwnerRegistrationRequest.Date = DateTime.Now;
                dbVehicleOwnerRegistrationRequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : MapRequestObjectToDB | An Exception occurred when mapping VehicleOwnerRegistrationRequest to DB Object : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping VehicleOwnerRegistrationRequest XML Response string to Class : Exception = " + ex);
            }

            return dbVehicleOwnerRegistrationRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCVehicleOwnerRegistration
        /// </summary>
        /// <param name="vehicleOwnerRegistrationResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCVehicleOwnerRegistrationDetail MapResponseObjectToRTMCVehicleOwnerRegistrationDB(VehicleOwnerRegistrationResponse vehicleOwnerRegistrationResponse, byte[] encryptedVehicleCertificateNumber){

            RTMCVehicleOwnerRegistrationDetail dbVehicleOwnerRegistrationDetail = new RTMCVehicleOwnerRegistrationDetail();
            try
            {
                dbVehicleOwnerRegistrationDetail = _mapper.Map<RTMCVehicleOwnerRegistrationDetail>(vehicleOwnerRegistrationResponse);
                dbVehicleOwnerRegistrationDetail.CreatedOn = DateTime.Now;
                dbVehicleOwnerRegistrationDetail.EncryptedVehicleCertificateNumber = encryptedVehicleCertificateNumber;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleOwnerRegistrationService : MapResponseObjectToRTMCVehicleOwnerRegistrationDB | An Exception occurred when mapping MapResponseObjectToRTMCVehicleOwnerRegistrationDB RTMCVehicleOwnerRegistrationDetail to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping vehicleOwnershipVerificationDetailResponse to Class : Exception = " + ex);
            }

            return dbVehicleOwnerRegistrationDetail;
        }

        /// <summary>
        /// Map Business Registration Object to Request
        /// </summary>
        /// <param name="request"></param>
        /// <param name="businessRegistrationObject"></param>
        /// <returns></returns>
        private VehicleOwnerRegistrationRequest MapBusinessRegObjectToRequest (VehicleOwnerRegistrationRequest request, RTMCLoginDetailsResponse businessRegistrationObject)
        {

            request.SetOwner(new EntityIdentification()
            {
                DocumentTypeCode = Core.Enumerations.DocumentTypeEnum.BusinessRegCertificate
                ,
                DocumentNumber = businessRegistrationObject.OwnerDocumentNumber
            });

            request.SetOwnerProxy(new EntityIdentification()
            {
                DocumentTypeCode = Core.Enumerations.DocumentTypeEnum.RSAIDDocument
                , DocumentNumber = businessRegistrationObject.ProxyDocumentNumber
            });

            request.SetOwnerRepresentative(new EntityIdentification()
            {
                DocumentTypeCode = Core.Enumerations.DocumentTypeEnum.RSAIDDocument
                , DocumentNumber = businessRegistrationObject.RepresentativeDocumentNumber
            });

            return request;

        }


        //Send Service Bus Message to Queue
        public async Task PublishVehicleOwnerRegistrationEvent(VehicleOwnerRegistrationResponse vehicleOwnerRegistrationResponse)
        {
            //Publish Event
            await _publishEndpoint.Publish(new OnlineTransactionCompleted() { VehicleOwnerRegistrationResponse = vehicleOwnerRegistrationResponse});
        }


        #endregion

    }

}