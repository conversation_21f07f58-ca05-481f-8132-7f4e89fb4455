using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCDriverService
    {

        readonly ILogger<RTMCDriverService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly IRTMCDriverInformationDetailRepository _rTMCDriverInformationRepository;
        readonly SharedServices _sharedServices;     
        readonly IDriverIntegrationService _driverIntegrationService;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        


        #region ctor
            
        public RTMCDriverService(
            ILogger<RTMCDriverService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            IRTMCDriverInformationDetailRepository rTMCDriverInformationRepository,
            IDriverIntegrationService driverIntegrationService,
            SharedServices sharedServices,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _driverIntegrationService = driverIntegrationService ?? throw new ArgumentNullException(nameof(driverIntegrationService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve Driver Information from Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<DriverInformationResponse> GetDriverInformationAsync(DriverInformationRequest request, string environmentName){

            DriverInformationResponse result = new DriverInformationResponse();

            Guid auditLogId = request.MessageId.Value;

            result.BaseResponse = new BaseResponse()
            {
                Successful = false,
                Message = "Failed to retrieve Driver Information",
                Reference = request.MessageId.Value
            };

            try{

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Initiate Log and Response Object
                DriverInformationResponse driverInformationResponse = new DriverInformationResponse();

                //Map Request to db object
                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Mapping Request Object");
                var dbDriverInformationRequest = MapRequestObjectToDB(request);

                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | If Check for dbVehicleRequest : " + dbDriverInformationRequest.ToString());
                if(dbDriverInformationRequest != null)
                {

                    //Write Request to DB
                    dbDriverInformationRequest.Reference = auditLogId;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbDriverInformationRequest);

                    _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Map Request Parameter to Natis Driver Request");
                        var natisDriverInformationRequest = _mapper.Map<NatisGetDriverInformationRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            // var natisDriverInformation = await _natisIntegrationService.GetDriverQuery(natisDriverInformationRequest);
                            var natisDriverInformation = await _driverIntegrationService.GetDriverQuery(auditLogId, natisDriverInformationRequest, environmentName);
                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | If check for Natis Response : " + natisDriverInformation.ToString());
                            
                            if(natisDriverInformation != null)
                            {
                                //Map Convert 
                                driverInformationResponse = _mapper.Map<DriverInformationResponse>(natisDriverInformation);

                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | If Check for Mapping driverInformationResponse");
                                if(driverInformationResponse != null)
                                {

                                    //Serialize Vehicle Detail Response to Json String for DB.
                                    var driverInformationResponseString = JsonConvert.SerializeObject(driverInformationResponse);
                                    var natisDriverInformationString =JsonConvert.SerializeObject(natisDriverInformation);

                                    //Write Response Object to DB
                                    var responseresult = _sharedServices.SaveRTMCResponse(dbDriverInformationRequest.Id, request.User,natisDriverInformationString, driverInformationResponseString, "Success", auditLogId);

                                    //Check if Response Object Wrote to DB
                                    if(responseresult > 0)
                                    {
                                        
                                        //Write Vehicle Information to Log Table
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Map Natis Vehicle Information to Log Table");
                                        var driverInformationDetail = MapResponseObjectToDriverInformationDetailDB(driverInformationResponse);

                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : RTMC | Method : GetVehicleAsync | Save Information to Log Table");
                                        var vehicleOwnerVerificationDetailResult = await _sharedServices.SaveDriverDetail(driverInformationDetail);

                                        if(vehicleOwnerVerificationDetailResult > 0)
                                        {

                                            result = driverInformationResponse;

                                            if(driverInformationResponse.BaseResponse.Successful != true)
                                            {
                                                result.BaseResponse.Successful = false;
                                                result.BaseResponse.Reference = auditLogId;
                                            }else
                                            {
                                                result.BaseResponse.Successful = true;
                                                result.BaseResponse.Reference = auditLogId;
                                            }                                            
                                            return result;
                                        }else{
                                            //Error to indicate that the Response Object was not able to be written to the DB
                                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to save Driver Information Detail to DB : " + request.ToString());
                                            throw new DomainException("Unable to save Driver Information Detail to DB : " + request.ToString());                                           
                                        }
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information

                            _sharedServices.SaveRTMCResponse(dbDriverInformationRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException(" Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCDriverService : GetDriverInformationAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private DriverInformationRequest FormatInputRequest(DriverInformationRequest request){

            if(!String.IsNullOrEmpty(request.DocumentNumber)){

                //Remove Special Characters
                request.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.DocumentNumber);
                //UpperCase
                request.DocumentNumber = request.DocumentNumber.ToUpper();
            }
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(DriverInformationRequest request){

            RTMCRequest dbDriverInformationRequest = new RTMCRequest();
            try
            {
                dbDriverInformationRequest = _mapper.Map<RTMCRequest>(request);
                dbDriverInformationRequest.RequestObject = request.ToString();
                dbDriverInformationRequest.IntegrationType = "GetDriver";
                dbDriverInformationRequest.Date = DateTime.Now;
                dbDriverInformationRequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCDriverService : MapRequestObjectToDB | An Exception occurred when mapping DriverInformationRequest to DB Object : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping DriverInformationRequest XML Response string to Class : Exception = " + ex);
            }

            return dbDriverInformationRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCDriverInformationDetail
        /// </summary>
        /// <param name="driverInformationResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCDriverInformationDetail MapResponseObjectToDriverInformationDetailDB(DriverInformationResponse driverInformationResponse){

            RTMCDriverInformationDetail dbDriverInformationDetail = new RTMCDriverInformationDetail();
            try
            {
                dbDriverInformationDetail = _mapper.Map<RTMCDriverInformationDetail>(driverInformationResponse);
                dbDriverInformationDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCDriverService : MapResponseObjectToDriverInformationDetailDB | An Exception occurred when mapping MapResponseObjectToDriverInformationDetailDB DriverInformationDetail to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping vehicleOwnershipVerificationDetailResponse to Class : Exception = " + ex);
            }

            return dbDriverInformationDetail;
        }

        #endregion





    }

}