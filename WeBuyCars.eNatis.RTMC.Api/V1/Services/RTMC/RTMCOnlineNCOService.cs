using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;
using MassTransit;
using MassTransit.Transports;
using RTMC.Contracts;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCOnlineNCOService
    {

        private readonly ILogger<RTMCOnlineNCOService> _logger;
        private readonly IMapper _mapper;
        private readonly INatisIntegrationService _natisIntegrationService;
        private readonly IRTMCRequestRepository _rTMCRequestRepository;
        private readonly IRTMCResponseRepository _rTMCResponseRepository;
        private readonly IRTMCDriverInformationDetailRepository _rTMCDriverInformationRepository;
        private readonly RTMCSettingsService _rTMCSettingService;
        private readonly SharedServices _sharedServices;
        //private readonly IEncryptionService _encryptionService;
        //private readonly IRSACryptoService _rsaEncryptDecryptService;
        private readonly IAesEncryptionDecryptionService _aesEncryptionDecryptionService; 
        readonly IPublishEndpoint _publishEndpoint;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        readonly IOnlineNCOIntegrationService _onlineNCOIntegrationService;       

        #region ctor
            
        public RTMCOnlineNCOService(
            ILogger<RTMCOnlineNCOService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            IRTMCDriverInformationDetailRepository rTMCDriverInformationRepository,
            SharedServices sharedServices,
            RTMCSettingsService rTMCSettingService,
            //IEncryptionService encryptionService,
            //IRSACryptoService rsaEncryptDecryptService
            IAesEncryptionDecryptionService aesEncryptionDecryptionService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions,
            IPublishEndpoint publishEndpoint,
            IOnlineNCOIntegrationService onlineNCOIntegrationService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _rTMCDriverInformationRepository = rTMCDriverInformationRepository ?? throw new ArgumentNullException(nameof(rTMCDriverInformationRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
            //_encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
            //_rsaEncryptDecryptService = rsaEncryptDecryptService ?? throw new ArgumentNullException(nameof(rsaEncryptDecryptService));
            _aesEncryptionDecryptionService = aesEncryptionDecryptionService ?? throw new ArgumentNullException(nameof(aesEncryptionDecryptionService));
            _publishEndpoint = publishEndpoint ?? throw new ArgumentNullException(nameof(publishEndpoint));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            _onlineNCOIntegrationService = onlineNCOIntegrationService ?? throw new ArgumentNullException(nameof(onlineNCOIntegrationService));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Register Vehicle Owner on Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<VehicleOwnerOnlineNCOResponse> OnlineNCOAsync(VehicleOwnerOnlineNCORequest request, string environmentName){

            Guid auditLogId = request.MessageId.Value;

            VehicleOwnerOnlineNCOResponse result = new VehicleOwnerOnlineNCOResponse();

            var validBRNRecord = await _rTMCSettingService.ValidateExistingBRNDBRecord(request.BusinessRegistrationNumber);

            if(!validBRNRecord){

                result.BaseResponse.Successful = false;
                result.BaseResponse.Status = "Failed";
                result.BaseResponse.Message = "The Business Registration Number provided does not exist in the Database";
                result.BaseResponse.Reference = auditLogId;
                return result;
            }

            try{

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Initiate Log and Response Object
                VehicleOwnerOnlineNCOResponse vehicleOwnerOnlineNCOResponse = new VehicleOwnerOnlineNCOResponse();

                //Map Request to db object
                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Mapping Request Object");
                var dbVehicleOwnerOnlineNCORequest = MapRequestObjectToDB(request);

                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | If Check for dbVehicleRequest : " + dbVehicleOwnerOnlineNCORequest.ToString());
                if(dbVehicleOwnerOnlineNCORequest != null)
                {

                    //Write Request to DB
                    dbVehicleOwnerOnlineNCORequest.Reference = auditLogId;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbVehicleOwnerOnlineNCORequest);

                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Map Request Parameter to Natis Driver Request");
                        var natisVehicleOwnerOnlineNCORequest = _mapper.Map<NatisOnlineNCORequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Send Request Object to Natis");

                            //Get Credentials for Business Registration Number
                            //var accessTokenCache = await _rTMCSettingService.GetUnencryptedCredentialsByBRNAsync(request.BusinessRegistrationNumber);

                            var soapCredentials = await _rTMCSettingService.GetSOAPCredentialsByBRN(request.BusinessRegistrationNumber);

                            //Override Credentials for testing purposes this needs to be removed for Production
                            // soapCredentials.Username = "4988A001";
                            // soapCredentials.DecryptedPassword = "TESTER01";

                            //var natisOnlineNCOInformation = await _natisIntegrationService.OnlineNCOQuery(natisVehicleOwnerOnlineNCORequest, soapCredentials.Username, soapCredentials.DecryptedPassword, environmentName, auditLogId);
                            var natisOnlineNCOInformation = await _onlineNCOIntegrationService.NominateOwnerChangeQuery(auditLogId, natisVehicleOwnerOnlineNCORequest, soapCredentials.Username, soapCredentials.DecryptedPassword, environmentName);
                            
                            if(natisOnlineNCOInformation != null)
                            {
                                
                                //Check if Base Response have failed
                                if(natisOnlineNCOInformation.BaseResponse.Successful == false)
                                {
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Natis Response Failed : " + natisOnlineNCOInformation.BaseResponse.Message);

                                    try{
                                        vehicleOwnerOnlineNCOResponse = _mapper.Map<VehicleOwnerOnlineNCOResponse>(natisOnlineNCOInformation);
                                        _sharedServices.SaveRTMCResponse(dbVehicleOwnerOnlineNCORequest.Id, request.User,vehicleOwnerOnlineNCOResponse.ToString(), natisOnlineNCOInformation.BaseResponse.Message, "Failed", auditLogId);
                                    }catch(Exception ex)
                                    {
                                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Failed to Map Natis Results to Response Object : " + vehicleOwnerOnlineNCOResponse + " | Exception : " + ex);
                                    }
                                    return vehicleOwnerOnlineNCOResponse;
                                }

                                byte[] encryptedVehicleCertificateNumber = null;
                                byte[] encryptedBarcode = null;

                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | If check for Natis Response : " + natisOnlineNCOInformation.ToString());
                                //Encrypt the Control Number and Barcode before it gets used.
                                if(natisOnlineNCOInformation.VehicleCertificateNumber != null)
                                {                                                             
                                    //encryptedVehicleCertificateNumber = _encryptionService.EncryptToByteArray(natisOnlineNCOInformation.VehicleCertificateNumber);
                                    encryptedVehicleCertificateNumber = _aesEncryptionDecryptionService.Encrypt(_sharedServices.ConvertStringToByteArray(natisOnlineNCOInformation.VehicleCertificateNumber));
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Encrypting Vehicle Control Number : " + natisOnlineNCOInformation.VehicleCertificateNumber);
                                }

                                if(natisOnlineNCOInformation.Barcode != null)
                                {                                                            
                                    //encryptedBarcode = _encryptionService.EncryptToByteArray(natisOnlineNCOInformation.Barcode);
                                    encryptedBarcode = _aesEncryptionDecryptionService.Encrypt(_sharedServices.ConvertStringToByteArray(natisOnlineNCOInformation.Barcode));
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Encrypting Barcode Number : " + natisOnlineNCOInformation.Barcode);
                                }

                                //Save Information to Elastic In order to recover if something Broke with writing it to the DB
                                var natisVehicleOwnerOnlineNCOResponseString =JsonConvert.SerializeObject(natisOnlineNCOInformation);
                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | RTMC Response Information : " + natisVehicleOwnerOnlineNCOResponseString);

                                //Save Response Information to DB
                                //Map Convert 
                                try{
                                    vehicleOwnerOnlineNCOResponse = _mapper.Map<VehicleOwnerOnlineNCOResponse>(natisOnlineNCOInformation);
                                }catch(Exception ex)
                                {
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Failed to Map Natis Results to Response Object : " + vehicleOwnerOnlineNCOResponse + " | Exception : " + ex);
                                }
                                //Assign Reference Number to Response Object
                                vehicleOwnerOnlineNCOResponse.BaseResponse.Reference = auditLogId;

                                //Formulate Information for Returning API
                                _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | If Check for Mapping dbVehicleOwnerOnlineNCOResponse");
                                if(vehicleOwnerOnlineNCOResponse != null)
                                {

                                    //Add Encrypted Control Number to response object
                                    vehicleOwnerOnlineNCOResponse.EncryptedVehicleCertificateNumber  = encryptedVehicleCertificateNumber;
                                    //Serialize NCO Detail Response to Json String for DB.
                                    var vehicleOwnerOnlineNCOResponseString = JsonConvert.SerializeObject(vehicleOwnerOnlineNCOResponse);
    
                                    //Write Vehicle Information to Log Table
                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Map Natis NCO Information to Log Table");
                                    var vehicleOwnerOnlineNCODetail = MapResponseObjectToRTMCVehicleOwnerOnlineNCODB(vehicleOwnerOnlineNCOResponse, encryptedVehicleCertificateNumber, encryptedBarcode);

                                    //Add Transaction Log Value to Reference Field 
                                    vehicleOwnerOnlineNCODetail.Reference = auditLogId;
                                    vehicleOwnerOnlineNCODetail.ClientMessageId = request.MessageId;

                                    //Add the Date of Liability for Registration to the Response and DB from the Request record.
                                    vehicleOwnerOnlineNCODetail.DateOfLiabilityForRegistration = request.ChangeDate;

                                    //Set Encrypted Values
                                    vehicleOwnerOnlineNCODetail.EncryptedVehicleCertificateNumber = encryptedVehicleCertificateNumber;
                                    vehicleOwnerOnlineNCODetail.EncryptedBarcode = encryptedBarcode;

                                    _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | Service : RTMC | Method : OnlineNCOAsync | Save Information to Log Table");
                                    var vehicleOwnerOnlineNCODetailResult = await _sharedServices.SaveVehicleOwnerOnlineNCODetail(vehicleOwnerOnlineNCODetail);

                                    //Remove Barcode from Call, as the Barcode can be utilized to get the Control Number, which is not secure.
                                    vehicleOwnerOnlineNCOResponse.EncryptedBarcode = null;

                                    result = vehicleOwnerOnlineNCOResponse;

                                    if(vehicleOwnerOnlineNCOResponse.BaseResponse.Successful != true)
                                    {
                                        result.BaseResponse.Successful = false;
                                        _sharedServices.SaveRTMCResponse(dbVehicleOwnerOnlineNCORequest.Id, request.User,natisVehicleOwnerOnlineNCOResponseString, vehicleOwnerOnlineNCOResponseString, "Failed", auditLogId);
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | Service : RTMC | Method : OnlineNCOAsync | Saved Failed Request to DB");

                                        return result;    
                                    }else
                                    {
                                        result.BaseResponse.Successful = true;
                                        _sharedServices.SaveRTMCResponse(dbVehicleOwnerOnlineNCORequest.Id, request.User,natisVehicleOwnerOnlineNCOResponseString, vehicleOwnerOnlineNCOResponseString, "Success", auditLogId);
                                        _logger.LogWarning("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | Service : RTMC | Method : OnlineNCOAsync | Saved Success Request to DB");

                                        try
                                        {
                                            //Publish Event
                                            await PublishVehicleOwnerRegistrationEvent(result);
                                        }catch(Exception ex)
                                        {
                                            _logger.LogError(ex,"GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | Reference : " + auditLogId + " | RTMCOnlineNCOService : Publish MassTransit  | Unable to Publish Mass Transit Event"+ result.ToString());
                                        }

                                        return result;    
                                    }

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " || RTMCOnlineNCOService : OnlineNCOAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information

                            _sharedServices.SaveRTMCResponse(dbVehicleOwnerOnlineNCORequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException(" Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCOnlineNCOService : OnlineNCOAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private VehicleOwnerOnlineNCORequest FormatInputRequest(VehicleOwnerOnlineNCORequest request){

            if(request.Receiver != null){
                //Remove Special Characters
                request.Receiver.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.Receiver.DocumentNumber);
                //UpperCase
                request.Receiver.DocumentNumber = request.Receiver.DocumentNumber.ToUpper();
            }

            if(request.ReceiverProxy != null){
                //Remove Special Characters
                request.ReceiverProxy.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.ReceiverProxy.DocumentNumber);
                //UpperCase
                request.ReceiverProxy.DocumentNumber = request.ReceiverProxy.DocumentNumber.ToUpper();
            }

            if(request.ReceiverRepresentative != null){
                //Remove Special Characters
                request.ReceiverRepresentative.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.ReceiverRepresentative.DocumentNumber);
                //UpperCase
                request.ReceiverRepresentative.DocumentNumber = request.ReceiverRepresentative.DocumentNumber.ToUpper();
            }

            //Vehicle information
            if(request.RegisterNumber != null){
                //Remove Special Characters
                request.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.RegisterNumber);
                //UpperCase
                request.RegisterNumber = request.RegisterNumber.ToUpper();
            }

            if(request.VinOrChassis != null){
                //Remove Special Characters
                request.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(request.VinOrChassis);
                //UpperCase
                request.VinOrChassis = request.VinOrChassis.ToUpper();
            }

            if(request.LicenceNumber != null){
                //Remove Special Characters
                request.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenceNumber);
                //UpperCase
                request.LicenceNumber = request.LicenceNumber.ToUpper();
            }

            
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(VehicleOwnerOnlineNCORequest request){

            RTMCRequest dbVehicleOwnerOnlineNCORequest = new RTMCRequest();
            try
            {
                dbVehicleOwnerOnlineNCORequest = _mapper.Map<RTMCRequest>(request);
                dbVehicleOwnerOnlineNCORequest.RequestObject = request.ToString();
                dbVehicleOwnerOnlineNCORequest.IntegrationType = "OnlineNCO";
                dbVehicleOwnerOnlineNCORequest.Date = DateTime.Now;
                dbVehicleOwnerOnlineNCORequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError("RTMCOnlineNCOService : MapRequestObjectToDB | An Exception occurred when mapping dbVehicleOwnerOnlineNCORequest to DB Object : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping dbVehicleOwnerOnlineNCORequest XML Response string to Class : Exception = " + ex);
            }

            return dbVehicleOwnerOnlineNCORequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCVehicleOwnerOnlineNCO
        /// </summary>
        /// <param name="vehicleOwnerOnlineNCOResponse"></param>
        /// <param name="encryptedVehicleCertificateNumber"></param>
        /// <param name="encryptedBarcode"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCVehicleOwnerOnlineNCODetail MapResponseObjectToRTMCVehicleOwnerOnlineNCODB(VehicleOwnerOnlineNCOResponse vehicleOwnerOnlineNCOResponse, byte[] encryptedVehicleCertificateNumber, byte[] encryptedBarcode){

            RTMCVehicleOwnerOnlineNCODetail dbVehicleOwnerOnlineNCODetail = new RTMCVehicleOwnerOnlineNCODetail();
            try
            {
                dbVehicleOwnerOnlineNCODetail = _mapper.Map<RTMCVehicleOwnerOnlineNCODetail>(vehicleOwnerOnlineNCOResponse);
                dbVehicleOwnerOnlineNCODetail.CreatedOn = DateTime.Now;
                dbVehicleOwnerOnlineNCODetail.EncryptedBarcode = encryptedBarcode;
                dbVehicleOwnerOnlineNCODetail.EncryptedVehicleCertificateNumber = encryptedVehicleCertificateNumber;

            }catch(Exception ex)
            {
                _logger.LogError("RTMCOnlineNCOService : MapResponseObjectToRTMCVehicleOwnerOnlineNCODB | An Exception occurred when mapping MapResponseObjectToRTMCVehicleOwnerOnlineNCODB RTMCVehicleOwnerOnlineNCODetail to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping vehicleOwnershipVerificationDetailResponse to Class : Exception = " + ex);
            }

            return dbVehicleOwnerOnlineNCODetail;
        }


        //Send Service Bus Message to Queue
        private async Task PublishVehicleOwnerRegistrationEvent(VehicleOwnerOnlineNCOResponse vehicleOwnerOnlineNCOResponse)
        {
            //Publish Event
            await _publishEndpoint.Publish(new OnlineTransactionCompleted() { VehicleOwnerOnlineNCOResponse = vehicleOwnerOnlineNCOResponse});
        }

        
        #endregion
    }

}