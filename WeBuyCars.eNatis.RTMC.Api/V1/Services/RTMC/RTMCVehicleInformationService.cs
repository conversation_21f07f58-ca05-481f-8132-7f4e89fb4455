using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicle;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCVehicleInformationService
    {

        readonly ILogger<RTMCVehicleInformationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly SharedServices _sharedServices;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        readonly ITestIntegration _testIntegration;
        readonly IVehicleIntegrationService _vehicleIntegrationService;



        #region ctor
            
        public RTMCVehicleInformationService(
            ILogger<RTMCVehicleInformationService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            SharedServices sharedServices,
            IVehicleIntegrationService vehicleIntegrationService,
            ITestIntegration testIntegration,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _vehicleIntegrationService = vehicleIntegrationService ?? throw new ArgumentNullException(nameof(vehicleIntegrationService));
            _testIntegration = testIntegration ?? throw new ArgumentNullException(nameof(testIntegration));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve Vehicle Information from Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>  
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<VehicleDetailResponse> GetVehicleAsync(VehicleDetailRequest request, string environmentName){

            VehicleDetailResponse result = new VehicleDetailResponse();

            Guid auditLogId = request.MessageId.Value;

            result.BaseResponse = new BaseResponse()
            {
                Successful = false,
                Message = "Failed to retrieve Vehicle Information",
                Reference = request.MessageId.Value
            };

            try{

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions+ " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Mapping Request Object");
                var dbVehicleRequest = MapRequestObjectToDB(request);

                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | If Check for dbVehicleRequest : " + dbVehicleRequest.ToString());
                if(dbVehicleRequest != null)
                {

                    //Write Request to DB
                    dbVehicleRequest.Reference = auditLogId;
                    var requestResult = _sharedServices.SaveRTMCRequest(dbVehicleRequest);

                    _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Map Request Parameter to Natis Vehicle Request");
                        var natisVehicleRequest = _mapper.Map<NatisGetVehicleRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            _logger.LogTrace("Service : RTMC | Method : GetVehicleAsync | Send Request Object to Natis");
                            //var natisVehicle = await _natisIntegrationService.GetVehicleQuery(natisVehicleRequest);
                            var natisVehicle = await _vehicleIntegrationService.GetVehicleQuery(auditLogId, natisVehicleRequest, environmentName);
                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Send Request Object to Natis");
                            //var natisVehicle = await _testIntegration.GetVehicleQuery(natisVehicleRequest);

                            _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions+ " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | If check for Natis Response : " + natisVehicle.ToString());
                            if(natisVehicle != null)
                            {
                                //Map Convert 
                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Convert Natis Response to vehicle Detail Response");
                                var vehicleDetailResponse = _mapper.Map<VehicleDetailResponse>(natisVehicle);

                                _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | If Check for Mapping vehicleDetailResponse");
                                if(vehicleDetailResponse != null)
                                {

                                    //Serialize Vehicle Detail Response to Json String for DB.
                                    var vehicleDetailResponseString = JsonConvert.SerializeObject(vehicleDetailResponse);
                                    var vehicleDetailString = JsonConvert.SerializeObject(natisVehicle);

                                    //Write Response Object to DB
                                    var responseresult = _sharedServices.SaveRTMCResponse(dbVehicleRequest.Id, request.User,vehicleDetailString, vehicleDetailResponseString, "Success", auditLogId);

                                    //Check if Response Object Wrote to DB
                                    if(responseresult > 0)
                                    {
                                        
                                        //Return Vehicle Detail Response object
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Return Success Result : " + responseresult.ToString());

                                        //Write Vehicle Information to Log Table
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Map Natis Vehicle Information to Log Table");
                                        var vehicleDetail = MapResponseObjectToVehicleDetailDB(vehicleDetailResponse);
                                        
                                        _logger.LogTrace("GUID : " + auditLogId + " : " + _serverEnvironmentOptions+ " | RTMCVehicleInformationService: RTMC | Method : GetVehicleAsync | Save Information to Log Table");
                                        var vehicleDetailresult = await _sharedServices.SaveVehicleDetail(vehicleDetail);

                                        if(vehicleDetailresult > 0)
                                        {
                                            result = vehicleDetailResponse;
                                            result.BaseResponse.Successful = true;
                                            result.BaseResponse.Reference = auditLogId;

                                            return result;
                                        }else{
                                            //Error to indicate that the Response Object was not able to be written to the DB
                                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | Unable to save Vehicle Detail to DB : " + request.ToString());
                                            throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | Unable to save Vehicle Detail to DB : " + request.ToString());                                           
                                        }
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information
                            _sharedServices.SaveRTMCResponse(dbVehicleRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", auditLogId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions + " | RTMCVehicleInformationService : Service : RTMC | Method : GetVehicleAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : GetVehicleAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError("GUID : " + auditLogId + " : " + _serverEnvironmentOptions+ " | RTMCVehicleInformationService | An Exception occurred : Service : RTMC | Method : GetVehicleAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : GetVehicleAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private VehicleDetailRequest FormatInputRequest(VehicleDetailRequest request){

            if(!String.IsNullOrEmpty(request.EngineNumber)){

                //Remove Special Characters
                request.EngineNumber = HelperExtensions.RemoveSpecialCharacters(request.EngineNumber);
                //UpperCase
                request.EngineNumber = request.EngineNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.LicenceNumber)){

                //Remove Special Characters
                request.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenceNumber);
                //UpperCase
                request.LicenceNumber = request.LicenceNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.RegisterNumber)){

                //Remove Special Characters
                request.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.RegisterNumber);
                //UpperCase
                request.RegisterNumber = request.RegisterNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vin)){

                //Remove Special Characters
                request.Vin = HelperExtensions.RemoveSpecialCharacters(request.Vin);
                //UpperCase
                request.Vin = request.Vin.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.RegistrationNumber)){

                //Remove Special Characters
                request.RegistrationNumber = HelperExtensions.RemoveSpecialCharacters(request.RegistrationNumber);
                //UpperCase
                request.RegistrationNumber = request.RegistrationNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.LicenseNumber)){

                //Remove Special Characters
                request.LicenseNumber = HelperExtensions.RemoveSpecialCharacters(request.LicenseNumber);
                //UpperCase
                request.LicenseNumber = request.LicenseNumber.ToUpper();
            }
            
            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(VehicleDetailRequest request){

            RTMCRequest dbVehicleRequest = new RTMCRequest();
            try
            {
                dbVehicleRequest = _mapper.Map<RTMCRequest>(request);
                dbVehicleRequest.RequestObject = request.ToString();
                dbVehicleRequest.IntegrationType = "GetVehicle";
                dbVehicleRequest.Date = DateTime.Now;
                dbVehicleRequest.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleInformationService | An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapXMLtoVehicleResponse XML Response string to Class : Exception = " + ex);
            }

            return dbVehicleRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCVehicleDetail
        /// </summary>
        /// <param name="vehicleDetailResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCVehicleDetail MapResponseObjectToVehicleDetailDB(VehicleDetailResponse vehicleDetailResponse){

            RTMCVehicleDetail dbVehicleDetail = new RTMCVehicleDetail();
            try
            {
                dbVehicleDetail = _mapper.Map<RTMCVehicleDetail>(vehicleDetailResponse);
                dbVehicleDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCVehicleInformationService | An Exception occurred when mapping MapResponseObjectToVehicleDetailDB vehicleDetailResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapResponseObjectToVehicleDetailDB vehicleDetailResponse to Class : Exception = " + ex);
            }

            return dbVehicleDetail;
        }

        #endregion





    }

}