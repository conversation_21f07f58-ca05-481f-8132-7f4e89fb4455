using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.CompleteVehicleLicenceRenewal;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.GetVehiclesAndLicenseExpiryDates;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.RenewVehicleLicense;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.VehiclesLicenceRenewalQuotation;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.CompleteRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesDueForRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesQuotation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.InitiateRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC;

public sealed class RTMCVehicleLicenseRenewalService(
    ILogger<RTMCVehicleLicenseRenewalService> logger,
    IMapper mapper,
    INatisIntegrationService natisIntegrationService,
    RTMCSettingsService rtmcSettingsService)
{
    #region Class Members

    private readonly ILogger<RTMCVehicleLicenseRenewalService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
    private readonly INatisIntegrationService _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
    private readonly RTMCSettingsService _rtmcSettingsService = rtmcSettingsService ?? throw new ArgumentNullException(nameof(rtmcSettingsService));

    #endregion


    #region Public Methods

    public async Task<GetVehiclesAndLicenseExpiryDatesResponse> GetVehicleLicenseRenewalsAsync(GetVehiclesAndLicenseExpiryDatesRequest getVehiclesAndLicenseExpiryDatesRequest)
    {
        getVehiclesAndLicenseExpiryDatesRequest.MessageId = Guid.NewGuid();
        getVehiclesAndLicenseExpiryDatesRequest = FormatVehicleLicenceExpiryRequest(getVehiclesAndLicenseExpiryDatesRequest);
        var brnNumber = getVehiclesAndLicenseExpiryDatesRequest.BusinessRegistrationNumber;

        try
        {
            // TODO: Change Audit Log Table
            var dbVehicleLicenseExpiryRequest = MapRequestObjectToDB(getVehiclesAndLicenseExpiryDatesRequest, "GetVehiclesAndLicenceExpiryDates");

            var natisRequest = _mapper.Map<NatisGetVehiclesAndLicenseExpiryDatesRequest>(getVehiclesAndLicenseExpiryDatesRequest);
            var token = await _rtmcSettingsService.GetAccessTokenByBRNAsync(brnNumber);
            
            var natisResponse = await _natisIntegrationService.GetVehiclesAndLicenseExpiryDatesQuery(natisRequest, token.access_token, brnNumber);
            if (natisResponse == null)
                throw new DomainException("Natis Get Vehicles And License Expiry Dates response is empty");

            var vehicleLicenseExpiryResponse = _mapper.Map<GetVehiclesAndLicenseExpiryDatesResponse>(natisResponse);
            if (vehicleLicenseExpiryResponse == null)
                throw new DomainException("Mapping Natis Get Vehicles And License Expiry Dates response failed");

            vehicleLicenseExpiryResponse.BaseResponse.Reference = (Guid)getVehiclesAndLicenseExpiryDatesRequest.MessageId;
            return vehicleLicenseExpiryResponse;
        }
        catch (Exception ex)
        {
            throw new DomainException("GetVehicleLicenseRenewalsAsync exception occurred:", ex);
        }
    }

    public async Task<VehiclesLicenceRenewalQuotationResponse> GetVehicleLicenseRenewalQuotationsAsync(VehiclesLicenceRenewalQuotationRequest quotationRequest)
    {
        var brnNumber = quotationRequest.BusinessRegistrationNumber;
        quotationRequest.MessageId = Guid.NewGuid();
        try
        {
            var natisRequest = _mapper.Map<NatisGetVehiclesQuotationRequest>(quotationRequest);
            var token = await _rtmcSettingsService.GetAccessTokenByBRNAsync(brnNumber);
            
            var natisResponse = await _natisIntegrationService.GetVehiclesLicenceRenewalQuotationQuery(natisRequest, token.access_token, brnNumber);
            if (natisResponse == null)
                throw new DomainException("Natis Vehicle License Renewal Quotation response is empty");

            var response = _mapper.Map<VehiclesLicenceRenewalQuotationResponse>(natisResponse);
            if (response == null)
                throw new DomainException("Mapping Natis Initiate Vehicle License Renewal Quotation response failed");

            response.BaseResponse.Reference = (Guid)quotationRequest.MessageId;
            return response;
        }
        catch (Exception ex)
        {
            throw new DomainException("GetVehicleLicenseRenewalQuotationsAsync exception occurred:", ex);
        }
    }

    public async Task<RenewVehicleLicenseResponse> InitiateVehicleLicenceRenewalAsync(RenewVehicleLicenseRequest renewRequest)
    {
        var brnNumber = renewRequest.BusinessRegistrationNumber;
        renewRequest.MessageId = Guid.NewGuid();
        try
        {
            var natisRequest = _mapper.Map<NatisInitiateRenewalRequest>(renewRequest);
            var token = await _rtmcSettingsService.GetAccessTokenByBRNAsync(brnNumber);
            
            var natisResponse = await _natisIntegrationService.InitiateVehicleLicenseRenewalQuery(natisRequest, token.access_token, brnNumber);
            if (natisResponse == null)
                throw new DomainException("Natis Initiate Vehicle License Renewal response is empty");

            var response = _mapper.Map<RenewVehicleLicenseResponse>(natisResponse);
            if (response == null)
                throw new DomainException("Mapping Natis Initiate Vehicle License Renewal response failed");

            response.BaseResponse.Reference = (Guid)renewRequest.MessageId;
            return response;
        }
        catch (Exception ex)
        {
            throw new DomainException("InitiateVehicleLicenceRenewalAsync exception occurred:", ex);
        }
    }

    public async Task<CompleteVehicleLicenseRenewalResponse> CompleteVehicleLicenseRenewalAsync(CompleteVehicleLicenseRenewalRequest completeRequest)
    {
        var brnNumber = completeRequest.BusinessRegistrationNumber;
        completeRequest.MessageId = Guid.NewGuid();
        
        try
        {
            var natisRequest = _mapper.Map<NatisCompleteRenewalRequest>(completeRequest);
            var token = await _rtmcSettingsService.GetAccessTokenByBRNAsync(brnNumber);
            
            var natisResponse = await _natisIntegrationService.CompleteVehicleLicenseRenewalQuery(natisRequest, token.access_token, brnNumber);
            if (natisResponse == null)
                throw new DomainException("Natis complete renewal response is empty");

            var response = _mapper.Map<CompleteVehicleLicenseRenewalResponse>(natisResponse);
            if (response == null)
                throw new DomainException("Mapping Natis complete renewal response failed");

            response.BaseResponse.Reference = (Guid)completeRequest.MessageId;
            return response;
        }
        catch (Exception ex)
        {
            throw new DomainException("CompleteVehicleLicenseRenewalAsync exception occurred:", ex);
        }
    }

    #endregion

    #region Private Methods

    /// <summary>
    /// Force Request Object to UpperCase and remove un wanted characters
    /// </summary>
    /// <param name="datesRequest"></param>
    /// <returns></returns>
    private static dynamic FormatVehicleLicenceExpiryRequest(GetVehiclesAndLicenseExpiryDatesRequest datesRequest)
    {
        if (string.IsNullOrEmpty(datesRequest.IdentificationNumber)) return datesRequest;
        datesRequest.IdentificationNumber = HelperExtensions.RemoveSpecialCharacters(datesRequest.IdentificationNumber);
        datesRequest.IdentificationNumber = datesRequest.IdentificationNumber.ToUpper();

        return datesRequest;
    }

    /// <summary>
    /// Convert Request Object to DBRequest (generic for all integration types)
    /// </summary>
    /// <typeparam name="TRequest">Type of the request object</typeparam>
    /// <param name="request">The request object</param>
    /// <param name="integrationType">Integration type string</param>
    /// <returns></returns>
    /// <exception cref="ArgumentNullException"></exception>
    private RTMCRequest MapRequestObjectToDB<TRequest>(TRequest request, string integrationType)
    {
        RTMCRequest dbRequest;
        try
        {
            dbRequest = _mapper.Map<RTMCRequest>(request);
            dbRequest.RequestObject = request?.ToString();
            dbRequest.IntegrationType = integrationType;
            dbRequest.Date = DateTime.Now;
            dbRequest.CreatedOn = DateTime.Now;
            var messageIdProp = request?.GetType().GetProperty("MessageId");
            if (messageIdProp != null)
            {
                var messageIdValue = messageIdProp.GetValue(request);
                if (messageIdValue is Guid guidValue)
                {
                    dbRequest.Reference = guidValue;
                }
            }
        }
        catch (Exception ex)
        {
            throw new DomainException("An exception occurred when mapping MapRequestObjectToDB REST Request", ex);
        }

        return dbRequest;
    }
    
    #endregion
}