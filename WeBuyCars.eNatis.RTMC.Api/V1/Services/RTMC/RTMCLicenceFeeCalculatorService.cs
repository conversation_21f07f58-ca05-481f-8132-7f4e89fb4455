using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using System.Collections.Generic;
using System.Linq;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationResponse;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisCalculateVehicleLicenceFeeRequest;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCLicenceFeeCalculatorService
    {

        readonly ILogger<RTMCLicenceFeeCalculatorService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly SharedServices _sharedServices;
        readonly RTMCSettingsService _rTMCSettingService;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;


        #region ctor

        public RTMCLicenceFeeCalculatorService(
            ILogger<RTMCLicenceFeeCalculatorService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCSettingsService rTMCSettingService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve the Calculation from RTMC for the Vehicle
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<FeeCalculationResponse> CalculateVehicleLicenceFeeAsync(FeeCalculationRequest request, string environmentName){

            FeeCalculationResponse result = new FeeCalculationResponse();

            try{

                if(!request.MessageId.HasValue){
                    //Create GUID for MessageId
                    Guid messageId = Guid.NewGuid();
                    request.MessageId = messageId;
                }

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | Mapping Request Object");
                var dbLicenceFeeCalculatorRequest = MapRequestObjectToDB(request);

                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | If Check for dbLicenceFeeCalculatorRequest : " + dbLicenceFeeCalculatorRequest.ToString());
                if(dbLicenceFeeCalculatorRequest != null)
                {

                    //Write Request to DB
                    var requestResult = _sharedServices.SaveRTMCRequest(dbLicenceFeeCalculatorRequest);

                    _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | Map Request Parameter to Natis  Request");
                        var natisLicenceFeeCalculatorRequest = _mapper.Map<NatisCalculateVehicleLicenceFeeRequest>(request);

                        //Convert Input DateTime to a Valid String Format for RTMC
                        natisLicenceFeeCalculatorRequest.data.effectiveDate = FormatDate(request.EffectiveDate);

                        //Send Request to Natis Interface
                        try
                        {

                            //Get Token to be sent
                            var token = await _rTMCSettingService.GetAccessTokenByBRNAsync(request.BusinessRegistrationNumber);

                            _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | Send Request Object to Natis");
                            var natisLicenceFeeCalculation = await _natisIntegrationService.CalculateVehicleLicenceFeeQuery(natisLicenceFeeCalculatorRequest,token.access_token, request.BusinessRegistrationNumber);

                            _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | If check for Natis Response : " + natisLicenceFeeCalculation.ToString());
                            if(natisLicenceFeeCalculation != null)
                            {

                                FeeCalculationResponse licenceFeeCalculationDetailResponse = new FeeCalculationResponse();

                                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | Convert Natis Response to Calculate Vehicle Licence Fee");
                                licenceFeeCalculationDetailResponse = _mapper.Map<FeeCalculationResponse>(natisLicenceFeeCalculation);


                                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | If Check for Mapping Vehicle Licence Fee Calculation");
                                if(licenceFeeCalculationDetailResponse != null)
                                {

                                    //Apply Error Message to licenceFeeCalculationDetailResponse
                                    if(natisLicenceFeeCalculation.result.successful != true)
                                    {
                                        licenceFeeCalculationDetailResponse.BaseResponse.Successful = false;                     
                                    }else
                                    {
                                        licenceFeeCalculationDetailResponse.BaseResponse.Successful = true;
                                    }

                                    if(natisLicenceFeeCalculation.result.errorMessages != null)
                                    {
                                        foreach(var error in natisLicenceFeeCalculation.result.errorMessages)
                                        {
                                            licenceFeeCalculationDetailResponse.BaseResponse.Message = licenceFeeCalculationDetailResponse.BaseResponse.Message + error.message + " | " ;
                                        }
                                        //Strip Last Pipe
                                        if (!string.IsNullOrEmpty(licenceFeeCalculationDetailResponse.BaseResponse.Message))
                                        {
                                            licenceFeeCalculationDetailResponse.BaseResponse.Message = licenceFeeCalculationDetailResponse.BaseResponse.Message.Substring(0, licenceFeeCalculationDetailResponse.BaseResponse.Message.Length - 3); 
                                        }
                                    }       

                                    //Serialize LiceneFee Calculation Detail Response to Json String for DB.
                                    var licenceFeeCalculationDetailResponseString = JsonConvert.SerializeObject(licenceFeeCalculationDetailResponse);
                                    var licenceFeeCalculationDetailString = JsonConvert.SerializeObject(natisLicenceFeeCalculation);
 
                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbLicenceFeeCalculatorRequest.Id, request.User,licenceFeeCalculationDetailString, licenceFeeCalculationDetailResponseString, "Success", (Guid)request.MessageId);

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {
                                        
                                        //Write CalculateLicenceFee Information to Log Table
                                        _logger.LogWarning(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Method : CalculateVehicleLicenceFeeAsync | Map Natis Calculate Vehicle Licence Fee to Log Table");

                                        result = licenceFeeCalculationDetailResponse;
                                        result.BaseResponse.Reference = (Guid)request.MessageId;

                                        return result;
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information
                            _sharedServices.SaveRTMCResponse(dbLicenceFeeCalculatorRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", (Guid)request.MessageId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | An Exception occurred : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : CalculateVehicleLicenceFeeAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase and remove un wanted characters
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private dynamic FormatInputRequest(FeeCalculationRequest request){

            if(!String.IsNullOrEmpty(request.Owner.DocumentNumber)){

                request.Owner.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.Owner.DocumentNumber);
                request.Owner.DocumentNumber = request.Owner.DocumentNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.RegisterNumber)){

                request.Vehicle.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.RegisterNumber);
                request.Vehicle.RegisterNumber = request.Vehicle.RegisterNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.VinOrChassis)){

                request.Vehicle.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.VinOrChassis);
                request.Vehicle.VinOrChassis = request.Vehicle.VinOrChassis.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.ControlNumber)){

                request.Vehicle.ControlNumber = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.ControlNumber);
                request.Vehicle.ControlNumber = request.Vehicle.ControlNumber.ToUpper();
            }

            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(FeeCalculationRequest request){

            RTMCRequest dbCalculateLicenceFeeRequest = new RTMCRequest();
            try
            {
                dbCalculateLicenceFeeRequest = _mapper.Map<RTMCRequest>(request);
                dbCalculateLicenceFeeRequest.RequestObject = request.ToString();
                dbCalculateLicenceFeeRequest.IntegrationType = "CalculateVehicleLicenceFee";
                dbCalculateLicenceFeeRequest.Date = DateTime.Now;
                dbCalculateLicenceFeeRequest.CreatedOn = DateTime.Now;
                dbCalculateLicenceFeeRequest.Reference = (Guid)request.MessageId;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Fee Calculation Response
                throw new DomainException("An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
            }

            return dbCalculateLicenceFeeRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCControlNumberVerificationResponse
        /// </summary>
        /// <param name="ownershipHistoryResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private List<RTMCOwnershipHistoryDetail> MapResponseObjectToFeeCalculationDB(ControlNumberVerificationResponse ownershipHistoryResponse){

            List<RTMCOwnershipHistoryDetail> dbOwnershipHistoryDetail = new List<RTMCOwnershipHistoryDetail>();
            try
            {
                var test = _mapper.Map<RTMCOwnershipHistoryDetail>(ownershipHistoryResponse);
                //dbOwnershipHistoryDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCLicenceFeeCalculatorService | An Exception occurred when mapping MapResponseObjectToFeeCalculationDB OwnershipHistoryResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to OwnershipHistory Response
                throw new DomainException("An Exception occurred when mapping MapResponseObjectToFeeCalculationDB OwnershipHistoryResponse to Class : Exception = " + ex);
            }

            return dbOwnershipHistoryDetail;
        }


        //Convert RTMC Date
        private string FormatDate(DateTime date)
        {
            return date.ToString("yyyy-MM-dd");
        }


        #endregion





    }

}