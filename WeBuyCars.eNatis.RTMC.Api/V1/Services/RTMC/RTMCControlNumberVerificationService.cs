using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using System.Collections.Generic;
using System.Linq;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationResponse;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCControlNumberVerificationService
    {

        readonly ILogger<RTMCControlNumberVerificationService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly SharedServices _sharedServices;
        readonly RTMCSettingsService _rTMCSettingService;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;


        #region ctor

        public RTMCControlNumberVerificationService(
            ILogger<RTMCControlNumberVerificationService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCSettingsService rTMCSettingService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Verify Control Number is the latest based on Vehicle, Owner and TitleHolder
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<ControlNumberVerificationResponse> GetControlNumberVerificationAsync(ControlNumberVerificationRequest request, string environmentName){

            ControlNumberVerificationResponse result = new ControlNumberVerificationResponse();

            try{

                if(!request.MessageId.HasValue){
                    //Create GUID for MessageId
                    Guid messageId = Guid.NewGuid();
                    request.MessageId = messageId;
                }

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Mapping Request Object");
                var dbControlNumberVerificationRequest = MapRequestObjectToDB(request);

                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | If Check for dbControlNumberVerificationRequest : " + dbControlNumberVerificationRequest.ToString());
                if(dbControlNumberVerificationRequest != null)
                {

                    //Write Request to DB
                    var requestResult = _sharedServices.SaveRTMCRequest(dbControlNumberVerificationRequest);

                    _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Map Request Parameter to Natis  Request");
                        var natisControlNumberVerificationRequest = _mapper.Map<NatisControlNumberVerificationRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            //Get Token to be sent
                            var token = await _rTMCSettingService.GetAccessTokenByBRNAsync(request.BusinessRegistrationNumber);

                            _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Send Request Object to Natis");
                            var natisControlNumberVerification = await _natisIntegrationService.ControlNumberVerificationQuery(natisControlNumberVerificationRequest,token.access_token, request.BusinessRegistrationNumber);

                            _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | If check for Natis Response : " + natisControlNumberVerification.ToString());
                            if(natisControlNumberVerification != null)
                            {

                                ControlNumberVerificationResponse controlNumberVerificationResponse = new ControlNumberVerificationResponse();

                                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Convert Natis Response to Control Number Verification Object");
                                controlNumberVerificationResponse = _mapper.Map<ControlNumberVerificationResponse>(natisControlNumberVerification);


                                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | If Check for Mapping Control Number Verification objects");
                                if(controlNumberVerificationResponse != null)
                                {

                                    //Serialize Ownership History Detail Response to Json String for DB.
                                    var controlNumberVerificationResponseString = JsonConvert.SerializeObject(controlNumberVerificationResponse);
                                    var controlNumberVerificationString = JsonConvert.SerializeObject(natisControlNumberVerification);
                                         
                                    //Write Vehicle Information to Log Table
                                    _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Map Natis Control Number Verification to Log Table");

                                    result = controlNumberVerificationResponse;
                                    result.BaseResponse.Reference = (Guid)request.MessageId;

                                    if(natisControlNumberVerification.result.successful != true)
                                    {
                                        result.BaseResponse.Successful = false;
                                        result.BaseResponse.Message = string.Join("-", natisControlNumberVerification.result.errorMessages);
                                    }else
                                    {
                                        result.BaseResponse.Successful = true;
                                    }
                                    controlNumberVerificationResponseString = JsonConvert.SerializeObject(result);
                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbControlNumberVerificationRequest.Id, request.User,controlNumberVerificationString, controlNumberVerificationResponseString, "Success", (Guid)request.MessageId);

                                    return result;                                        

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : GetControlNumberVerificationAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : GetControlNumberVerificationAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information
                            _sharedServices.SaveRTMCResponse(dbControlNumberVerificationRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", (Guid)request.MessageId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : GetControlNumberVerificationAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : GetControlNumberVerificationAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogWarning(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : GetControlNumberVerificationAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService : RTMC | Method : GetControlNumberVerificationAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : GetControlNumberVerificationAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase and remove un wanted characters
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private dynamic FormatInputRequest(ControlNumberVerificationRequest request){

            if(!String.IsNullOrEmpty(request.Owner.DocumentNumber)){

                request.Owner.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.Owner.DocumentNumber);
                request.Owner.DocumentNumber = request.Owner.DocumentNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.TitleHolder.DocumentNumber)){

                request.TitleHolder.DocumentNumber = HelperExtensions.RemoveSpecialCharacters(request.TitleHolder.DocumentNumber);
                request.TitleHolder.DocumentNumber = request.TitleHolder.DocumentNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.RegisterNumber)){

                request.Vehicle.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.RegisterNumber);
                request.Vehicle.RegisterNumber = request.Vehicle.RegisterNumber.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.VinOrChassis)){

                request.Vehicle.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.VinOrChassis);
                request.Vehicle.VinOrChassis = request.Vehicle.VinOrChassis.ToUpper();
            }

            if(!String.IsNullOrEmpty(request.Vehicle.ControlNumber)){

                request.Vehicle.ControlNumber = HelperExtensions.RemoveSpecialCharacters(request.Vehicle.ControlNumber);
                request.Vehicle.ControlNumber = request.Vehicle.ControlNumber.ToUpper();
            }

            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB(ControlNumberVerificationRequest request){

            RTMCRequest dbControlNumberVerificationRequest = new RTMCRequest();
            try
            {
                dbControlNumberVerificationRequest = _mapper.Map<RTMCRequest>(request);
                dbControlNumberVerificationRequest.RequestObject = request.ToString();
                dbControlNumberVerificationRequest.IntegrationType = "GetControlNumberVerification";
                dbControlNumberVerificationRequest.Date = DateTime.Now;
                dbControlNumberVerificationRequest.CreatedOn = DateTime.Now;
                dbControlNumberVerificationRequest.Reference = (Guid)request.MessageId;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService | An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
            }

            return dbControlNumberVerificationRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCControlNumberVerificationResponse
        /// </summary>
        /// <param name="controlNumberVerificationResponse"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private List<RTMCOwnershipHistoryDetail> MapResponseObjectToOwnershipHistoryDetailDB(ControlNumberVerificationResponse controlNumberVerificationResponse){

            List<RTMCOwnershipHistoryDetail> dbControlNumberVerificationDetail = new List<RTMCOwnershipHistoryDetail>();
            try
            {
                var test = _mapper.Map<RTMCOwnershipHistoryDetail>(controlNumberVerificationResponse);

            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCControlNumberVerificationService | An Exception occurred when mapping MapResponseObjectToControlNumberVerificationDetailDB controlNumberVerificationResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to ControlNumberVerification Response
                throw new DomainException("An Exception occurred when mapping MapResponseObjectToControlNumberVerificationDetailDB controlNumberVerificationResponse to Class : Exception = " + ex);
            }

            return dbControlNumberVerificationDetail;
        }

        #endregion





    }

}