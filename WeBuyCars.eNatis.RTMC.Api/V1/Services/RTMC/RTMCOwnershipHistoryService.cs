using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using System.Collections.Generic;
using System.Linq;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using Microsoft.Extensions.Options;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Services.RTMC
{
    public class RTMCOwnershipHistoryService
    {

        readonly ILogger<RTMCOwnershipHistoryService> _logger;
        readonly IMapper _mapper;
        readonly INatisIntegrationService _natisIntegrationService;
        readonly IRTMCRequestRepository _rTMCRequestRepository;
        readonly IRTMCResponseRepository _rTMCResponseRepository;
        readonly SharedServices _sharedServices;
        readonly RTMCSettingsService _rTMCSettingService;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;


        #region ctor

        public RTMCOwnershipHistoryService(
            ILogger<RTMCOwnershipHistoryService> logger,
            IMapper mapper,
            INatisIntegrationService natisIntegrationService,
            IRTMCRequestRepository rTMCRequestRepository,
            IRTMCResponseRepository rTMCResponseRepository,
            SharedServices sharedServices,
            RTMCSettingsService rTMCSettingService,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _natisIntegrationService = natisIntegrationService ?? throw new ArgumentNullException(nameof(natisIntegrationService));
            _rTMCRequestRepository = rTMCRequestRepository ?? throw new ArgumentNullException(nameof(rTMCRequestRepository));
            _rTMCResponseRepository = rTMCResponseRepository ?? throw new ArgumentNullException(nameof(rTMCResponseRepository));
            _sharedServices = sharedServices ?? throw new ArgumentNullException(nameof(sharedServices));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
        }

        #endregion

        #region public methods

        /// <summary>
        /// Retrieve Vehicle Information from Enatis
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<OwnershipHistoryDataResponse> GetVehicleOwnershipHistoryAsync(OwnershipHistoryDataRequest request, string environmentName){

            OwnershipHistoryDataResponse result = new OwnershipHistoryDataResponse();

            try{

                if(!request.MessageId.HasValue){
                    //Create GUID for MessageId
                    Guid messageId = Guid.NewGuid();
                    request.MessageId = messageId;
                }

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Mapping Request Object");
                var dbOwnershipHistoryRequest = MapRequestObjectToDB(request);

                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for dbOwnershipHistoryRequest : " + dbOwnershipHistoryRequest.ToString());
                if(dbOwnershipHistoryRequest != null)
                {

                    //Write Request to DB
                    var requestResult = _sharedServices.SaveRTMCRequest(dbOwnershipHistoryRequest);

                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Map Request Parameter to Natis Ownership History Request");
                        var natisOwnershipHistoryRequest = _mapper.Map<NatisOwnershipHistoryRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            //Get Token to be sent
                            var token = await _rTMCSettingService.GetAccessTokenByBRNAsync(request.BusinessRegistrationNumber);

                            _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Send Request Object to Natis");
                            var natisOwnershipHistory = await _natisIntegrationService.OwnershipHistoryQuery(natisOwnershipHistoryRequest,token.access_token, request.BusinessRegistrationNumber);

                            _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If check for Natis Response : " + natisOwnershipHistory.ToString());
                            if(natisOwnershipHistory != null)
                            {

                                OwnershipHistoryDataResponse ownershipHistoryDetailResponse = new OwnershipHistoryDataResponse();

                                ownershipHistoryDetailResponse = _mapper.Map<OwnershipHistoryDataResponse>(natisOwnershipHistory);        

                                foreach(var item in ownershipHistoryDetailResponse.ownershipHistory)
                                {
                                    //Write Response Record to History Log Table
                                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Map Natis Vehicle Information to Log Table");
                                    var ownershipHistoryDetail = MapResponseObjectToOwnershipHistoryDetailDB(item, natisOwnershipHistory, request.MessageId);

                                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Save Information to Log Table");
                                    try
                                    {
                                        await _sharedServices.SaveOwnershipHistoryDetail(ownershipHistoryDetail);
                                    }catch (Exception ex)
                                    {
                                        _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | An error occurred trying to save a History Line item : " + ownershipHistoryDetail + " | Exception : " + ex.Message);
                                    }

                                    if (request.IncludeSensitiveInformation != true)
                                    {
                                         _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Convert Natis Response to Ownership History Detail Response");

                                         if (!string.IsNullOrEmpty(item.owner.identificationNumber) && item.owner.identificationNumber.Length >= 4)
                                        {
                                            string lastFourDigits = item.owner.identificationNumber.Substring(item.owner.identificationNumber.Length - 4);
                                            item.owner.identificationNumber = "*********"+ lastFourDigits;

                                        }else
                                        {
                                            item.owner.identificationNumber = "";
                                        }                                        
                                    }
                                }

                                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for Mapping Ownership History");
                                if(ownershipHistoryDetailResponse != null)
                                {

                                    //Serialize Ownership History Detail Response to Json String for DB.
                                    var ownershipHistoryDetailResponseString = JsonConvert.SerializeObject(ownershipHistoryDetailResponse);
                                    var ownershipHistoryDetailString = JsonConvert.SerializeObject(natisOwnershipHistory);
 
                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbOwnershipHistoryRequest.Id, request.User,ownershipHistoryDetailString, ownershipHistoryDetailResponseString, "Success", (Guid)request.MessageId);

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {
                                        
                                        //Write Vehicle Information to Log Table
                                        _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Map Natis Ownership History Information to Log Table");

                                        result = ownershipHistoryDetailResponse;
                                        result.BaseResponse.Reference = (Guid)request.MessageId;

                                        if(natisOwnershipHistory.result.successful != true)
                                        {
                                            result.BaseResponse.Successful = false;
                                            result.BaseResponse.Message = string.Join("-", natisOwnershipHistory.result.errorMessages);
                                        }else
                                        {
                                            result.BaseResponse.Successful = true;
                                        }

                                        return result;
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to save Enatis Response to DB : " + ownershipHistoryDetailResponseString.ToString());
                                        throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information
                            _sharedServices.SaveRTMCResponse(dbOwnershipHistoryRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", (Guid)request.MessageId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | An Exception occurred : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : GetVehicleOwnershipHistoryAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        /// <summary>
        /// Compare if a Individual or Company ever owned a Specific Vehicle
        /// </summary>
        /// <param name="request"></param>
        /// <param name="environmentName"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        public async Task<OwnershipHistoryVerificationResponse> GetVehicleOwnershipHistoryVerificationAsync(OwnershipHistoryVerificationRequest request,string environmentName){

            OwnershipHistoryVerificationResponse result = new OwnershipHistoryVerificationResponse();

            try{

                if(!request.MessageId.HasValue){
                    //Create GUID for MessageId
                    Guid messageId = Guid.NewGuid();
                    request.MessageId = messageId;
                }

                //Force Request values to Uppercase
                request = FormatInputRequest(request);

                //Map Request to db object
                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Mapping Request Object");
                var dbOwnershipHistoryRequest = MapRequestObjectToDB(request);

                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for dbOwnershipHistoryRequest : " + dbOwnershipHistoryRequest.ToString());
                if(dbOwnershipHistoryRequest != null)
                {

                    //Write Request to DB
                    var requestResult = _sharedServices.SaveRTMCRequest(dbOwnershipHistoryRequest);

                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for requestResult : " + requestResult.ToString());
                    //Check if record have been entered correctly in db before initiating request
                    if(requestResult > 0)
                    {

                        //Map Request to Natis Object
                        _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Map Request Parameter to Natis Ownership History Request");
                        var natisOwnershipHistoryRequest = _mapper.Map<NatisOwnershipHistoryRequest>(request);

                        //Send Request to Natis Interface
                        try
                        {

                            //Get Token to be sent
                            var token = await _rTMCSettingService.GetAccessTokenByBRNAsync(request.BusinessRegistrationNumber);

                            _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Send Request Object to Natis");
                            var natisOwnershipHistory = await _natisIntegrationService.OwnershipHistoryQuery(natisOwnershipHistoryRequest,token.access_token, request.BusinessRegistrationNumber);

                            _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If check for Natis Response : " + natisOwnershipHistory.ToString());
                            if(natisOwnershipHistory != null)
                            {

                                OwnershipHistoryDataResponse ownershipHistoryDetailResponse = new OwnershipHistoryDataResponse();

                                //Strip Confidential Information from Response Object
                                if(request.IncludeSensitiveInformation != true)
                                {
                                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Convert Natis Response to Ownership History Detail Response");
                                    ownershipHistoryDetailResponse = _mapper.Map<OwnershipHistoryDataResponse>(natisOwnershipHistory);

                                    foreach(var item in ownershipHistoryDetailResponse.ownershipHistory){
                                        item.owner.identificationNumber = "";
                                    }

                                }else
                                {
                                    _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Convert Natis Response to Ownership History Detail Response with Sensitive Information");
                                    ownershipHistoryDetailResponse = _mapper.Map<OwnershipHistoryDataResponse>(natisOwnershipHistory);
                                }

                                _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | If Check for Mapping Ownership History");
                                if(ownershipHistoryDetailResponse != null)
                                {

                                    //Serialize Ownership History Detail Response to Json String for DB.
                                    var ownershipHistoryDetailResponseString = JsonConvert.SerializeObject(ownershipHistoryDetailResponse);
                                    var ownershipHistoryDetailString = JsonConvert.SerializeObject(natisOwnershipHistory);
 
                                    //Write Response Object to DB
                                    var responseResult = _sharedServices.SaveRTMCResponse(dbOwnershipHistoryRequest.Id, request.User,ownershipHistoryDetailString, ownershipHistoryDetailResponseString, "Success", (Guid)request.MessageId);

                                    //Check if Response Object Wrote to DB
                                    if(responseResult > 0)
                                    {
                                        
                                        //Write Vehicle Information to Log Table
                                        _logger.LogTrace(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Map Natis Ownership History Information to Log Table");

                                        result = CalculateOwnershipTitleholderLogic(ownershipHistoryDetailResponse, request.IdentifierNumber, result);

                                        result.BaseResponse.Reference = (Guid)request.MessageId;

                                        if(natisOwnershipHistory.result.successful != true)
                                        {
                                            result.BaseResponse.Successful = false;
                                            result.BaseResponse.Message = string.Join("-", natisOwnershipHistory.result.errorMessages);
                                        }else
                                        {
                                            result.BaseResponse.Successful = true;
                                        }

                                        return result;
                                        // }else{
                                        //     //Error to indicate that the Response Object was not able to be written to the DB
                                        //     _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to save Ownership History Detail to DB : " + request.ToString());
                                        //     throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to save Ownership History Detail to DB : " + request.ToString());                                           
                                        // }
                                        
                                    }else{
                                        //Error to indicate that the Response Object was not able to be written to the DB
                                        _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                        throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to save Enatis Response to DB : " + request.ToString());
                                    }                            

                                }else
                                {
                                    //Error to indicate that the Response Object could not be Converted to a usable Json Object
                                    _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                    throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to convert Enatis Response to DB Object : " + request.ToString());
                                }

                            }else{
                                //Error to indicate that there was not a response from the Enatis Web Service
                                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | The Enatis Response was blank : " + request.ToString());
                                throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | The Enatis Response was blank : " + request.ToString());
                            }
                            
                        }catch(Exception ex)
                        {

                            //Write Error Log in DB that will match Corresponding Request
                            //Set Response Record information
                            _sharedServices.SaveRTMCResponse(dbOwnershipHistoryRequest.Id, request.User,"Did not make a proper Call", "Exception : " + ex, "Failed", (Guid)request.MessageId);

                            //Error to indicate that there was Problem with Integrating to the Enatis Web Service
                            _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | The Enatis Integration threw an error EX : " + ex.InnerException.ToString());
                            throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | The Enatis Integration threw an error Response : Exception :" + ex);
                        }

                    }else
                    {
                        //Error to indicate record have not been entered correctly in DB
                        _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to Request to DB : " + request.ToString());
                        throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Request to DB : " + request);                        
                    }

                }else{
                    //Error to indicate that the Request Object was not able to be Mapped
                    _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | Method : GetOwnershipHistoryAsync | Unable to Map Request Object : Object : " + request.ToString());
                    throw new DomainException("Error : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Map Request Object : Object : " + request);
                }

            }catch(Exception ex){

                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | An Exception occurred : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Integrate To ENatis : Exception : " + ex);
                //Throw Error that XML was not able to be Converted to Class
                throw new DomainException("An Exception occurred : Service : RTMC | Method : GetOwnershipHistoryAsync | Unable to Integrate To ENatis : Exception" + ex);
            }

        }

        #endregion

        #region private methods

        /// <summary>
        /// Force Request Object to UpperCase
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        private dynamic FormatInputRequest<T>(T request) where T : OwnershipHistoryAbstractValidatableObject{

            if(request is OwnershipHistoryDataRequest ownershipHistoryDataRequest)
            {
                if(!String.IsNullOrEmpty(ownershipHistoryDataRequest.LicenceNumber)){

                    //Remove Special Characters
                    ownershipHistoryDataRequest.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryDataRequest.LicenceNumber);
                    //UpperCase
                    ownershipHistoryDataRequest.LicenceNumber = ownershipHistoryDataRequest.LicenceNumber.ToUpper();
                }

                if(!String.IsNullOrEmpty(ownershipHistoryDataRequest.RegisterNumber)){

                    //Remove Special Characters
                    ownershipHistoryDataRequest.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryDataRequest.RegisterNumber);
                    //UpperCase
                    ownershipHistoryDataRequest.RegisterNumber = ownershipHistoryDataRequest.RegisterNumber.ToUpper();
                }

                if(!String.IsNullOrEmpty(ownershipHistoryDataRequest.VinOrChassis)){

                    //Remove Special Characters
                    ownershipHistoryDataRequest.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryDataRequest.VinOrChassis);
                    //UpperCase
                    ownershipHistoryDataRequest.VinOrChassis = ownershipHistoryDataRequest.VinOrChassis.ToUpper();
                }
            }

            if(request is OwnershipHistoryVerificationRequest ownershipHistoryVerificationRequest)
            {
                if(!String.IsNullOrEmpty(ownershipHistoryVerificationRequest.LicenceNumber)){

                    //Remove Special Characters
                    ownershipHistoryVerificationRequest.LicenceNumber = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryVerificationRequest.LicenceNumber);
                    //UpperCase
                    ownershipHistoryVerificationRequest.LicenceNumber = ownershipHistoryVerificationRequest.LicenceNumber.ToUpper();
                }

                if(!String.IsNullOrEmpty(ownershipHistoryVerificationRequest.RegisterNumber)){

                    //Remove Special Characters
                    ownershipHistoryVerificationRequest.RegisterNumber = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryVerificationRequest.RegisterNumber);
                    //UpperCase
                    ownershipHistoryVerificationRequest.RegisterNumber = ownershipHistoryVerificationRequest.RegisterNumber.ToUpper();
                }

                if(!String.IsNullOrEmpty(ownershipHistoryVerificationRequest.VinOrChassis)){

                    //Remove Special Characters
                    ownershipHistoryVerificationRequest.VinOrChassis = HelperExtensions.RemoveSpecialCharacters(ownershipHistoryVerificationRequest.VinOrChassis);
                    //UpperCase
                    ownershipHistoryVerificationRequest.VinOrChassis = ownershipHistoryVerificationRequest.VinOrChassis.ToUpper();
                }
            }

            
            return request;
        }

        /// <summary>
        /// Convert Request Object to DBRequest
        /// </summary>
        /// <param name="request"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCRequest MapRequestObjectToDB<T>(T request) where T : OwnershipHistoryAbstractValidatableObject{


            // OwnershipHistoryDataRequest request)                

            RTMCRequest dbOwnershipHistoryRequest = new RTMCRequest();
            try
            {
                dbOwnershipHistoryRequest = _mapper.Map<RTMCRequest>(request);
                dbOwnershipHistoryRequest.RequestObject = request.ToString();
                dbOwnershipHistoryRequest.IntegrationType = "GetOwnershipHistory";
                dbOwnershipHistoryRequest.Date = DateTime.Now;
                dbOwnershipHistoryRequest.CreatedOn = DateTime.Now;
                dbOwnershipHistoryRequest.Reference = (Guid)request.MessageId;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to Vehicle Response
                throw new DomainException("An Exception occurred when mapping MapRequestObjectToDB REST Request string to Class : Exception = " + ex);
            }

            return dbOwnershipHistoryRequest;
        }

        /// <summary>
        /// Convert Response Object to RTMCOwnershipHistoryResponse
        /// </summary>
        /// <param name="ownershipHistoryResponse"></param>
        /// <param name="natisOwnershipHistory"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentNullException"></exception>
        private RTMCOwnershipHistoryDetail MapResponseObjectToOwnershipHistoryDetailDB(OwnershipHistoryResponse ownershipHistoryResponse, OwnershipHistoryData natisOwnershipHistory, Guid? messageId){

            RTMCOwnershipHistoryDetail dbOwnershipHistoryDetail = new RTMCOwnershipHistoryDetail();
            try
            {
                dbOwnershipHistoryDetail = _mapper.Map<RTMCOwnershipHistoryDetail>(ownershipHistoryResponse);
                dbOwnershipHistoryDetail.LicenseNumber = natisOwnershipHistory.data.vehicle.licenseNumber;
                dbOwnershipHistoryDetail.VinOrChassis = natisOwnershipHistory.data.vehicle.vinOrChassis;
                dbOwnershipHistoryDetail.RegisterNumber = natisOwnershipHistory.data.vehicle.registerNumber;
                dbOwnershipHistoryDetail.RequestId = (Guid)messageId;

                dbOwnershipHistoryDetail.CreatedOn = DateTime.Now;
            }catch(Exception ex)
            {
                _logger.LogError(_serverEnvironmentOptions + " | RTMCOwnershipHistoryService | An Exception occurred when mapping MapResponseObjectToOwnershipHistoryDetailDB OwnershipHistoryResponse to Class : Exception = " + ex);
                //Throw Error that XML was not able to be mapped to OwnershipHistory Response
                throw new DomainException("An Exception occurred when mapping MapResponseObjectToOwnershipHistoryDetailDB OwnershipHistoryResponse to Class : Exception = " + ex);
            }

            return dbOwnershipHistoryDetail;
        }

        /// <summary>
        /// Calculate Current Ownership and Title Holder
        /// </summary>
        /// <param name="ownershipHistoryDataResponse"></param>
        /// <param name="identifierNumber"></param>
        /// <param name="result"></param>
        /// <returns></returns>
        private OwnershipHistoryVerificationResponse CalculateOwnershipTitleholderLogic(OwnershipHistoryDataResponse ownershipHistoryDataResponse, string identifierNumber, OwnershipHistoryVerificationResponse result)
        {

            //Calculation if the Entity ever where the Owner of the Vehicle
            result.HasEverOwned = ownershipHistoryDataResponse.ownershipHistory.Any(x => x.owner.identificationNumber == identifierNumber && x.owner.ownershipType == "OWNER");

            //Calculation if the Entity is the current Owner of the Vehicle
            result.IsCurrentOwner = ownershipHistoryDataResponse.ownershipHistory.Any(x => x.owner.identificationNumber == identifierNumber && x.owner.ownershipStatus == "CURRENT" && x.owner.ownershipType == "OWNER");

            //Calculation if the Entity ever where the Title Holder of the Vehicle
            result.HasEverHeldTitle = ownershipHistoryDataResponse.ownershipHistory.Any(x => x.owner.identificationNumber == identifierNumber && x.owner.ownershipType == "TITLE_HOLDER");

            //Calculation if the Entity are the current Title Holder of the Vehicle
            result.IsCurrentTitleHolder = ownershipHistoryDataResponse.ownershipHistory.Any(x => x.owner.identificationNumber == identifierNumber && x.owner.ownershipStatus == "CURRENT" && x.owner.ownershipType == "TITLE_HOLDER");

            return result;
        }

        #endregion

    }

}