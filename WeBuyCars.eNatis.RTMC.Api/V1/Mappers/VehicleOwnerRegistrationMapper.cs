using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.Registration;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleOwnerRegistration;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class VehicleOwnerRegistrationMapper : Profile
    {

        public VehicleOwnerRegistrationMapper()
        {
            CreateMap<RTMCRequest, VehicleOwnerRegistrationRequest>().ReverseMap();
            CreateMap<RTMCResponse, VehicleOwnerRegistrationResponse>().ReverseMap();
        
            CreateMap<VehicleOwnerRegistrationRequest, NatisVehicleOwnerRegistrationRequest>()
            .ForMember(dest => dest.OwnerDocumentTypeCode, opt => opt.MapFrom(src => src.GetOwner().DocumentTypeCode))
            .ForMember(dest => dest.OwnerDocumentNumber, opt => opt.MapFrom(src => src.GetOwner().DocumentNumber))
            .ForMember(dest => dest.ProxyDocumentTypeCode, opt => opt.MapFrom(src => src.GetOwnerProxy().DocumentTypeCode))
            .ForMember(dest => dest.ProxyDocumentNumber, opt => opt.MapFrom(src => src.GetOwnerProxy().DocumentNumber))
            .ForMember(dest => dest.RepresentativeDocumentTypeCode, opt => opt.MapFrom(src => src.GetOwnerRepresentative().DocumentTypeCode))
            .ForMember(dest => dest.RepresentativeDocumentNumber, opt => opt.MapFrom(src => src.GetOwnerRepresentative().DocumentNumber))
            .ForMember(dest => dest.RegistrationLiabilityDate,opt => opt.MapFrom(src => src.RegistrationLiabilityDate.ToString("yyyy-MM-dd")))
            ;

            CreateMap<RegistrationArchivedRequestByReferenceResponse, RTMCVehicleOwnerRegistrationDetail>().ReverseMap();    

            CreateMap<BaseResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.BaseResponse>().ReverseMap();
            CreateMap<VehicleOwnerRegistrationInformation, VehicleOwnerRegistrationResponse>().ReverseMap();
            CreateMap<RTMCVehicleOwnerRegistrationDetail, VehicleOwnerRegistrationResponse>().ReverseMap();

        }

    }
}