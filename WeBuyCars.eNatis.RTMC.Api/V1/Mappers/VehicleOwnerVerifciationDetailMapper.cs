using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetVehicleOwnerVerification;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class VehicleOwnerVerifciationDetailMapper : Profile
    {

        public VehicleOwnerVerifciationDetailMapper()
        {
            CreateMap<RTMCRequest, VehicleOwnerVerificationRequest>().ReverseMap();
            CreateMap<RTMCResponse, VehicleOwnerVerificationResponse>().ReverseMap();
            CreateMap<RTMCVehicleOwnerVerificationDetail, VehicleOwnerVerificationResponse>().ReverseMap();

            CreateMap<VehicleOwnerVerificationRequest, NatisGetVehicleOwnerVerificationRequest>()
                .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => ((int)src.DocumentTypeCode).ToString().PadLeft(2,'0')))
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocumentNumber))
                .ForMember(dest => dest.Vin, opt => opt.MapFrom(src => src.Vin))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.RegisterNumber))
                .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.LicenceNumber));

            CreateMap<NatisGetVehicleOwnerVerificationRequest, VehicleOwnerVerificationRequest>()
                .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => src.DocumentTypeCode))
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocumentNumber))
                .ForMember(dest => dest.Vin, opt => opt.MapFrom(src => src.Vin))
                .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.RegisterNumber))
                .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.LicenceNumber));

            CreateMap<BaseResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.BaseResponse>().ReverseMap();
            CreateMap<VehicleOwnerVerificationResponse, VehicleOwnerVerification>().ReverseMap();

        }

    }
}