using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.CalculateVehicleLicenceFee;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.ControlNumber;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisCalculateVehicleLicenceFeeRequest;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class LicenceFeeCalculationMapper : Profile
    {

        public LicenceFeeCalculationMapper()
        {
            CreateMap<RTMCRequest, FeeCalculationRequest>().ReverseMap();

            CreateMap<Vehicle, VehicleIdentification>()
            .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.registerNumber))
            .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.vinOrChassis))
            .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.licenseNumber))
            .ReverseMap();

            CreateMap<Owner, EntityIdentification>()
            .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => src.idDocumentTypeCode))
            .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.idDocumentNumber))
            .ReverseMap();

            CreateMap<NatisCalculateVehicleLicenceFeeRequest, FeeCalculationRequest>()
            .ForMember(dest => dest.MessageId, opt => opt.MapFrom(src => src.data.messageId))
            .ForMember(dest => dest.Owner, opt => opt.MapFrom(src => src.data.owner))
            .ForMember(dest => dest.Vehicle, opt => opt.MapFrom(src => src.data.vehicle))
            .ForMember(dest => dest.EffectiveDate, opt => opt.MapFrom(src => src.data.effectiveDate))
            .ReverseMap();

            CreateMap<CalculateVehicleLicenceFeeData, FeeCalculationResponse>()
            .ForMember(dest => dest.BaseResponse, opt => opt.MapFrom(src => src.BaseResponse))
            .ForMember(dest => dest.arrears, opt => opt.MapFrom(src => src.data.arrears))
            .ForMember(dest => dest.existingDebt, opt => opt.MapFrom(src => src.data.existingDebt))
            .ForMember(dest => dest.fees, opt => opt.MapFrom(src => src.data.fees))
            .ForMember(dest => dest.penalties, opt => opt.MapFrom(src => src.data.penalties))
            .ForMember(dest => dest.totalAmountDue, opt => opt.MapFrom(src => src.data.totalAmountDue))
            .ForMember(dest => dest.transactionFee, opt => opt.MapFrom(src => src.data.transactionFee))
            .ForMember(dest => dest.vehicle, opt => opt.MapFrom(src => src.data.vehicle))
            .ReverseMap();

            CreateMap<RTMCLoginDetailsResponse, RTMCSettings>().ReverseMap();
            CreateMap<RTMCLoginDetailsListResponse, RTMCSettings>().ReverseMap();         

        }

    }
}