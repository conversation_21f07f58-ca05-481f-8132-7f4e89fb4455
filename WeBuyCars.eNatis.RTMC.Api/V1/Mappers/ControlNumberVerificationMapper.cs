using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationResponse;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.ControlNumber;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisControlNumberVerificationRequest;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class ControlNumberVerificationMapper : Profile
    {

        public ControlNumberVerificationMapper()
        {
            CreateMap<RTMCRequest, ControlNumberVerificationRequest>().ReverseMap();

            CreateMap<Vehicle, VehicleIdentification>()
            .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.registerNumber))
            .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.vinOrChassis))
            .ForMember(dest => dest.ControlNumber, opt => opt.MapFrom(src => src.controlNumber))
            .ReverseMap();

            CreateMap<Owner, EntityIdentification>()
            .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => src.idDocumentTypeCode))
            .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.idDocumentNumber))
            .ReverseMap();

            CreateMap<TitleHolder, EntityIdentification>()
            .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => src.idDocumentTypeCode))
            .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.idDocumentNumber))
            .ReverseMap();

            CreateMap<NatisControlNumberVerificationRequest, ControlNumberVerificationRequest>()
            .ForMember(dest => dest.MessageId, opt => opt.MapFrom(src => src.data.messageId))
            .ForMember(dest => dest.TitleHolder, opt => opt.MapFrom(src => src.data.titleHolder))
            .ForMember(dest => dest.Owner, opt => opt.MapFrom(src => src.data.owner))
            .ForMember(dest => dest.Vehicle, opt => opt.MapFrom(src => src.data.vehicle))
            .ReverseMap();

            CreateMap<ControlNumberVerificationData, ControlNumberVerificationResponse>()
            .ForMember(dest => dest.ControlNumber, opt => opt.MapFrom(src => src.data.certificateNumber))
            .ForMember(dest => dest.Owner, opt => opt.MapFrom(src => src.data.owner))
            .ForMember(dest => dest.TitleHolder, opt => opt.MapFrom(src => src.data.titleHolder))
            .ReverseMap();

            CreateMap<RTMCLoginDetailsResponse, RTMCSettings>().ReverseMap();
            CreateMap<RTMCLoginDetailsListResponse, RTMCSettings>().ReverseMap();         

        }

    }
}