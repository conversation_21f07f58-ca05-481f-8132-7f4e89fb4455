using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.NCO;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OnlineNCO;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{

    public class VehicleOwnerOnlineNCOMapper : Profile
    {

        public VehicleOwnerOnlineNCOMapper()
        {
            CreateMap<RTMCRequest, VehicleOwnerOnlineNCORequest>().ReverseMap();
            CreateMap<RTMCResponse, VehicleOwnerOnlineNCOResponse>().ReverseMap();

            CreateMap<VehicleOwnerOnlineNCORequest, NatisOnlineNCORequest>()
            .ForMember(dest => dest.ReceiverDocumentTypeCode, opt => opt.MapFrom(src => src.Receiver.DocumentTypeCode))
            .ForMember(dest => dest.ReceiverDocumentNumber, opt => opt.MapFrom(src => src.Receiver.DocumentNumber))
            .ForMember(dest => dest.ReceiverProxyDocumentTypeCode, opt => opt.MapFrom(src => src.ReceiverProxy.DocumentTypeCode))
            .ForMember(dest => dest.ReceiverProxyDocumentNumber, opt => opt.MapFrom(src => src.ReceiverProxy.DocumentNumber))
            .ForMember(dest => dest.ReceiverRepresentativeDocumentTypeCode, opt => opt.MapFrom(src => src.ReceiverRepresentative.DocumentTypeCode))
            .ForMember(dest => dest.ReceiverRepresentativeDocumentNumber, opt => opt.MapFrom(src => src.ReceiverRepresentative.DocumentNumber))
            .ForMember(dest => dest.ChangeDate,opt => opt.MapFrom(src => src.ChangeDate.ToString("yyyy-MM-dd")))
            ;

            CreateMap<BaseResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.BaseResponse>().ReverseMap();

            CreateMap<OnlineNCOInformation, VehicleOwnerOnlineNCOResponse>()
            .ForMember(dest => dest.FirstLicenceLiabiltyDate, opt => opt.MapFrom(src => src.FirstLicenceLiabilityDate))
            .ForMember(dest => dest.FirstRegistrationLiabiltyDate, opt => opt.MapFrom(src => src.FirstRegistrationLiabilityDate))
            .ForMember(dest => dest.UserGroupCode, opt => opt.MapFrom(src => src.UserGroupCode))
            .ForMember(dest => dest.DateTime, opt => opt.MapFrom(src => src.DateTime))

            .ReverseMap();


            CreateMap<RTMCVehicleOwnerOnlineNCODetail, VehicleOwnerOnlineNCOResponse>()
            .ForMember(dest => dest.FirstLicenceLiabiltyDate, opt => opt.MapFrom(src => src.FirstLicenceLiabilityDate))
            .ForMember(dest => dest.FirstRegistrationLiabiltyDate, opt => opt.MapFrom(src => src.FirstRegistrationLiabilityDate))
            .ForMember(dest => dest.UserGroupCode, opt => opt.MapFrom(src => src.UserGroupCode))
            .ForMember(dest => dest.DateTime, opt => opt.MapFrom(src => src.DateTime))
            .ReverseMap();

            CreateMap<Infrastructure.Natis.Models.OnlineNCO.Envelope,  WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.BaseResponse>()
            .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X314AResponse.messages.code))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X314AResponse.transactionStatus))
            .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X314AResponse.messages.message))
            .ReverseMap();

            CreateMap<RTMCVehicleOwnerOnlineNCODetail, NCOArchivedRequestByReferenceResponse>()
            .ForMember(dest => dest.DateOfLiabilityForRegistration, opt => opt.MapFrom(src => src.DateOfLiabilityForRegistration.ToString("yyyy-MM-dd")))
            .ReverseMap();

        }

    }
}