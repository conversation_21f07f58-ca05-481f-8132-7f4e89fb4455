using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.CompleteVehicleLicenceRenewal;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.GetVehiclesAndLicenseExpiryDates;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.RenewVehicleLicense;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.VehiclesLicenceRenewalQuotation;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.CompleteRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesDueForRenewal;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.GetVehiclesQuotation;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.InitiateRenewal;
using Vehicle = WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.VehicleLicenseRenewal.Vehicle;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers;

public class VehicleLicenseRenewalMapper : Profile
{
    public VehicleLicenseRenewalMapper()
    {
        // Get Vehicles and License Expiry Dates
        CreateMap<RTMCRequest, GetVehiclesAndLicenseExpiryDatesRequest>().ReverseMap();
        
        CreateMap<GetVehiclesAndLicenseExpiryDatesRequest, NatisGetVehiclesAndLicenseExpiryDatesRequest>()
            .ForMember(dest => dest.IdentificationType, opt => opt.MapFrom(src => src.IdentificationTypeCode))
            .ForMember(dest => dest.IdentificationNumber, opt => opt.MapFrom(src => src.IdentificationNumber));

        CreateMap<Vehicle, GetVehiclesAndLicenseExpiryDatesResponse>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src =>
                new List<Models.VehicleLicenseRenewal.Shared.Vehicle>
                {
                new()
                {
                    VehicleRegisterNumber = src.VehicleRegisterNumber,
                    LicenceNumber = src.LicenceNumber,
                    EngineNumber = src.EngineNumber,
                    VinOrChassisNumber = src.VinOrChassis,
                    LicenseExpiryDate = src.MvLicExpryD,
                    Roadworthy = src.Roadworthy,
                    Make = src.MvMake,
                    Model = src.MvModel,
                    SeriesName = src.MvSeries,
                    Colour = src.MvColour,
                    Tare = src.Tare
                }
            }));

        CreateMap<IEnumerable<Vehicle>, List<GetVehiclesAndLicenseExpiryDatesResponse>>()
            .ConvertUsing((src, dest, context) => src.Select(item => context.Mapper.Map<GetVehiclesAndLicenseExpiryDatesResponse>(item)).ToList());

        
        // Vehicle License Renewal Quotation
        CreateMap<RTMCRequest, VehiclesLicenceRenewalQuotationRequest>().ReverseMap();
        
        CreateMap<VehiclesLicenceRenewalQuotationRequest, NatisGetVehiclesQuotationRequest>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.Vehicles));

        CreateMap<VehiclesQuotationData, RenewVehicleLicenseResponse>();
        
            
        // Initiate Vehicle License Renewal
        CreateMap<RTMCRequest, RenewVehicleLicenseRequest>().ReverseMap();

        CreateMap<RenewVehicleLicenseRequest, NatisInitiateRenewalRequest>()
            .ForMember(dest => dest.Owner, opt => opt.MapFrom(src => src.Owner))
            .ForMember(dest => dest.RenewalInformation, opt => opt.MapFrom(src => src.RenewalInformation));
    
        CreateMap<VehiclesLicenceRenewalQuotationRequest, NatisGetVehiclesQuotationRequest>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.Vehicles));
        
        
        // Complete Vehicle License Renewal
        CreateMap<RTMCRequest, CompleteVehicleLicenseRenewalRequest>().ReverseMap();

        CreateMap<CompleteVehicleLicenseRenewalRequest, NatisCompleteRenewalRequest>();
        
        CreateMap<CompleteRenewalData, CompleteVehicleLicenseRenewalResponse>()
            .ForMember(dest => dest.Vehicles, opt => opt.MapFrom(src => src.data.Vehicles));
    }
}

