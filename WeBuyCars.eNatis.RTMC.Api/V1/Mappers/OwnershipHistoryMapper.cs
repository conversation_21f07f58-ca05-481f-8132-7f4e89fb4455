using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.NatisOwnershipHistoryRequest;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class OwnershipHistoryMapper : Profile
    {

        public OwnershipHistoryMapper()
        {
            CreateMap<RTMCRequest, OwnershipHistoryDataRequest>().ReverseMap();
            CreateMap<NatisOwnershipHistoryRequest, OwnershipHistoryDataRequest>()
            .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.data.vehicle.licenseNumber))
            .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.data.vehicle.registerNumber))
            .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.data.vehicle.vinOrChassis))
            .ForMember(dest => dest.MessageId, opt => opt.MapFrom(src => src.data.messageId))
            .ReverseMap();

            CreateMap<RTMCRequest, OwnershipHistoryVerificationRequest>().ReverseMap();
            CreateMap<NatisOwnershipHistoryRequest, OwnershipHistoryVerificationRequest>()
            .ForMember(dest => dest.LicenceNumber, opt => opt.MapFrom(src => src.data.vehicle.licenseNumber))
            .ForMember(dest => dest.RegisterNumber, opt => opt.MapFrom(src => src.data.vehicle.registerNumber))
            .ForMember(dest => dest.VinOrChassis, opt => opt.MapFrom(src => src.data.vehicle.vinOrChassis))
            .ForMember(dest => dest.MessageId, opt => opt.MapFrom(src => src.data.messageId))
            .ReverseMap();

            CreateMap<VehicleResponse, Infrastructure.Natis.Models.OwnershipHistory.Vehicle>().ReverseMap()
            .ForMember(dest => dest.licenceNumber, opt => opt.MapFrom(src => src.licenseNumber))
            ;

            CreateMap<OwnershipHistoryData,OwnershipHistoryDataResponse>()
            .ForMember(dest => dest.vehicle, opt => opt.MapFrom(src => src.data.vehicle))
            .ForMember(dest => dest.ownershipHistory, opt => opt.MapFrom(src => src.data.ownershipHistory))
            .ReverseMap();

            CreateMap<OwnershipHistoryResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory.Ownershiphistory>().ReverseMap();
            CreateMap<OwnerResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.OwnershipHistory.Owner>().ReverseMap();


            CreateMap<RTMCOwnershipHistoryDetail, OwnerResponse>()
            .ForMember(dest => dest.name, opt => opt.MapFrom(src => src.EntityName))
            .ForMember(dest => dest.identificationNumber, opt => opt.MapFrom(src => src.OwnershipStatus))
            .ForMember(dest => dest.ownershipStatus, opt => opt.MapFrom(src => src.OwnershipStatus))
            .ForMember(dest => dest.insuranceCompany, opt => opt.MapFrom(src => src.InsuranceCompany))
            .ForMember(dest => dest.ownershipType, opt => opt.MapFrom(src => src.OwnershipType))
            .ForMember(dest => dest.ownershipDate, opt => opt.MapFrom(src => src.OwnershipDate))
            .ReverseMap();

            CreateMap<RTMCOwnershipHistoryDetail, VehicleResponse>()
            .ForMember(dest => dest.vinOrChassis, opt => opt.MapFrom(src => src.VinOrChassis))
            .ForMember(dest => dest.licenceNumber, opt => opt.MapFrom(src => src.LicenseNumber))
            .ForMember(dest => dest.registerNumber, opt => opt.MapFrom(src => src.RegisterNumber))
            .ReverseMap();

            CreateMap<RTMCOwnershipHistoryDetail, OwnerResponse>()
            .ReverseMap();

            CreateMap<RTMCLoginDetailsResponse, RTMCSettings>().ReverseMap();
            CreateMap<RTMCLoginDetailsListResponse, RTMCSettings>().ReverseMap();

            CreateMap<OwnershipHistoryResponse, RTMCOwnershipHistoryDetail>()
            .ForMember(dest => dest.IdentificationNumber, opt => opt.MapFrom(src => src.owner.identificationNumber))
            .ForMember(dest => dest.InsuranceCompany, opt => opt.MapFrom(src => src.owner.insuranceCompany))
            .ForMember(dest => dest.EntityName, opt => opt.MapFrom(src => src.owner.name))
            .ForMember(dest => dest.OwnershipType, opt => opt.MapFrom(src => src.owner.ownershipType))
            .ForMember(dest => dest.InsuranceCompany, opt => opt.MapFrom(src => src.owner.insuranceCompany))
            .ForMember(dest => dest.OwnershipDate, opt => opt.MapFrom(src => src.owner.ownershipDate))
            .ForMember(dest => dest.OwnershipStatus, opt => opt.MapFrom(src => src.owner.ownershipStatus))
            .ReverseMap();

            CreateMap<RTMCOwnershipHistoryDetail, VehicleOwnershipInformation>().ReverseMap();

        }

    }
}