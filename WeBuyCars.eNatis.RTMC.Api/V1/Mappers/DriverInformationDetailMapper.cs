using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.GetDriverInformation;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class DriverInformationDetailMapper : Profile
    {

        public DriverInformationDetailMapper()
        {
            CreateMap<RTMCRequest, DriverInformationRequest>().ReverseMap();
            CreateMap<RTMCResponse, DriverInformationResponse>().ReverseMap();
            CreateMap<RTMCDriverInformationDetail, DriverInformationResponse>().ReverseMap();

            CreateMap<DriverInformationRequest, NatisGetDriverInformationRequest>()
                .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => ((int)src.DocumentTypeCode).ToString().PadLeft(2,'0')))
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocumentNumber));

            CreateMap<NatisGetDriverInformationRequest, DriverInformationRequest>()
                .ForMember(dest => dest.DocumentTypeCode, opt => opt.MapFrom(src => src.DocumentTypeCode))
                .ForMember(dest => dest.DocumentNumber, opt => opt.MapFrom(src => src.DocumentNumber));

            CreateMap<BaseResponse, WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Models.Shared.BaseResponse>().ReverseMap();

            CreateMap<DriverInformationResponse, DriverInformation>().ReverseMap();

            CreateMap<Infrastructure.Natis.Models.GetDriverInformation.Envelope, BaseResponse>()
                .ForMember(dest => dest.StatusCode, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.errorMessages.code))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.successful))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Body.X3042Response.executionResult.errorMessages.message))
                .ReverseMap();
        }

    }
}