using AutoMapper;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Mappers
{


    public class SettingsMapper : Profile
    {

        public SettingsMapper()
        {
            CreateMap<RTMCSettings, RTMCLoginDetails>().ReverseMap();      
            CreateMap<RTMCLoginDetailsResponse, RTMCSettings>().ReverseMap();

            CreateMap<RTMCLoginDetailsListResponse, RTMCSettings>().ReverseMap();
            

        }

    }
}