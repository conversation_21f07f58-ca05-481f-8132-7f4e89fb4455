using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class VehicleOwnerVerificationRequest : AbstractValidatableObject
    {

        [Required]
        public DocumentTypeEnum DocumentTypeCode { get; set; }

        [Required]
        public string DocumentNumber { get; set; }

        [Required]
        public string Vin { get; set; }
        public string RegisterNumber { get; set; }
        public string LicenceNumber { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext, CancellationToken cancellation)
        {

            var errors = new List<ValidationResult>();

            if(DocumentNumber.Length > 13)
            {
                errors.Add(new ValidationResult("Document Number cannot be more than 13 digits", new[] { nameof(DocumentNumber) }));
            }

            if(String.IsNullOrWhiteSpace(RegisterNumber) && String.IsNullOrWhiteSpace(LicenceNumber) && String.IsNullOrWhiteSpace(Vin))
            {
                errors.Add(new ValidationResult("Atleast two identifiers need to be provided", new[] { nameof(RegisterNumber), nameof(LicenceNumber), nameof(Vin) }));
            }

            if(!TwoOfThreeCheck(Vin, RegisterNumber, LicenceNumber)){
                errors.Add(new ValidationResult("Atleast two identifiers need to be provided", new[] { nameof(RegisterNumber), nameof(LicenceNumber), nameof(Vin) }));
            }


            if(!String.IsNullOrWhiteSpace(RegisterNumber))
            {
                if(RegisterNumber.Length > 7)
                {
                    errors.Add(new ValidationResult("Register Number length cannot exceed 7 digits", new[] { nameof(RegisterNumber) }));
                }

                if(!Regex.IsMatch(RegisterNumber,@"[a-zA-Z]{3}\d{3}[a-zA-Z]"))
                {
                    errors.Add(new ValidationResult("Register Number can only contain Numbers and Letters and must at least contain 3 digits of each", new[] { nameof(RegisterNumber) }));
                }
            }

            if(!String.IsNullOrWhiteSpace(Vin))
            {
                if(!Regex.IsMatch(Vin,@"[A-Z0-9]{1,17}"))
                {
                    errors.Add(new ValidationResult("Vin Number can only contain Numbers and Letters and more than 17 Digits", new[] { nameof(RegisterNumber) }));
                }
            }

            return errors;

        }

        public bool TwoOfThreeCheck(string Vin, string RegisterNumber, string LicenceNumber)
        {

            bool result = false;

            int count = 0;

            if(!String.IsNullOrWhiteSpace(Vin)){
                count = count + 1;
            }

            if(!String.IsNullOrWhiteSpace(RegisterNumber)){
                count = count + 1;
            }

            if(!String.IsNullOrWhiteSpace(LicenceNumber)){
                count = count + 1;
            }

            if(count >= 2){
                result = true;
            }

            return result;

        }
        
    }
}