using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using static WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared.SharedServices;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class VehicleOwnerOnlineNCORequest : AbstractValidatableObject
    {

        //Business Registration Number
        [Required]
        public string BusinessRegistrationNumber { get; set; }

        //Driver Information
        [Required]
        public EntityIdentification Receiver { get; set; }        
        public EntityIdentification ReceiverProxy { get; set; }
        public EntityIdentification ReceiverRepresentative { get; set; }

        [Required]
        public string ControlNumber { get; set; }

        //Vehicle Information
        public string RegisterNumber { get; set; }        
        public string VinOrChassis { get; set; }
        public string LicenceNumber { get; set; }

        [Required]
        [JsonConverter(typeof(DateFormatConverter), "yyyy-MM-dd")]
        public System.DateTime ChangeDate { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(BusinessRegistrationNumber))
            {
                errors.Add(new ValidationResult("Business Registration Number is required", new[] { nameof(BusinessRegistrationNumber) }));            
            } 

            if(String.IsNullOrWhiteSpace(RegisterNumber) && String.IsNullOrWhiteSpace(VinOrChassis) && String.IsNullOrWhiteSpace(LicenceNumber))
            {
                errors.Add(new ValidationResult("Atleast a Vin, Register Number or Licence Number needs to be provided", new[] { nameof(RegisterNumber), nameof(VinOrChassis), nameof(LicenceNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(RegisterNumber))
            {
                if(RegisterNumber.Length > 7)
                {
                    errors.Add(new ValidationResult("Register Number cannot be more than 7 digits", new[] { nameof(RegisterNumber) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(VinOrChassis))
            {
                if(VinOrChassis.Length > 17)
                {
                    errors.Add(new ValidationResult("Vin or Chassis Number cannot be more than 17 digits", new[] { nameof(VinOrChassis) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(ControlNumber))
            {
                if(ControlNumber.Length > 12)
                {
                    errors.Add(new ValidationResult("Control Number cannot exceed 12 digits", new[] { nameof(ControlNumber) }));
                }                
            }

            return errors;
        }
        
    }
}