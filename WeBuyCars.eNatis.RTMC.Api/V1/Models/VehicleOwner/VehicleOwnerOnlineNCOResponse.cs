using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class VehicleOwnerOnlineNCOResponse
    {

        public VehicleOwnerOnlineNCOResponse(){
            // BaseResponse BaseResponse = new BaseResponse();
        }

        public string RegAuthority { get; set; }
        public string RegisterNumber { get; set; }
        public string VinOrChassis { get; set; }
        public string EngineNumber { get; set; }
        public string Make { get; set; }
        public string ModelName { get; set; }
        public string VehicleCategory { get; set; }
        public string Driven { get; set; }
        public string VehicleDescription { get; set; }
        public string Tare { get; set; }
        public string FirstLicenceLiabiltyDate { get; set; }
        public string VehicleLifeStatus { get; set; }
        public string SellerIdDocumentType { get; set; }
        public string SellerIdDocumentNumber { get; set; }
        public string SellerCountryOfIssue { get; set; }
        public string SellerName { get; set; }
        public string PurchaserIdDocumentType { get; set; }
        public string PurchaserName { get; set; }
        public string PurchaserIdDocumentNumber { get; set; }
        public string PurchaserCountryOfIssue { get; set; }
        public string FirstRegistrationLiabiltyDate { get; set; }
        public string IssueDate { get; set; }
        public string IssuedBy { get; set; }
        public byte[] EncryptedVehicleCertificateNumber { get; set; }
        public byte[] EncryptedBarcode { get; set; }
        public string UserGroupCode { get; set; }
        public string DateTime { get; set; }
        public string Watermark { get; set; }
        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }
}