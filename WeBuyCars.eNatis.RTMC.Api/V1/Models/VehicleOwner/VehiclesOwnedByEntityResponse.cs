using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class VehiclesOwnedByEntityResponse
    {

        public VehiclesOwnedByEntityResponse()
        {
            BaseResponse = new BaseResponse();
        }
        public VehicleOwnershipInformation[] vehicleOwnershipInformationList { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);

    }

    public class VehicleOwnershipInformation
    {
        public string VinOrChassis { get; set; }
        public string RegisterNumber { get; set; }
        public string LicenseNumber { get; set; }
        public string EntityName { get; set; }
        public string IdentificationNumber { get; set; }
        public string OwnershipStatus { get; set; }
        public string InsuranceCompany { get; set; }
        public string OwnershipType { get; set; }
        public string OwnershipDate { get; set; }    
    }

}