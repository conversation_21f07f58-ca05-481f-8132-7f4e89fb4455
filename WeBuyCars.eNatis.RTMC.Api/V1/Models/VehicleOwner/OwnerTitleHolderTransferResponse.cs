using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class OwnerTitleHolderTransferResponse
    {

        public OwnerTitleHolderTransferResponse(){
            // BaseResponse BaseResponse = new BaseResponse();
        }

        public string DrivingLicenceNumber { get; set; }
        public string LicenceCarddateOfFirstIssue { get; set; }
        public string LicenceCardcardIssueNumber { get; set; }
        public string LicenceCardvalidTo { get; set; }

        public string IdDocumentTypeCode { get; set; }
        public string IdDocumentTypeDescription { get; set; }

        public string IdDocumentNumber { get; set; }
        public string Initials { get; set; }
        public string BusinessOrSurname { get; set; }
        public string BirthDate { get; set; }
        public string Age { get; set; }
        public string NatureOfPersonCode { get; set; }
        public string NatureOfPersonDescription { get; set; }
        public string PopulationGroupCode { get; set; }
        public string PopulationGroupDescription { get; set; }
        public string LicenceRestrictionCode { get; set; }
        public string LicenceRestrictionDescription { get; set; }

        public string DrivingLicencedateOfFirstIssue { get; set; }
        public string DrivingLicenceTypeCode { get; set; }
        public string DrivingLicenceTypeDescription { get; set; }
        public string LicenceAuthorisationDate { get; set; }
        public string DrivingLicenceValidFrom { get; set; }
        public string VehicleRestrictionCode { get; set; }
        public string VehicleRestrictionDescription { get; set; }

        public string LearnerCertificateNumber { get; set; }
        public string LearnerLicenceTypeCode { get; set; }
        public string LearnerLicenceTypeDescription { get; set; }
        public string LearnerLicenceStatusCode { get; set; }
        public string LearnerLicenceStatusDescription { get; set; }
        public string LearnerLicenceValidFrom { get; set; }
        public string LearnerLicenceSpeciallyAdaptedVehicleRequired { get; set; }

        public string ProfessionalDateAuthorised { get; set; }
        public string ProfessionalDangerousGoodsCategoryCode { get; set; }
        public string ProfessionalDangerousGoodsCategoryDescription { get; set; }
        public string ProfessionalGoodsCategoryCode { get; set; }       
        public string ProfessionalGoodsCategoryDescription { get; set; }
        public string ProfessionalPassengerCategoryCode { get; set; }
        public string ProfessionalPassengerCategoryDescription { get; set; }
        public string ProfessionalSpareCategoryXCode { get; set; }
        public string ProfessionalSpareCategoryXDescription { get; set; }
        public string ProfessionalSpareCategoryYCode { get; set; }
        public string ProfessionalSpareCategoryYDescription { get; set; }
        public string ProfessionalvalidFromDate { get; set; }
        public string ProfessionalExpiryDate { get; set; }
        public string ProfessionalSuspendedFromDate { get; set; }
        public string ProfessionalSuspendedToDate { get; set; }


        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }
}