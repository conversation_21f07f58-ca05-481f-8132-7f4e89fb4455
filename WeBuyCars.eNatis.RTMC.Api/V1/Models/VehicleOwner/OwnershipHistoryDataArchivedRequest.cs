using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class OwnershipHistoryDataArchivedRequest : BaseAbstractValidatableObject
    {

        [Required]
        public Guid Reference { get; set; }
        
        [Required]
        public bool? IncludeSensitiveInformation { get; set; } = false;

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            return errors;
        }


    }
}