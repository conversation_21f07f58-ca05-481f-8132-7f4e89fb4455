using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using static WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared.SharedServices;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class VehicleOwnerRegistrationRequest : AbstractValidatableObject
    {

        //Business Registration Number
        [Required]
        public string BusinessRegistrationNumber { get; set; }

        //Driver Information
        //[Required]
        private EntityIdentification Owner { get; set; }        
        private EntityIdentification OwnerProxy { get; set; }
        private EntityIdentification OwnerRepresentative { get; set; }

        //Vehicle Information
        public string RegisterNumber { get; set; }        
        public string VinOrChassis { get; set; }
        public string LicenceNumber { get; set; }

        [Required]
        [JsonConverter(typeof(DateFormatConverter), "yyyy-MM-dd")]
        public System.DateTime RegistrationLiabilityDate { get; set; }

        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public NatureOfOwnershipEnum NatureOfOwnership { get; set; }

        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public VehicleUsageEnum VehicleUsage { get; set; }

        [Required]
        public string VehicleCertificateNumber { get; set; }

        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public RegistrationReasonEnum RegistrationReason { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(BusinessRegistrationNumber))
            {
                errors.Add(new ValidationResult("Business Registration Number is required", new[] { nameof(BusinessRegistrationNumber) }));            
            } 

            if(!String.IsNullOrWhiteSpace(RegisterNumber))
            {
                if(RegisterNumber.Length > 7)
                {
                    errors.Add(new ValidationResult("Register Number cannot be more than 7 digits", new[] { nameof(RegisterNumber) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(VinOrChassis))
            {
                if(VinOrChassis.Length > 17)
                {
                    errors.Add(new ValidationResult("Vin or Chassis Number cannot be more than 17 digits", new[] { nameof(VinOrChassis) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(LicenceNumber))
            {
                if(LicenceNumber.Length > 9)
                {
                    errors.Add(new ValidationResult("Licence Number cannot be more than 9 digits", new[] { nameof(LicenceNumber) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(VehicleCertificateNumber))
            {
                if(VehicleCertificateNumber.Length > 12)
                {
                    errors.Add(new ValidationResult("VehicleCertificate Number cannot exceed 12 digits", new[] { nameof(VehicleCertificateNumber) }));
                }                
            }

            return errors;
        }
        
        public EntityIdentification GetOwner(){

            return this.Owner;
        }

        public void SetOwner(EntityIdentification owner){

            this.Owner = owner;
        }

        public EntityIdentification GetOwnerProxy(){

            return this.OwnerProxy;
        }

        public void SetOwnerProxy(EntityIdentification ownerProxy){

            this.OwnerProxy = ownerProxy;
        }

        public EntityIdentification GetOwnerRepresentative(){

            return this.OwnerRepresentative;
        }

        public void SetOwnerRepresentative(EntityIdentification ownerRepresentative){

            this.OwnerRepresentative = ownerRepresentative;
        }

    }
}