using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations.Schema;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class OwnershipHistoryDataRequest : OwnershipHistoryAbstractValidatableObject
    {

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(RegisterNumber) && String.IsNullOrWhiteSpace(VinOrChassis) && String.IsNullOrWhiteSpace(LicenceNumber))
            {
                errors.Add(new ValidationResult("Atleast a Vin, Register Number or Licence Number needs to be provided", new[] { nameof(RegisterNumber), nameof(VinOrChassis), nameof(LicenceNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(RegisterNumber))
            {
                if(RegisterNumber.Length > 7)
                {
                    errors.Add(new ValidationResult("Register Number cannot be more than 7 digits", new[] { nameof(RegisterNumber) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(VinOrChassis))
            {
                if(VinOrChassis.Length > 17)
                {
                    errors.Add(new ValidationResult("Vin or Chassis Number cannot be more than 17 digits", new[] { nameof(RegisterNumber) }));
                }                
            }

            if(!String.IsNullOrWhiteSpace(LicenceNumber))
            {
                if(LicenceNumber.Length > 9)
                {
                    errors.Add(new ValidationResult("Vin or Chassis Number cannot be more than 9 digits", new[] { nameof(LicenceNumber) }));
                }                
            }

            if(String.IsNullOrWhiteSpace(BusinessRegistrationNumber))
            {
                errors.Add(new ValidationResult("Business Registration Number cannot be blank", new[] { nameof(LicenceNumber) })); 
            }
            return errors;
        }
    }
}