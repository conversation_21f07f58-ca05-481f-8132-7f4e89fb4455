using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class OwnershipHistoryVerificationResponse
    {

        public OwnershipHistoryVerificationResponse(){
             BaseResponse = new BaseResponse();             
        }
        public bool HasEverOwned { get; set; }
        public bool IsCurrentOwner { get; set; }
        public bool HasEverHeldTitle { get; set; }
        public bool IsCurrentTitleHolder { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }

}