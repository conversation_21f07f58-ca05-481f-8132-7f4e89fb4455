using System;
using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class OwnershipHistoryDataResponse
    {

        public OwnershipHistoryDataResponse(){
             BaseResponse = new BaseResponse();
             
        }

        public DateTimeOffset CreatedOn { get; set; }

        public VehicleResponse vehicle { get; set; }
        public OwnershipHistoryResponse[] ownershipHistory { get; set; }
        // public DataResponse data { get; set; }
        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }

    public class VehicleResponse
    {
        public string vinOrChassis { get; set; }
        public string registerNumber { get; set; }
        public string licenceNumber { get; set; }
    }

    public class OwnershipHistoryResponse
    {
        public OwnerResponse owner { get; set; }
    }

    public class OwnerResponse
    {
        public string name { get; set; }
        public string identificationNumber { get; set; }
        public string ownershipStatus { get; set; }
        public string insuranceCompany { get; set; }
        public string ownershipType { get; set; }
        public string ownershipDate { get; set; }
    }

}