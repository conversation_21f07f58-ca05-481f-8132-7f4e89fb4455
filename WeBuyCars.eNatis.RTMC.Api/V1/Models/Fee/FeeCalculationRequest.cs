using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class FeeCalculationRequest : AbstractValidatableObject
    {

          [Required]
          public DateTime EffectiveDate { get; set; }

          [Required]
          public EntityIdentification Owner { get; set; }    

          [Required]
          public VehicleIdentification Vehicle { get; set; }

          public string BusinessRegistrationNumber { get; set; } = "F151307720094";

          public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

          public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
          {

               var errors = new List<ValidationResult>();

               if(String.IsNullOrWhiteSpace(Vehicle.VinOrChassis) && String.IsNullOrWhiteSpace(Vehicle.RegisterNumber) && String.IsNullOrWhiteSpace(Vehicle.LicenceNumber))
               {
                    errors.Add(new ValidationResult("Atleast a Vin, Register Number or Licence Number needs to be provided", new[] { nameof(Vehicle.VinOrChassis), nameof(Vehicle.RegisterNumber), nameof(Vehicle.LicenceNumber) }));
               }


               if(!String.IsNullOrWhiteSpace(Vehicle.RegisterNumber))
               {
                    if(Vehicle.RegisterNumber.Length > 7)
                    {
                         errors.Add(new ValidationResult("RegisterNumber cannot be more than 7 digits", new[] { nameof(Vehicle.RegisterNumber) }));
                    }
               }

               if(!String.IsNullOrWhiteSpace(Vehicle.LicenceNumber))
               {
                    if(Vehicle.LicenceNumber.Length > 8)
                    {
                         errors.Add(new ValidationResult("LicenceNumber cannot be more than 8 digits", new[] { nameof(Vehicle.LicenceNumber) }));
                    }
               }

                    if(!String.IsNullOrWhiteSpace(Vehicle.VinOrChassis))
               {
                    if(Vehicle.VinOrChassis.Length > 17)
                    {
                         errors.Add(new ValidationResult("Vin or Chassis Number cannot be more than 17 digits", new[] { nameof(Vehicle.VinOrChassis) }));
                    }
               }


               return errors;
          }
    }
}