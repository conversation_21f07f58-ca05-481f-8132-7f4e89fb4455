using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class FeeCalculationResponse
    {

        public FeeCalculationResponse(){
             BaseResponse = new BaseResponse();             
        }
        public string vehicle { get; set; }
        public string fees { get; set; }
        public string transactionFee { get; set; }
        public string totalAmountDue { get; set; }
        public string existingDebt { get; set; }
        public string arrears { get; set; }
        public string penalties { get; set; }
        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
 
    }

}