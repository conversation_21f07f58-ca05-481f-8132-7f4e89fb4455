using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.Shared;

public class VehicleLicenceRenewalBaseRequest : AbstractValidatableObject
{
    [Required]
    [JsonConverter(typeof(StringEnumConverter))]
    public IdentificationTypeEnum IdentificationTypeCode { get; set; }
    
    [Required]
    public string IdentificationNumber { get; set; }
}