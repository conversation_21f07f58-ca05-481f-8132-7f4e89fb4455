using WeBuyCars.eNatis.RTMC.Core.Enumerations;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models.VehicleLicenseRenewal.RenewVehicleLicense;

public class RenewVehicleLicenseRequestAddress
{
    public class Address
    {
        public AddressTypeEnum AddressType { get; set; }
        public string Address1 { get; set; }
        public string? Address2 { get; set; }
        public string? Address3 { get; set; }
        public string Address4 { get; set; }
        public string Address5 { get; set; }
        public string PostalCode { get; set; }
    }
}