using System;
using Newtonsoft.Json;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models.Registration
{
    public sealed class RegistrationArchivedRequestByReferenceResponse : Entity
    {

        public RegistrationArchivedRequestByReferenceResponse()
        {
            BaseResponse BaseResponse = new BaseResponse();
        }

        public string RegistrationFeeAmount { get; set; }
        public string ConvenienceFeeAmount { get; set; }
        public string TotalRegistrationFeeAmount { get; set; }
        public string PaymentReferenceNumber { get; set; }

        public string RegisterNumber { get; set; }
        public string VinOrChassis { get; set; }
        public byte[] EncryptedVehicleCertificateNumber { get; set; }

        public string TitleHolderIdDocumentType { get; set; }
        public string TitleHolderIdDocumentNumber { get; set; }

        public string OwnerIdDocumentType { get; set; }
        public string OwnerIdDocumentNumber { get; set; }
        public Guid Reference { get; set; }

        public BaseResponse BaseResponse { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);

    }

}