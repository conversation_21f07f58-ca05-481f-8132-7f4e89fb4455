using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class DriverInformationRequest : AbstractValidatableObject
    {
        [Required]
        [JsonConverter(typeof(StringEnumConverter))]
        public DocumentTypeEnum DocumentTypeCode { get; set; }

        [Required]
        public string DocumentNumber { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(DocumentNumber.Length > 13)
            {
                errors.Add(new ValidationResult("DocumentNumber cannot be more than 13 digits", new[] { nameof(DocumentNumber) }));
            }

            return errors;
        }
    }
}