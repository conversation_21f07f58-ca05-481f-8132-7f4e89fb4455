using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class RTMCLoginDetails : AbstractValidatableObject
    {

        public long? Id { get; set; }

        public string BusinessRegistrationNumber { get; set;}
        public string Username { get; set; }
        public string Password { get; set; }
        public string OwnerDocumentNumber { get; set; }
        public string ProxyDocumentNumber { get; set; }
        public string RepresentativeDocumentNumber { get; set; }
        public bool? HasLogin { get; set; }
        public bool? CanOnlineReg { get; set; }
        public bool? CanOnlineRelease {get;set;}

        public override string ToString() => JsonConvert.SerializeObject(this);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {

            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(BusinessRegistrationNumber))
            {
                errors.Add(new ValidationResult("Business Registration Number cannot blank", new[] { nameof(BusinessRegistrationNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(BusinessRegistrationNumber) && BusinessRegistrationNumber.Length > 20)
            {
                errors.Add(new ValidationResult("Business Registration Number Length cannot exceed 20 characters", new[] { nameof(BusinessRegistrationNumber) }));
            }

            if(String.IsNullOrWhiteSpace(Username))
            {
                errors.Add(new ValidationResult("Username cannot blank", new[] { nameof(Username) }));
            }

            if(!String.IsNullOrWhiteSpace(Username) && Username.Length > 200)
            {
                errors.Add(new ValidationResult("Username Length cannot exceed 200 characters", new[] { nameof(Username) }));
            }

            if(String.IsNullOrWhiteSpace(Password))
            {
                errors.Add(new ValidationResult("Password cannot blank", new[] { nameof(Password) }));
            }

            if(!String.IsNullOrWhiteSpace(Password) && Password.Length > 200)
            {
                errors.Add(new ValidationResult("Password Length cannot exceed 200 characters", new[] { nameof(Password) }));
            }

            if(String.IsNullOrWhiteSpace(OwnerDocumentNumber))
            {
                errors.Add(new ValidationResult("OwnerDocumentNumber cannot blank", new[] { nameof(OwnerDocumentNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(OwnerDocumentNumber) && OwnerDocumentNumber.Length > 200)
            {
                errors.Add(new ValidationResult("OwnerDocumentNumber Length cannot exceed 200 characters", new[] { nameof(OwnerDocumentNumber) }));
            }

            if(String.IsNullOrWhiteSpace(ProxyDocumentNumber))
            {
                errors.Add(new ValidationResult("ProxyDocumentNumber cannot blank", new[] { nameof(ProxyDocumentNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(ProxyDocumentNumber) && ProxyDocumentNumber.Length > 200)
            {
                errors.Add(new ValidationResult("ProxyDocumentNumber Length cannot exceed 200 characters", new[] { nameof(ProxyDocumentNumber) }));
            }

            if(String.IsNullOrWhiteSpace(RepresentativeDocumentNumber))
            {
                errors.Add(new ValidationResult("RepresentativeDocumentNumber cannot blank", new[] { nameof(RepresentativeDocumentNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(RepresentativeDocumentNumber) && RepresentativeDocumentNumber.Length > 200)
            {
                errors.Add(new ValidationResult("RepresentativeDocumentNumber Length cannot exceed 200 characters", new[] { nameof(RepresentativeDocumentNumber) }));
            }


            return errors;
        }

    }
}