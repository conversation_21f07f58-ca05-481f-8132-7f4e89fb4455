using System;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class RTMCLoginDetailsResponse
    {

        public RTMCLoginDetailsResponse()
        {
            
        }

        public string BusinessRegistrationNumber { get; set;}
        public string Username { get; set; }
        public string OwnerDocumentNumber { get; set; }
        public string ProxyDocumentNumber { get; set; }
        public string RepresentativeDocumentNumber { get; set; }
        public bool? HasLogin { get; set; }
        public bool? CanOnlineReg { get; set; }
        public bool? CanOnlineRelease {get;set;}

        public long Id { get; set; }

        public bool Deleted { get; set; } = false;

        public DateTimeOffset CreatedOn { get; set; }

        public DateTimeOffset? ModifiedOn { get; set; }

        public string CreatedBy { get; set; }

        public string ModifiedBy { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
        public BaseResponse BaseResponse { get; set; }


    }
}