using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class RTMCLoginDetailsRequest : AbstractValidatableObject
    {
        public string BusinessRegistrationNumber {get;set;}

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {

            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(BusinessRegistrationNumber))
            {
                errors.Add(new ValidationResult("Business Registration Number cannot blank", new[] { nameof(BusinessRegistrationNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(BusinessRegistrationNumber) && BusinessRegistrationNumber.Length > 14)
            {
                errors.Add(new ValidationResult("Business Registration Number Length cannot exceed 14 characters", new[] { nameof(BusinessRegistrationNumber) }));
            }

            return errors;
        }

    }
}