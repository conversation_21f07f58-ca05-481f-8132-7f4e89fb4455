using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class RTMCCredentialsResponse 
    {

        public string BusinessRegistrationNumber { get; set;}
        public string Username { get; set; }
        public string DecryptedPassword { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);


    }
}