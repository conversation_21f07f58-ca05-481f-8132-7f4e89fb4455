using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class RTMCDeleteRequest : AbstractValidatableObject
    {

        public long Id { get; set; }


        public override string ToString() => JsonConvert.SerializeObject(this);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {

            var errors = new List<ValidationResult>();

            if(Id < 0)
             {
                errors.Add(new ValidationResult("ID Field cannot be less than zero", new[] { nameof(Id) }));
            }

            return errors;
        }

    }
}