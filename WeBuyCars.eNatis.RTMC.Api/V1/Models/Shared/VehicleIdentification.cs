using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public sealed class VehicleIdentification : BaseAbstractValidatableObject
    {


        public string VinOrChassis { get; set; }
        public string RegisterNumber { get; set; }
        public string ControlNumber { get; set; }
        public string LicenceNumber { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);


        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            return errors;
        }
    }
}