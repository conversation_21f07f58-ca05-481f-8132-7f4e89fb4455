using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberVerificationRequest
{
    public sealed class ControlNumberVerificationRequest : AbstractValidatableObject
    {

        [Required]
        public EntityIdentification TitleHolder { get; set; }      

        [Required]
        public EntityIdentification Owner { get; set; }    

        [Required]
        public VehicleIdentification Vehicle { get; set; }

        public string BusinessRegistrationNumber { get; set; } = "F151307720094";

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(String.IsNullOrWhiteSpace(Vehicle.ControlNumber))
            {
                errors.Add(new ValidationResult("A Control Number must be provided", new[] { nameof(Vehicle.ControlNumber) }));
            }

            if(!String.IsNullOrWhiteSpace(Vehicle.VinOrChassis))
            {
                if(Vehicle.VinOrChassis.Length > 17)
                {
                    errors.Add(new ValidationResult("vinOrChassis cannot be more than 17 digits", new[] { nameof(Vehicle.VinOrChassis) }));
                }
            }

            if(!String.IsNullOrWhiteSpace(Vehicle.RegisterNumber))
            {
                if(Vehicle.RegisterNumber.Length > 7)
                {
                    errors.Add(new ValidationResult("RegisterNumber cannot be more than 7 digits", new[] { nameof(Vehicle.RegisterNumber) }));
                }
            }

            return errors;
        }
    }
}