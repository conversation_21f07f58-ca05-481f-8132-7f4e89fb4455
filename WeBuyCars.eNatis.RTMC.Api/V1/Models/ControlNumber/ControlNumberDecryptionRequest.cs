using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberDecryptionRequest
{
    public sealed class ControlNumberDecryptionRequest : AbstractValidatableObject
    {

        [Required]
        public byte[] EncryptedControlNumber { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

        public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
        {
            var errors = new List<ValidationResult>();

            if(EncryptedControlNumber == null)
            {
                errors.Add(new ValidationResult("An Encrypted Control Number must be provided", new[] { nameof(EncryptedControlNumber) }));
            }

            return errors;
        }
    }
}