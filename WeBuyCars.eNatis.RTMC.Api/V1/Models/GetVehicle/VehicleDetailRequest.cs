using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class VehicleDetailRequest : AbstractValidatableObject
    {

          public string Vin { get; set; }
          //Need to remove the RegistrationNumber field as its only done temporarily
          public string RegistrationNumber { get; set; }
          public string RegisterNumber { get; set; }
          public string EngineNumber { get; set; }
          public string LicenceNumber { get; set; }
          public string LicenseNumber { get; set; }

          public override string ToString() => JsonConvert.SerializeObject(this, Formatting.Indented);

          public override async Task<IEnumerable<ValidationResult>> ValidateAsync(ValidationContext validationContext,CancellationToken cancellation)
          {

               var errors = new List<ValidationResult>();

               if(String.IsNullOrWhiteSpace(Vin) && String.IsNullOrWhiteSpace(RegisterNumber) && String.IsNullOrWhiteSpace(EngineNumber) && String.IsNullOrWhiteSpace(LicenceNumber) && String.IsNullOrWhiteSpace(RegistrationNumber) && String.IsNullOrWhiteSpace(LicenseNumber))
               {
                    errors.Add(new ValidationResult("Atleast a Vin, Register Number, Engine Number or Licence Number needs to be provided", new[] { nameof(Vin), nameof(RegisterNumber), nameof(EngineNumber), nameof(LicenceNumber), nameof(RegistrationNumber), nameof(LicenseNumber)  }));
               }

               if(!String.IsNullOrWhiteSpace(RegisterNumber) && RegisterNumber.Length > 7)
               {
                    errors.Add(new ValidationResult("Register Number Length cannot exceed 7 characters", new[] { nameof(RegisterNumber) }));
               }

               if(!String.IsNullOrWhiteSpace(Vin) && Vin.Length > 17)
               {
                    errors.Add(new ValidationResult("Vin Number Length cannot exceed 17 characters", new[] { nameof(Vin) }));
               }

               if(!String.IsNullOrWhiteSpace(EngineNumber) && EngineNumber.Length > 20)
               {
                    errors.Add(new ValidationResult("Engine Number Length cannot exceed 20 characters", new[] { nameof(EngineNumber) }));
               }

               if(!String.IsNullOrWhiteSpace(LicenceNumber) && LicenceNumber.Length > 9)
               {
                    errors.Add(new ValidationResult("Licence Number Length cannot exceed 9 characters", new[] { nameof(LicenceNumber) }));
               }

               return errors;
          }
    }
}