using System;
using Newtonsoft.Json;
namespace WeBuyCars.eNatis.RTMC.Api.V1.Models
{
    public class VehicleDetailArchivedResponse : VehicleDetailResponse
    {

        public VehicleDetailArchivedResponse(){
            
        }

        public long Id { get; set; }

        public bool Deleted { get; set; } = false;

        public DateTimeOffset CreatedOn { get; set; }

        public DateTimeOffset? ModifiedOn { get; set; }

        public string CreatedBy { get; set; }

        public string ModifiedBy { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);
        
    }
}