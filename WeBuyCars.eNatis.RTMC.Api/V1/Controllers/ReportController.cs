
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.Report;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{


    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    [Authorize]  
    public class ReportController : ControllerBase
    {

        readonly ILogger<ReportController> _logger;
        private readonly ILogger _loggerFactory;
        private readonly SharedServices _sharedServices;

        private readonly IReportRepository _reportRepository;


        #region Constructors
        public ReportController(
            ILogger<ReportController> logger,
            ILoggerFactory loggerFactory,
            SharedServices sharedServices,
            IReportRepository reportRepository
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this._loggerFactory = loggerFactory.CreateLogger<EnumController>();
            _sharedServices = sharedServices ?? throw new System.ArgumentNullException(nameof(sharedServices));
            _reportRepository = reportRepository ?? throw new System.ArgumentNullException(nameof(reportRepository));
        }
        #endregion


        /// <summary>
        /// Retrieve a List of Enums
        /// Potential Values :
        /// DocumentType
        /// MotorVehicleState
        /// NatureOfOwnership
        /// RegistrationReason
        /// VehicleUsage
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        [HttpPost("[action]")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "GetReportData")]
        public async Task<ActionResult> GetOnlineTransactionReport(OnlineTransactionReportRequest request)
        {
            var records = await this._reportRepository.GetOnlineTransactionReportRecords(request.NamedTimeRange);

            return Ok(records);
        }
    }
}