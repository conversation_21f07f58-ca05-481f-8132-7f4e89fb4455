using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Network;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Services.Interfaces;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{
    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    //[AllowAnonymous]
    [Authorize] 
    public class HealthCheckController : ControllerBase
    {
        private readonly INatisIntegrationService _natisIntegrationService;
        private readonly ILogger<HealthCheckController> _logger;
        private readonly RTMCVPNStatus _rTMCVPNStatus;
        private readonly RTMCSettingsService _rTMCSettingService;

        #region Constructors
        public HealthCheckController(
            INatisIntegrationService natisIntegrationService,
            ILogger<HealthCheckController> logger,
            RTMCVPNStatus rTMCVPNStatus,
            RTMCSettingsService rTMCSettingService
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _natisIntegrationService = natisIntegrationService ?? throw new System.ArgumentNullException(nameof(natisIntegrationService));
            _rTMCVPNStatus = rTMCVPNStatus ?? throw new ArgumentNullException(nameof(rTMCVPNStatus));
            _rTMCSettingService = rTMCSettingService ?? throw new ArgumentNullException(nameof(rTMCSettingService));
        }
        #endregion

        #region Public Methods
             

        [HttpGet("ClearCache")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> ClearCache()
        {
            try
            {

                //Clear Cache
                var result = await _rTMCSettingService.ClearCache();

                if(result)
                {
                    return Ok();
                }else
                {
                    return BadRequest();
                }

            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Clearing Cache API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCVehicleEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCVehicleEndPoint()
        {
            try
            {
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCVehicleEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }

            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCVehicleEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCVehicleDetailEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCVehicleDetailEndPoint()
        {
            try
            {
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCVehicleDetailEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }
            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCVehicleDetailEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCOwnerTitleHolderConfirmationEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCOwnerTitleHolderConfirmationEndPoint()
        {
            try
            {                
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCOwnerTitleHolderConfirmationEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }
            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCOwnerTitleHolderConfirmationEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCDriverEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCDriverEndPoint()
        {
            try
            {
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCDriverEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }
            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCDriverEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCVehicleOwnerRegistrationEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCVehicleOwnerRegistrationEndPoint()
        {
            try
            {
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCVehicleOwnerRegistrationEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }
            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCVehicleOwnerRegistrationEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        [HttpGet("CheckRTMCTitleHolderTransferEndPoint")]
        [ProducesResponseType(typeof(void), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> CheckRTMCTitleHolderTransferEndPoint()
        {
            try
            {
                if(_rTMCVPNStatus.VPNOnline){
                    var result = await _natisIntegrationService.CheckRTMCTitleHolderTransferEndPointAsync();

                    if(result)
                    {
                        return Ok();
                    }else
                    {
                        return BadRequest();
                    }
                }else
                {
                    return StatusCode(StatusCodes.Status503ServiceUnavailable);
                }
            }catch(Exception ex)
            {
                _logger.LogError("Error : Exception in Checking CheckRTMCTitleHolderTransferEndPointAsync API Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        #endregion


        #region Pivate Methods
        

        #endregion


    }
}