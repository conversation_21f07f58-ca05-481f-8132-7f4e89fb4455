using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Settings;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{
    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    //[AllowAnonymous]
    [Authorize] 
    public class SettingsController : ControllerBase
    {
        readonly RTMCSettingsService _rTMCSettingsService;
        readonly ILogger<SettingsController> _logger;
        private readonly ILogger _loggerFactory;

        #region Constructors
        public SettingsController(
            RTMCSettingsService rTMCSettingsService,
            ILogger<SettingsController> logger,
            ILoggerFactory loggerFactory
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _rTMCSettingsService = rTMCSettingsService ?? throw new System.ArgumentNullException(nameof(rTMCSettingsService));
            this._loggerFactory = loggerFactory.CreateLogger<SettingsController>();
        }
        #endregion

        #region Public Methods

        /// <summary>
        /// Retrieve Specific RTMC Login Settings
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// POST /api/v1/Settings/GetRTMCLoginSettings
        /// {
        ///  "businessRegistrationNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        /// }
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetRTMCLoginSettings")]
        [ProducesResponseType(typeof(VehicleDetailResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "SettingsAdminRead")]
        public async Task<ActionResult> GetRTMCLoginSettings(RTMCLoginDetailsRequest loginDetailRequest)
        {
            try
            {
                _logger.LogWarning("SettingsController : GetRTMCLoginSettings | loginDetailRequest : " + loginDetailRequest.ToString());
                return Ok(await _rTMCSettingsService.GetRTMCLoginSettingsAsync(loginDetailRequest));
            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetRTMCLoginSettings Controller : Exception :" + ex.ToString());
                throw;
            }

        }

        /// <summary>
        /// Retrieve RTMC Login Settings List
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// POST /api/v1/Settings/GetRTMCLoginSettingsList
        /// {
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        /// }
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetRTMCLoginSettingsList")]
        [ProducesResponseType(typeof(RTMCLoginDetailsListResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "SettingsAdminRead")]
        public async Task<ActionResult> GetRTMCLoginSettingsList(RTMCLoginDetailsListRequest loginDetailListRequest)
        {
            try
            {
                _logger.LogWarning("SettingsController : GetRTMCLoginSettingsList | loginDetailListRequest : " + loginDetailListRequest.ToString());
                return Ok(await _rTMCSettingsService.GetRTMCLoginSettingsListAsync(loginDetailListRequest));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in GetRTMCLoginSettingsList Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        /// <summary>
        /// Create a RTMC Login Setting.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// POST /api/v1/Settings/UpdateRTMCLoginSettings
        ///
        /// </remarks>
        /// <returns>The update a RTMC Login Settings.</returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="409">Conflict. Body type name already exists.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("CreateRTMCLoginSettings")]
        [ProducesResponseType(typeof(RTMCLoginDetailsResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "SettingsAdminModify")]
        public async Task<ActionResult<RTMCLoginDetailsResponse>> CreateRTMCLoginSettings(RTMCLoginDetails request)
        {
            try{
                _logger.LogWarning("SettingsController : CreateRTMCLoginSettings | loginDetailListRequest : " + request.ToString());
                return Ok(await _rTMCSettingsService.CreateRTMCLoginSettingsAsync(request));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in CreateRTMCLoginSettings Controller : Exception :" + ex.ToString());              
                throw;
            }
        }

        /// <summary>
        /// Update an Existing RTMC Login Setting.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// PUT /api/v1/Settings/UpdateRTMCLoginSettings
        ///
        /// </remarks>
        /// <returns>The update a RTMC Login Settings.</returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="409">Conflict. Body type name already exists.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPut("UpdateRTMCLoginSettings")]
        [ProducesResponseType(typeof(RTMCLoginDetailsResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "SettingsAdminModify")]
        public async Task<ActionResult<RTMCLoginDetailsResponse>> UpdateRTMCLoginSettings(RTMCLoginDetails request)
        {
            try{
                _logger.LogWarning("SettingsController : UpdateRTMCLoginSettings | loginDetailListRequest : " + request.ToString());
                return Ok(await _rTMCSettingsService.UpdateRTMCLoginSettingsAsync(request));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in UpdateRTMCLoginSettings Controller : Exception :" + ex.ToString());              
                throw;
            }
        }


        /// <summary>
        /// Delete an Existing RTMC Login Setting.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// DELETE /api/v1/Settings/DeleteRTMCLoginSettings
        ///
        /// </remarks>
        /// <returns>Delete a RTMC Login Settings.</returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="409">Conflict. Body type name already exists.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpDelete("DeleteRTMCLoginSettings")]
        [ProducesResponseType(typeof(RTMCLoginDetailsResponse), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "SettingsAdminDelete")]
        public async Task<ActionResult<RTMCLoginDetailsResponse>> DeleteRTMCLoginSettings(RTMCDeleteRequest request)
        {
            try{
                _logger.LogWarning("SettingsController : UpdateRTMCLoginSettings | loginDetailListRequest : " + request.ToString());
                return Ok(await _rTMCSettingsService.DeleteRTMCLoginSettingsAsync(request));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in UpdateRTMCLoginSettings Controller : Exception :" + ex.ToString());              
                throw;
            }
        }


        /// <summary>
        /// REST Token Generation
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// Get /api/v1/Settings/TokenGeneration
        ///
        /// </remarks>
        /// <returns>Get a Token.</returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="409">Conflict. Body type name already exists.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpGet("TokenGeneration")]
        [ProducesResponseType(typeof(AccessToken), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //[AllowAnonymous]
        public async Task<ActionResult<AccessToken>> TokenGeneration()
        {
            try{
                
                _logger.LogWarning("SettingsController : TokenGenerationByBRN | loginDetailListRequest");

                var token = await _rTMCSettingsService.GetAccessTokenAsync();
                if(token == null)
                {
                    return NotFound();
                }

                return Ok(token);

            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in TokenGeneration Controller : Exception :" + ex.ToString());              
                throw;
            }
        }


        /// <summary>
        /// REST Token Generation by BRN
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        /// Get /api/v1/Settings/TokenGeneration
        ///
        /// </remarks>
        /// <returns>Get a Token.</returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="409">Conflict. Body type name already exists.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpGet("TokenGenerationByBRN")]
        [ProducesResponseType(typeof(AccessToken), StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status409Conflict)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        //[AllowAnonymous]
        public async Task<ActionResult<AccessToken>> TokenGenerationByBRN(string businessRegistrationNumber)
        {
            try{

                _logger.LogWarning("SettingsController : TokenGenerationByBRN | loginDetailListRequest");

                var token = await _rTMCSettingsService.GetAccessTokenByBRNAsync(businessRegistrationNumber);
                if(token == null)
                {
                    return NotFound();
                }

                return Ok(token);

            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in TokenGenerationByBRN Controller : Exception :" + ex.ToString());              
                throw;
            }
        }

        #endregion

    }
}