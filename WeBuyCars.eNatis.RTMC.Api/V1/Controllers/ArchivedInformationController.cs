using System;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Models;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberDecryptionRequest;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.ControlNumberDecryptionResponse;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.NCO;
using WeBuyCars.eNatis.RTMC.Api.V1.Models.Registration;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.ArchivedInformation;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.Extensions;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{
    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    //[AllowAnonymous]
    [Authorize] 
    public class ArchivedInformationController : ControllerBase
    {
        readonly ILogger<ArchivedInformationController> _logger;
        private readonly ArchivedVehicleHistoryService _archivedVehicleHistoryService;
        private readonly ArchivedInformationService _archivedInformationService;
        private readonly ArchivedOwnerVehicleHistoryService _archivedOwnerVehicleHistoryService;
        private readonly ILogger _loggerFactory;
        private readonly IWebHostEnvironment _environment;
        private readonly string _environmentName;

        #region Constructors
        public ArchivedInformationController(
            ILogger<ArchivedInformationController> logger,
            ArchivedVehicleHistoryService archivedVehicleHistoryService,
            ArchivedInformationService archivedInformationService,
            ArchivedOwnerVehicleHistoryService archivedOwnerVehicleHistoryService,
            ILoggerFactory loggerFactory,
            IWebHostEnvironment environment
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this._loggerFactory = loggerFactory.CreateLogger<ArchivedInformationController>();
            _archivedVehicleHistoryService = archivedVehicleHistoryService ?? throw new System.ArgumentNullException(nameof(archivedVehicleHistoryService));
            _archivedInformationService = archivedInformationService ?? throw new System.ArgumentNullException(nameof(archivedInformationService));
            _archivedOwnerVehicleHistoryService = archivedOwnerVehicleHistoryService ?? throw new System.ArgumentNullException(nameof(archivedOwnerVehicleHistoryService));
            _environment = environment ?? throw new ArgumentNullException(nameof(environment));

            _environmentName = _environment.EnvironmentName;
        }
        #endregion

        #region Public Methods
             
        /// <summary>
        /// Retrieve Latest Historic Vehicle Information 
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleHistoricInformationLatest
        ///{
        ///  "vin": "string",
        ///  "registerNumber": "string",
        ///  "engineNumber": "string",
        ///  "licenceNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleHistoricInformationLatest")]
        [ProducesResponseType(typeof(VehicleDetailArchivedResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "GetVehicle")]
        public async Task<ActionResult> GetVehicleHistoricInformationLatest(VehicleDetailArchivedRequest vehicleDetailArchivedRequest)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : GetVehicleHistoricInformationLatest | vehicleRequest : " + vehicleDetailArchivedRequest.ToString());

                var vehicleRequest = vehicleDetailArchivedRequest.VehicleDetailRequest;
                var cacheTimeToLive = vehicleDetailArchivedRequest.CacheTimeToLive;

                vehicleRequest.NetworkAddress = IPCheck(vehicleRequest.NetworkAddress);

                return Ok(await _archivedInformationService.GetVehicleHistoricInformationAsync(vehicleRequest, cacheTimeToLive, _environmentName));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in GetVehicleHistoricInformationLatest Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        /// <summary>
        /// Retrieve Historic Vehicle Information List
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleHistoricInformationList
        ///{
        ///  "vin": "string",
        ///  "registerNumber": "string",
        ///  "engineNumber": "string",
        ///  "licenceNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleHistoricInformationList")]
        [ProducesResponseType(typeof(VehicleDetailArchivedResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "GetVehicle")]
        public async Task<ActionResult> GetVehicleHistoricInformationList(VehicleDetailRequest vehicleRequest)
        {
            try
            {
                vehicleRequest.NetworkAddress = IPCheck(vehicleRequest.NetworkAddress);
                _logger.LogWarning("ArchivedInformationController : GeVehicleHistoricInformationList | vehicleRequest : " + vehicleRequest.ToString());

                return Ok(await _archivedInformationService.GetVehicleHistoricInformationListAsync(vehicleRequest));
            }catch(DomainException ex)
            {
                _logger.LogError("Error : Exception in GeVehicleHistoricInformationList Controller : Exception :" + ex.ToString());              
                throw;
            }

        }

        /// <summary>
        /// This will be Deprecated
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/VehicleOwnershipHistory
        ///{
        ///  "Reference": "Guid",
        ///  "IncludeSensitiveInformation": "Bool"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("VehicleOwnershipHistory")]
        [ProducesResponseType(typeof(OwnershipHistoryDataResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> VehicleOwnershipHistory(OwnershipHistoryDataArchivedRequest ownershipHistoryDataArchivedRequest)
        {
            try
            {

                // Log the API call information
                var httpClient = this.HttpContext?.User?.GetClient();

                string headers = string.Join("; ", HttpContext.Request.Headers.Select(h => $"{h.Key}: {h.Value}"));
                _logger.LogError("ArchivedInformationController : VehicleOwnershipHistory | API called: {Method} {Url}, Headers: {Headers}", HttpContext.Request.Method, HttpContext.Request.Path, headers);
                _logger.LogError("ArchivedInformationController : VehicleOwnershipHistory | Client Calling Deprecated End Point : " + httpClient);


                _logger.LogWarning("ArchivedInformationController : VehicleOwnershipHistory | Reference GUID : " + ownershipHistoryDataArchivedRequest.Reference);

                return Ok(await _archivedVehicleHistoryService.GetVehicleOwnershipHistoryFromRequestAsync(ownershipHistoryDataArchivedRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in VehicleOwnershipHistory Controller : Exception :" + ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// This Will be Deprecated
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleOwnershipHistoryByReference
        ///{
        ///  "Reference": "Guid",
        ///  "IncludeSensitiveInformation": "Bool"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleOwnershipHistoryByReference")]
        [ProducesResponseType(typeof(OwnershipHistoryDataResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> GetVehicleOwnershipHistoryByReference(OwnershipHistoryDataArchivedRequest ownershipHistoryDataArchivedRequest)
        {
            try
            {
                // Log the API call information
                var httpClient = this.HttpContext?.User?.GetClient();

                string headers = string.Join("; ", HttpContext.Request.Headers.Select(h => $"{h.Key}: {h.Value}"));
                _logger.LogError("ArchivedInformationController : GetVehicleOwnershipHistoryByReference | API called: {Method} {Url}, Headers: {Headers}", HttpContext.Request.Method, HttpContext.Request.Path, headers);
                _logger.LogError("ArchivedInformationController : GetVehicleOwnershipHistoryByReference | Client Calling Deprecated End Point : " + httpClient);

                _logger.LogWarning("ArchivedInformationController : GetVehicleOwnershipHistoryByReference | Reference GUID : " + ownershipHistoryDataArchivedRequest.Reference);

                return Ok(await _archivedVehicleHistoryService.GetVehicleOwnershipHistoryAsync(ownershipHistoryDataArchivedRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetVehicleOwnershipHistoryByReference Controller : Exception :" + ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Retrieve Owner Vehicle History List based on Identifier Number
        /// </summary>
        /// <remarks>
        /// Sample request:
        /// Get /api/v1/ArchivedInformation/VehicleOwnershipHistoryByIdentifier
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpGet("VehicleOwnershipHistoryByIdentifier")]
        [ProducesResponseType(typeof(VehiclesOwnedByEntityResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> VehicleOwnershipHistoryByIdentifier(string identificationNumber)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : VehicleOwnershipHistoryByIdentifier | IdentificationNumber : " + identificationNumber);

                return Ok(await _archivedOwnerVehicleHistoryService.GetVehicleOwnershipHistoryByIdentifierNumberAsync(identificationNumber));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in VehicleOwnershipHistoryByIdentifier Controller : Exception :" + ex.ToString());
                throw;
            }
        }


        /// <summary>
        /// This is the Main End Point that is used to retrieve the Vehicle Ownership History from the Request Object for the Buy lead from the Request Table
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleOwnershipHistoryRequestByReference
        ///{
        ///  "Reference": "Guid",
        ///  "IncludeSensitiveInformation": "Bool"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleOwnershipHistoryRequestByReference")]
        [ProducesResponseType(typeof(OwnershipHistoryDataResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanVerifyVehicleOwner")]
        public async Task<ActionResult> GetVehicleOwnershipHistoryRequestByReference(OwnershipHistoryDataArchivedRequest ownershipHistoryDataArchivedRequest)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : GetVehicleOwnershipHistoryRequestByReference | Reference GUID : " + ownershipHistoryDataArchivedRequest.Reference);

                return Ok(await _archivedVehicleHistoryService.GetVehicleOwnershipHistoryFromRequestAsync(ownershipHistoryDataArchivedRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetVehicleOwnershipHistoryRequestByReference Controller : Exception :" + ex.ToString());
                throw;
            }
        }


        /// <summary>
        /// This is the Main End Point that is used to retrieve the Vehicle Detailed Information by Reference from the Request Table
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleDetailedRequestByReference
        ///{
        ///  "Reference": "Guid",
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleDetailedRequestByReference")]
        [ProducesResponseType(typeof(VehicleDetailArchivedResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "GetVehicle")]
        public async Task<ActionResult> GetVehicleDetailedRequestByReference(VehicleDetailArchivedByReferenceRequest vehicleDetailArchivedByReferenceRequest)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : GetVehicleDetailedRequestByReference | Reference GUID : " + vehicleDetailArchivedByReferenceRequest.Reference);

                return Ok(await _archivedInformationService.GetVehicleDetailedRequestByReferenceAsync(vehicleDetailArchivedByReferenceRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetVehicleDetailedRequestByReference Controller : Exception :" + ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// Decrypt Control Number
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/DecryptControlNumber/
        ///{
        ///  "encryptedControlNumber": "string",
        ///  "user": "string",
        ///  "workStation": "string",
        ///  "locality": "string",
        ///  "networkAddress": "string"
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("DecryptControlNumber")]
        [ProducesResponseType(typeof(ControlNumberDecryptionResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [ProducesResponseType(StatusCodes.Status503ServiceUnavailable)]
        [Authorize(Policy = "CanDecryptControlNumber")]
        public async Task<ActionResult> DecryptControlNumber(ControlNumberDecryptionRequest controlNumberDecryptionRequest)
        {
            try
            {

                controlNumberDecryptionRequest.NetworkAddress = IPCheck(controlNumberDecryptionRequest.NetworkAddress);
                _logger.LogWarning("ArchivedInformationController : DecryptControlNumber | onlineNCORequest : " + controlNumberDecryptionRequest.ToString());

                return Ok(await _archivedInformationService.DecryptControlNumberAsync(controlNumberDecryptionRequest));
            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in DecryptControlNumber Controller : Exception :" + ex.ToString());
                throw;
            }
        }

        /// <summary>
        /// This End Point will be used for Retrieving Unencrypted NCO Information for Printing the RC2 Documentation
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetDecryptedOnlineNCOByReference
        ///{
        ///  "Reference": "Guid",
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetDecryptedOnlineNCOByReference")]
        [ProducesResponseType(typeof(NCOArchivedRequestByReferenceResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanRetrieveDecryptedOnlineNCO")]
        public async Task<ActionResult> GetDecryptedOnlineNCOByReference(NCOArchivedRequestByReferenceRequest nCOArchivedRequestByReferenceRequest)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : GetNCODecryptedInformationRequestByReference | Reference GUID : " + nCOArchivedRequestByReferenceRequest.Reference);

                return Ok(await _archivedInformationService.GetNCODecryptedInformationRequestByReferenceAsync(nCOArchivedRequestByReferenceRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetNCODecryptedInformationRequestByReference Controller : Exception :" + ex.ToString());
                throw;
            }
        }


        /// <summary>
        /// This End Point will be used for Retrieving Vehicle Registration Information (Successful Dealer Stocked Record)
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///POST /api/v1/ArchivedInformation/GetVehicleOwnerRegistrationByReference
        ///{
        ///  "Reference": "Guid",
        ///}
        ///
        /// </remarks>
        /// <returns></returns>
        /// <response code="201">The request was successful.</response>
        /// <response code="400">Request validation exception.</response>
        /// <response code="401">Unauthorized. Invalid token.</response>
        /// <response code="403">Forbidden. Insufficient scope or claim.</response>
        /// <response code="500">A server error occurred.</response>
        [HttpPost("GetVehicleOwnerRegistrationByReference")]
        [ProducesResponseType(typeof(RegistrationArchivedRequestByReferenceResponse), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        [Authorize(Policy = "CanRetrieveVehicleRegistration")]
        public async Task<ActionResult> GetVehicleOwnerRegistrationByReference(RegistrationArchivedRequestByReferenceRequest registrationArchivedRequestByReferenceRequest)
        {
            try
            {

                _logger.LogWarning("ArchivedInformationController : GetVehicleOwnerRegistrationByReference | Reference GUID : " + registrationArchivedRequestByReferenceRequest.Reference);

                return Ok(await _archivedInformationService.GetVehicleOwnerRegistrationByReferenceAsync(registrationArchivedRequestByReferenceRequest));

            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in GetVehicleOwnerRegistrationByReference Controller : Exception :" + ex.ToString());
                throw;
            }
        }



        #endregion


        #region Pivate Methods
        
        private string IPCheck(string ip){

            var ipresult = "";
            if(String.IsNullOrEmpty(ip))
            {
                ipresult = this.HttpContext.Request.Host.Host;
            }else
            {
                ipresult = ip;
            }

            return ipresult;

        }
            
        #endregion


    }
}