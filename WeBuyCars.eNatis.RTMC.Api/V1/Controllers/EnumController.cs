
using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Shared;
using WeBuyCars.eNatis.RTMC.Core.Enumerations;
using WeBuyCars.eNatis.RTMC.Core.Exceptions;

namespace WeBuyCars.eNatis.RTMC.Api.V1.Controllers
{


    [ApiController]
    [Produces("application/json")]
    [Route("v{version:apiVersion}/[controller]")]
    //[AllowAnonymous]
    [Authorize]  
    public class EnumController : ControllerBase
    {

        readonly ILogger<EnumController> _logger;
        private readonly ILogger _loggerFactory;
        private readonly SharedServices _sharedServices;


        #region Constructors
        public EnumController(
            ILogger<EnumController> logger,
            ILoggerFactory loggerFactory,
            SharedServices sharedServices
            )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            this._loggerFactory = loggerFactory.CreateLogger<EnumController>();
            _sharedServices = sharedServices ?? throw new System.ArgumentNullException(nameof(sharedServices));
        }
        #endregion



        #region Public Properties

        /// <summary>
        /// Retrieve a List of Enums
        /// Potential Values :
        /// DocumentType
        /// MotorVehicleState
        /// NatureOfOwnership
        /// RegistrationReason
        /// VehicleUsage
        /// </summary>
        /// <param name="enumType"></param>
        /// <returns></returns>
        [HttpGet("GetEnumList")]
        [ProducesResponseType(typeof(SharedServices.EnumDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status401Unauthorized)]
        [ProducesResponseType(StatusCodes.Status403Forbidden)]
        [ProducesResponseType(StatusCodes.Status500InternalServerError)]
        public async Task<ActionResult> GetEnumList(string enumType)
        {
            try
            {
                return Ok(await _sharedServices.GetEnumList(enumType));
            }
            catch (DomainException ex)
            {
                _logger.LogError("Error : Exception in EnumController : Exception :" + ex.ToString());
                throw ex;
            }
        }

        #endregion

    }
}