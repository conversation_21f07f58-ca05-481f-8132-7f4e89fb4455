{"Serilog": {"MinimumLevel": {"Default": "Warning", "Override": {"WeBuyCars.eNatis.RTMC.API": "Information"}}, "Using": ["Elastic.Serilog.Sinks"]}, "Service": {"Title": "WeBuyCars eNatis RTMC API (Development)", "Description": "WeBuyCars eNatis RTMC Microservice", "Contact": {"Name": "WeBuyCars", "Email": "<EMAIL>"}}, "ApplicationInsights": {"InstrumentationKey": "00000000000000000000000"}, "Authentication": {"Authority": "https://identity-test.webuycars.co.za", "RequireHttpsMetadata": false, "Audience": "RTMC.API"}, "AppConfig": {"EndPoint": "https://wbc-appconfig-development.azconfig.io"}, "EnatisSettings": {"BaseUrl": "https://rtmc-proxy.webuycars.co.za/development/natis/ws", "RESTBaseUrl": "https://rtmc-proxy.webuycars.co.za/development/natis/rest", "CertPath": "/Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API-Sample/example.sh", "ScriptPath": "/Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/curl.sh", "CertPassword": "", "PayloadUsername": "4984A001", "PayloadPassword": "TESTER01", "PathExtension": "Scripts/curl.sh"}, "EnatisEndPoints": {"GetVehicle": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x3003Services.wsdl", "VehicleOwnerRegistration": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x3141Services.wsdl", "OnlineNCO": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x314AServices.wsdl", "GetVehicleDetailedInformation": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x3067Services.wsdl", "GetOwnerTitleHolderConfirmation": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x3004Services.wsdl", "GetDriver": "https://rtmc-proxy.webuycars.co.za/development/natis/ws/x3042Services.wsdl", "RESTToken": "https://sauthdev.natis.gov.za/uaa/oauth2/token?grant_type=client_credentials", "GetAllOwners": "/vehicles/GetAllOwners/v1", "ControlNumberVerification": "/vehicles/TitleHolderAndOwnerQuery", "FeeCalculator": "/drivers/GetLicenseRenewalQuote", "GetVehiclesAndLicenseExpiryDates": "/cashier/X4009/getVehiclesDueForRenewal/v1", "GetVehiclesQuotationRequest": "/cashier/X4009/getVehiclesQuotation/v1", "InitiateRenewalRequest": "/cashier/X4010/initiateRenewal/v1", "CompleteRenewalRequest": "/cashier/X4010/completeRenewal/v1"}, "EnableEndPoints": {"EnabledGetVehicle": true, "EnabledVehicleOwnerRegistration": false, "EnabledOnlineNCO": false, "EnabledGetVehicleDetailedInformation": true, "EnabledGetOwnerTitleHolderConfirmation": true, "EnabledGetDriver": true, "EnabledGetAllOwners": true, "EnabledControlNumberVerification": true, "EnabledFeeCalculator": true, "EnabledVehicleLicenseRenewal": true}, "PrivateKey": "<RSAKeyValue><Modulus>4DlsLl+Wh8oAxCzm4qEThGrqZvqMZbLjmGtI/ZmYJQwh61AdrAvxIs43AFFzyXDo897jy1ScJvNMuLGiNGYJUI9uyDoMgjicEhSTsrR+u7S0s5HxZ7q0dYpsrivHG1HcPr3XZzerOonCNrCbCI3FI85c6Ko29xCDeSRususK1oU=</Modulus><Exponent>AQAB</Exponent><P>4J46apycSPP5hP4Ok6SHi8fSPIoiOXJKdaQJks7pzb9FW0Xh7r6R+EK0Ls/QIbeK3o1Jbra8rsi1uC19/aOkbw==</P><Q>/40cUJMPwYCZg+OoHCDIYzGMGCyEUKFA124MNySG628XN99IaBwcWAg+4/xv4jnFhd+EolT/4CUR0urIptn2Sw==</Q><DP>oNForbAXTTwwqAFl2ltwGKomYeXcfxjPKs8Zc5zlVMPfGmw8SLz0sbTAYMu1Do7kxFaP/s6i5NkbEhSwIgspzQ==</DP><DQ>NIUuxYyD13PzmPyHx0ghhule7nTvuJZ7egr+7bJPHEHXlr8H5YWpeZowOrDd2bOf2bSnUKmBcPKNaDiihlATYw==</DQ><InverseQ>Ov7RDFtIJ8bHEKcPOtmtQv71t84yGnGX2mEksEOco+bkR8rD36MplOuxQgjo2r1EjQIivLO61aw8/+C4JgeVQw==</InverseQ><D>nsjLTUOvy9VHGaVak57RvXIhMG+5QMOf3O4MsFo2nfWrh4IioRWL28QAux7zSfHFZf2vfuwZ8JNW/2v7m/OuH6SJtDPMm3fvzE4x6nBGBoXuFXzYwLp5Gaf9IfAU/rhEWuc1dVsXFFYVmTKFnwnLkelxeMSDEll18mDODLshhPk=</D></RSAKeyValue>", "PublicKey": "<RSAKeyValue><Modulus>4DlsLl+Wh8oAxCzm4qEThGrqZvqMZbLjmGtI/ZmYJQwh61AdrAvxIs43AFFzyXDo897jy1ScJvNMuLGiNGYJUI9uyDoMgjicEhSTsrR+u7S0s5HxZ7q0dYpsrivHG1HcPr3XZzerOonCNrCbCI3FI85c6Ko29xCDeSRususK1oU=</Modulus><Exponent>AQAB</Exponent></RSAKeyValue>", "RSAKey": "<RSAKeyValue><Modulus>vSMN5S3vG6DyCqE8dCC6vWzRwjBmZRqoE0GKMjjYSLu3+ap/Gl8Rvbk4oj4jQKqGf5c/U8BUi5+AVTn9yIJgOuyJexaqzTH+wCjuwA9DqTIkY7TIxegLeZVV+vTgVnCaKI+Ii/MGvI8D03cWicFC8GHdS3pNQ1jOSW+mDeAxjyXa2uG1tWfEgNGov6KkiVoiEPR3EgR0dJxUV4TPDRXpGs7JU0b5VZ9iRGeqjI/jkj5X5N6TJLWh3W6swK2L2SbO9STsbZECs4E0bLECHVq7OkW9lu3ChTJTs3sdM5YtPT5bvA6v25ne5LcRO9H1uW0eXd9aOk6DHzgqkxAYlqShYU6M59r9Cwz8iZT9OTJe8w0O1T3D/N9YpOF/UMkeIzRJDXs+UDuUxYwchmZqr3M5+pW/VU9e+hIL7xvO1v0x/hfJ3TOFeT9ndXjLCweJqPtAnE5Tjg3XO+opV2AsfcZcdKouFzBAAo0nuOly+O3I6qffB5J84v7J/CT/OTJh3U9p/BQ/w8KB3KUtXgfl+nbBQ/OlkUxwR9FtXyX2ud0f6BYQKUkCAwEAAQ==</Modulus><Exponent>AQAB</Exponent><P>4pT1iCNRV6Wn+44P9sTZkIxXYuGylE6isvW1st+c2JnTPceOIXJNTe6MfVUJYj0F6h2HFd3sOujhDOEwHWaXoPrOhSSugz6RYYSJ+IhC13rRAjlf2Jp5qseJTo1lDeBdhzBwz+UQHyLjRzFl81G4FRZX0i1npFqWKiIRGg+h1GUCmC8EGBwB2M1rDFeSnE9L/MY+spZKUudEfdSM8wU6KACi55M5wu/t0C3dp/NvFt8yZS1MIn9UXg4zFGUe96zWv/1gKrqU3ewZd5mVzU7rLidOjOCvW+wH2AhfFcCxzRf0f4ppwhXXNfrO6n8mW/FQaq2eLFK4XHmXCfwyETxCwQ==</P><Q>3fE+XG3bz6PhvpP7DD5Nsb3f6v+nuufjoPf3iKhzAyDwUhKfQGq+e2L0ER1vqKtuvEjbkMiMytR+6Yi3Ty/2MIRIbHqV85fIfUZu5/TS1pAkMLeNVC/K4F9WwBtgDixBLtc7N/B3iI/poB5CoyDJm2Bd6NVgHSo2uTJPGPfhlSPwnPILtq8Yl+jIpnNobMtiugXpUEPQIxl2MxKtMw4IY5g2AhnyqprW4kq68RY13wir7xd15PySb77Kiccm7vOUfG3tLAg9L5Km4tmCjFKbxt2+eQsgUH9FYZmVmhiCv7N4dktjLY/vfVYUrGLx4e7fLjoKwQ==</Q><DP>w20Q0SokrApFpHcNgzBnlmZTEzHtoABm/5d0SX1ZktjLbUxJeTE5WTU6Bac6Hk8/A7qR+r02Z7ZRAFDeSfS5EnE2qjHJwCrNW6aJ3W19GOBndOzkZ4/tC06e4B2bDc7QrZ/lZzvQwzUcsc0gIpS/3Uxw6dSwyWIDZUIjCzYRR0r/YfMThE5dZ18Q6V3tn/WUYLVMYyIC/o/3OOJTzZVMK6n9s7GtYfIrRSc6zQGsPjstUKYD34W2m4ei3WJfzyZeYQT7y4oJUZ9jmuFKO4rLL/LtKk8lVlT5JUC0OiqSs0nYvi+Viwylwby6bjkZjGzXQjkFfw==</DP><DQ>w6phTZLWdMyuHohY3E8RUBqV2cLqLvn/VdOKtu97NoDQgpbIXpZSPC3xWg5zhWYbHIaMmSAb4BIDZq3sk7/WOvvlV1GNjto7MM3UVvnizmIqFmkI7ww6UJ6ocpgRyYyayDSnKnb++d6efN/xRxFVG/1kweY+9NjICwye9HHXvn/wE5r/NRSI3vRhNLUdf4MTV27ry0A9azvVz14SsZUNnJqqnmjH/f8pz/jG+My1cet+bQb4kHETqmtGsLnujxOdqY5msfn8BBNdSQQ9X07LOk7W4sFLc0EJx5lyiP9Q8d22wfpnV5kGBSVcvu4HTn6NYc5vCw==</DQ><InverseQ>vn0AEeMyLb6JHRhX0y//JL8QSmEbsuwEt06F2W1bE+v08QJNZFmqTy9vQsUO8uK4wuho9p3SlTL7LrUtCr8mY9CG3Tqljv/cKoRIYoXDAWBrmhlbP00nFovKT2/hFKd2Nty6OLXHm6g5tq5QFr25OeeBynWtD9J5JN+sdp9kk68ZLsCk5sC8aQzg2Oo9M1lXZ0MJgD2Y+uun3SWQ2g8m2eF7V4oE5Z+f32OXLlTBjN66eAkKg2ZTMYX5ZdmkEZp9eYU1U1e/GQublNg5iI7TfQom0qddTEH3l/nF2g==</InverseQ><D>MYqDJmd8MGzCTNws6A5F+Yk28Wy1Y+JpHidb9ClKhfOQO2m5c5bLPdoq0Hk9IVfX6sCkpCAbSf+YIuq8ReMteO9xxCZKY3QhHymANs0N2y0j10wVNT3wnmxJUJzF58k73pZqCYlz0sbx+VU0VECC7/tHEv5Xcj9r3wUZxqqP7VH9rJxUfItcOSgwaEzmRP0xiNex7iINJTVimNJ3Sp7LaWsiEDPT7RAQ0Qru9qVbV0t10lxqdNw/zCoDEPC+iGGXlch1m5v32DPsiQF3WUwtrrsosQ0xZTCGf4gGDEajZAbBwWrxJtzSnA/Lq4n7pN5W4dV9QI1BeI72tw==</D></RSAKeyValue>", "ServerEnvironment": {"ServerName": "DEV", "ServerType": "SOAP"}}