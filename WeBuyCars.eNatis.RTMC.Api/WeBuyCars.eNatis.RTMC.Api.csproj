<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <RootNamespace>WeBuyCars.eNatis.RTMC.Api</RootNamespace>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\$(AssemblyName).xml</DocumentationFile>
    <noWarn>1591</noWarn>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'WeBuyCars.eNatis.RTMC.Api_LOCAL' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'WeBuyCars.eNatis.RTMC.Api_DEV' " />
  <PropertyGroup Condition=" '$(RunConfiguration)' == 'WeBuyCars.eNatis.RTMC.Api_STAGING' " />
  <ItemGroup>
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="8.0.0" />
    <ProjectReference Include="..\WeBuyCars.eNatis.RTMC.Infrastructure.Natis\WeBuyCars.eNatis.RTMC.Infrastructure.Natis.csproj" />
    <ProjectReference Include="..\WeBuyCars.eNatis.RTMC.Core\WeBuyCars.eNatis.RTMC.Core.csproj" />
    <ProjectReference Include="..\WeBuyCars.eNatis.RTMC.Infrastructure.Data\WeBuyCars.eNatis.RTMC.Infrastructure.Data.csproj" />
    <Content Include="Scripts/curl.sh">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts/curl_local.sh">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts/enatisca2028.crt">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts/webuycars_key.pem">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts/webuycars_cert.pem">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="Scripts/example.sh">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.22.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.3" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning.ApiExplorer" Version="5.1.0" />
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.3"/>
    <PackageReference Include="Microsoft.Extensions.Diagnostics.HealthChecks.EntityFrameworkCore" Version="6.0.8" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="6.0.8">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="6.0.8" />
    <PackageReference Include="System.Runtime.Caching" Version="8.0.1" />
    <PackageReference Include="Polly" Version="8.5.2" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Authentication" Version="6.0.1" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.ActiveDirectory.Authentication" Version="6.1.2" />
    <PackageReference Include="WeBuyCars.Core.Infrastructure.Swagger" Version="8.0.2" />

    <PackageReference Include="MassTransit" Version="8.1.2" />
		<PackageReference Include="MassTransit.Azure.ServiceBus.Core" Version="8.1.2" />
    <PackageReference Include="Microsoft.IdentityModel.JsonWebTokens" Version="7.5.1" />
    <PackageReference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect" Version="7.5.1" />
    <PackageReference Include="Refit" Version="8.0.0" />
    <PackageReference Include="WeBuyCars.Core" Version="8.0.2" />
    
  </ItemGroup>
</Project>