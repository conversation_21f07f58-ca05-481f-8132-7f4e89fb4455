using Microsoft.AspNetCore.Hosting;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Polly;
using Polly.Retry;
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Api.Configurations;
using WeBuyCars.eNatis.RTMC.Api.V1.Services.Network;
using WeBuyCars.eNatis.RTMC.Infrastructure.Natis.Configurations;

namespace WeBuyCars.eNatis.RTMC.Api.Diagnostics
{
    public class RTMCVPNHealthCheck : IHealthCheck
    {

        #region prop
        private const int MaxRetries = 2;
        private readonly RTMCVPNStatus _rTMCVPNStatus;
        readonly ILogger<RTMCVPNHealthCheck> _logger;
        private readonly AsyncRetryPolicy<HealthCheckResult> _retryPolicy;
        readonly eNatisServiceOptions _serviceOptions;
        readonly ServerEnvironmentOptions _serverEnvironmentOptions;
        private readonly IWebHostEnvironment _environment;
        private readonly string _environmentName;

        #endregion

        #region ctor
        public RTMCVPNHealthCheck(
            RTMCVPNStatus rTMCVPNStatus,
            ILogger<RTMCVPNHealthCheck> logger,
            IOptionsMonitor<eNatisServiceOptions> serviceOptions,
            IOptionsMonitor<ServerEnvironmentOptions> serverEnvironmentOptions,
            IWebHostEnvironment environment
        ){
            _rTMCVPNStatus = rTMCVPNStatus ?? throw new ArgumentNullException(nameof(rTMCVPNStatus));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _retryPolicy = Policy<HealthCheckResult>.Handle<Exception>().RetryAsync(MaxRetries);
            _serviceOptions = serviceOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serviceOptions));
            _serverEnvironmentOptions = serverEnvironmentOptions?.CurrentValue ?? throw new ArgumentNullException(nameof(serverEnvironmentOptions));
            _environment = environment ?? throw new ArgumentNullException(nameof(environment));

            _environmentName = _environment.EnvironmentName;
        }
            
        #endregion


        #region public        
        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default(CancellationToken))
        {

            try
            {
                var flag = true;

                //Special Check for DEV, in order to ensure that it always returns healthy, as the Certificate on RTMC Dev is broken
                if(_environmentName != "Production")
                {
                    _rTMCVPNStatus.VPNOnline = true;
                    return HealthCheckResult.Healthy(_serverEnvironmentOptions.ServerType + " is Heathy"); 
                }

                //CHeck if this is a SOAP or REST End Point
                HealthCheckResult result = HealthCheckResult.Unhealthy($"RTMC VPN is down.");

                if(_serverEnvironmentOptions.ServerType == "SOAP")
                {
                    result =  await _retryPolicy.ExecuteAsync(async () =>
                    {

                        using var client = new HttpClient();
                        client.Timeout = TimeSpan.FromSeconds(15);
                        // var result = await client.GetAsync("https://prod.enatis.co.za:8782/natis/ws");
                        var result = await client.GetAsync(_serviceOptions.BaseUrl);

                        //Do Logic to add to InMemory Cache based on the result
                        if(result.StatusCode == System.Net.HttpStatusCode.MethodNotAllowed)
                        {
                            flag = true;
                            return HealthCheckResult.Healthy();                
                        }

                        flag = false;
                        _logger.LogError("RTMCVPNHealthCheck : SOAP Health Check Failed");
                        return HealthCheckResult.Unhealthy($"RTMC SOAP is down.");

                    });

                    if(flag)
                    {
                        _rTMCVPNStatus.VPNOnline = true;
                    }else
                    {
                        _rTMCVPNStatus.VPNOnline = false;
                    }
                }

                if(_serverEnvironmentOptions.ServerType == "REST")
                {
                    result =  await _retryPolicy.ExecuteAsync(async () =>
                    {

                        using var client = new HttpClient();
                        client.Timeout = TimeSpan.FromSeconds(15);
                        // var result = await client.GetAsync("https://prod.enatis.co.za:8782/natis/ws");
                        var result = await client.GetAsync(_serviceOptions.RESTBaseUrl);

                        //Do Logic to add to InMemory Cache based on the result
                        if(result.StatusCode == System.Net.HttpStatusCode.Unauthorized || result.StatusCode == System.Net.HttpStatusCode.InternalServerError)
                        {
                            flag = true;
                            return HealthCheckResult.Healthy();                
                        }

                        flag = false;
                        _logger.LogError("RTMCVPNHealthCheck : REST Health Check Failed");
                        return HealthCheckResult.Unhealthy($"RTMC REST is down.");

                    });

                    if(flag)
                    {
                        _rTMCVPNStatus.VPNOnline = true;
                    }else
                    {
                        _rTMCVPNStatus.VPNOnline = false;
                    }

                }

                return result;

            }catch(Exception ex)
            {
                _rTMCVPNStatus.VPNOnline = false;
                _logger.LogWarning("RTMCVPNHealthCheck : Health Check Failed | Exception : " + ex);
                return HealthCheckResult.Unhealthy($"RTMC VPN is down.");
            }

        }

        #endregion

        #region private
            
        #endregion


    }
}
