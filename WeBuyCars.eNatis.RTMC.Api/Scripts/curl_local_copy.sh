#!/bin/bash


if [ "$OSTYPE" == "linux-gnu" ]; then
	#TODO - These paths need to move to a more standard location (Just here for testing)
	export LD_LIBRARY_PATH=/home/<USER>/openssl/lib:/home/<USER>/curl/lib:$LD_LIBRARY_PATH
	CURL=/home/<USER>/curl/bin/curl
else
	CURL=/usr/bin/curl
fi

#$CURL -v --insecure --cacert ./enatisca2028.crt --key ./webuycars_key.pem --cert ./webuycars_cert.pem -H 'Content-Type: text/xml' --location --request POST 'https://prod.enatis.co.za:4443/enatis/ws' \
$CURL -v --insecure --cacert ./enatisca2028.crt --key ./webuycars_key.pem --cert ./webuycars_cert.pem -H 'Content-Type: text/xml' --location --request POST 'https://iftst.enatis.co.za:4443/enatis/ws' \
--data-raw '<soapenv:Envelope
	xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"
	xmlns:sch="http://tasima/common/ws/schema/">
	<soapenv:Header>
		<wsse:Security
			xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
			<wsse:UsernameToken
				xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd">
				<wsse:Username>4984A001</wsse:Username>
				<wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">TESTER01</wsse:Password>
			</wsse:UsernameToken>
		</wsse:Security>
	</soapenv:Header>
	<soapenv:Body>
		<sch:X3003Request>
			<sch:VinOrChassis>AHT101A0000000001</sch:VinOrChassis>
		</sch:X3003Request>
	</soapenv:Body>
</soapenv:Envelope>'