#!/bin/bash


if [ "$OSTYPE" == "linux-gnu" ]; then
	#TODO - These paths need to move to a more standard location (Just here for testing)
	export LD_LIBRARY_PATH=/home/<USER>/openssl/lib:/home/<USER>/curl/lib:$LD_LIBRARY_PATH
	CURL=/home/<USER>/curl/bin/curl
else
	CURL=/usr/bin/curl
fi

$CURL -v --insecure --cacert ./Scripts/enatisca2028.crt --key ./Scripts/webuycars_key.pem --cert ./Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" "$2"



#!/bin/bash

# export LD_LIBRARY_PATH=/usr/local/openssl/lib:/usr/local/curl/lib:$LD_LIBRARY_PATH

# CURL=/usr/local/curl/bin/curl
# BASE=/app/Scripts

# $CURL -v --insecure --cacert $BASE/enatisca2028.crt --key $BASE/webuycars_key.pem --cert $BASE/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" "$2"

#$CURL -v --insecure --cacert ./Scripts/enatisca2028.crt --key ./Scripts/webuycars_key.pem --cert ./Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" "$2"

#$CURL -v --insecure --cacert $BASE/enatisca2028.crt --key $BASE/webuycars_key.pem --cert $BASE/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws

#$CURL -v --insecure --cacert ./Scripts/enatisca2028.crt --key ./Scripts/webuycars_key.pem --cert ./Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws

#$CURL --insecure --cacert enatisca2028.crt --key webuycars_key.pem --cert webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws

#$CURL -v --insecure --cacert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Api/bin/Debug/net6.0/Scripts/enatisca2028.crt --key /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Api/bin/Debug/net6.0/Scripts/webuycars_key.pem --cert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Api/bin/Debug/net6.0/Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws

#$CURL -v --insecure --cacert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/enatisca2028.crt --key /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_key.pem --cert /Users/<USER>/Development/WeBuyCars-eNatis-RTMC-API/WeBuyCars.eNatis.RTMC.Infrastructure.Natis/Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" https://iftst.enatis.co.za/enatis/ws

#$CURL -v --insecure --cacert ./Scripts/enatisca2028.crt --key ./Scripts/webuycars_key.pem --cert ./Scripts/webuycars_cert.pem -H 'Content-Type: text/xml' -d "$1" "$2"

