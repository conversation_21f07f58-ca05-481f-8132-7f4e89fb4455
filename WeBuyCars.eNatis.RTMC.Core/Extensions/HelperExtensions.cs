using System;
using System.Security.Claims;
using System.Security.Principal;
using System.Text;

namespace WeBuyCars.eNatis.RTMC.Core.Extensions
{
    /// <summary>
    ///     Extensions making it easier to get the user related claims off of an identity
    /// </summary>
    public static class HelperExtensions
    {
        /// <summary>
        ///    Remove Special Characters
        /// </summary>
        /// <param name="inputString"></param>
        /// <returns></returns>
        /// 
        public static string RemoveSpecialCharacters(string inputString) {
            StringBuilder sb = new StringBuilder();
            foreach (char c in inputString) {
                if ((c >= '0' && c <= '9') || (c >= 'A' && c <= 'Z') || (c >= 'a' && c <= 'z')) {
                    sb.Append(c);
                }
            }
            return sb.ToString();
        }



    }
}