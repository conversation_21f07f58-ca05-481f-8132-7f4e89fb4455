using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCSettingsRepository : IRepository
    {
        RTMCSettings AddRTMCSettings(RTMCSettings entity);

        RTMCSettings UpdateRTMCSettings(RTMCSettings entity);

        Task<List<RTMCSettings>> Where(Expression<Func<RTMCSettings, bool>> predicate);

        Task<RTMCSettings> FirstOrDefaultAsync(Expression<Func<RTMCSettings, bool>> predicate);

        Task<RTMCSettings> LastOrDefaultAsync(Expression<Func<RTMCSettings, bool>> predicate);

        Task<RTMCSettings> LastOrDefaultNoTrackingAsync(Expression<Func<RTMCSettings, bool>> predicate);

    }
}