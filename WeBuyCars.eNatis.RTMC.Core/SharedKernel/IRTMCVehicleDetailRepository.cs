using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleDetailRepository : IRepository
    {
        RTMCVehicleDetail AddRTMCVehicleDetail(RTMCVehicleDetail entity);

        Task<List<RTMCVehicleDetail>> Where(Expression<Func<RTMCVehicleDetail, bool>> predicate);

        Task<RTMCVehicleDetail> FirstOrDefaultAsync(Expression<Func<RTMCVehicleDetail, bool>> predicate);

        Task<RTMCVehicleDetail> LastOrDefaultAsync(Expression<Func<RTMCVehicleDetail, bool>> predicate);

    }
}