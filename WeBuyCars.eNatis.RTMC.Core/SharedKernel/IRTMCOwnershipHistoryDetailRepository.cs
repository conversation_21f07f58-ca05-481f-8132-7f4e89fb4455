using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCOwnershipHistoryDetailRepository : IRepository
    {
        RTMCOwnershipHistoryDetail AddRTMCOwnershipHistoryDetail(RTMCOwnershipHistoryDetail entity);

        bool AddRTMCOwnershipHistoryDetailRange(List<RTMCOwnershipHistoryDetail> entity);

        Task<List<RTMCOwnershipHistoryDetail>> Where(Expression<Func<RTMCOwnershipHistoryDetail, bool>> predicate);

        Task<RTMCOwnershipHistoryDetail> FirstOrDefaultAsync(Expression<Func<RTMCOwnershipHistoryDetail, bool>> predicate);

        Task<RTMCOwnershipHistoryDetail> LastOrDefaultAsync(Expression<Func<RTMCOwnershipHistoryDetail, bool>> predicate);

        Task<RTMCOwnershipHistoryDetail> UpdateRTMCOwnershipHistoryDetail(RTMCOwnershipHistoryDetail entity);

    }
}