using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleOwnerOnlineNCORepository : IRepository
    {
        RTMCVehicleOwnerOnlineNCODetail AddRTMCVehicleOwnerOnlineNCODetail(RTMCVehicleOwnerOnlineNCODetail entity);
    
        Task<List<RTMCVehicleOwnerOnlineNCODetail>> Where(Expression<Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate);

        Task<RTMCVehicleOwnerOnlineNCODetail> FirstOrDefaultAsync(Expression<Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate);

        Task<RTMCVehicleOwnerOnlineNCODetail> LastOrDefaultAsync(Expression<Func<RTMCVehicleOwnerOnlineNCODetail, bool>> predicate);

    }
}