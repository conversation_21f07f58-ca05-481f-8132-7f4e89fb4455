using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCDriverInformationDetailRepository : IRepository
    {
        RTMCDriverInformationDetail AddRTMCDriverInformationDetail(RTMCDriverInformationDetail entity);

        Task<List<RTMCDriverInformationDetail>> Where(Expression<Func<RTMCDriverInformationDetail, bool>> predicate);

        Task<RTMCDriverInformationDetail> FirstOrDefaultAsync(Expression<Func<RTMCDriverInformationDetail, bool>> predicate);

        Task<RTMCDriverInformationDetail> LastOrDefaultAsync(Expression<Func<RTMCDriverInformationDetail, bool>> predicate);

    }
}