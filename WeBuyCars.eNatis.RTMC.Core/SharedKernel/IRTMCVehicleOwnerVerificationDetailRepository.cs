using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleOwnerVerificationDetailRepository : IRepository
    {
        RTMCVehicleOwnerVerificationDetail AddRTMCVehicleOwnerVerificationDetail(RTMCVehicleOwnerVerificationDetail entity);
    
        Task<List<RTMCVehicleOwnerVerificationDetail>> Where(Expression<Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate);

        Task<RTMCVehicleOwnerVerificationDetail> FirstOrDefaultAsync(Expression<Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate);

        Task<RTMCVehicleOwnerVerificationDetail> LastOrDefaultAsync(Expression<Func<RTMCVehicleOwnerVerificationDetail, bool>> predicate);

    }
}