using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCRequestRepository : IRepository
    {
        RTMCRequest AddRTMCRequest(RTMCRequest entity);

        Task<List<RTMCRequest>> Where(Expression<Func<RTMCRequest, bool>> predicate);

        Task<RTMCRequest> FirstOrDefaultAsync(Expression<Func<RTMCRequest, bool>> predicate);

        Task<RTMCRequest> LastOrDefaultAsync(Expression<Func<RTMCRequest, bool>> predicate);

    }
}