using System;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Threading.Tasks;
using WeBuyCars.eNatis.RTMC.Core.Entities;

namespace WeBuyCars.eNatis.RTMC.Core.SharedKernel
{
    public interface IRTMCVehicleOwnerRegistrationDetailRepository : IRepository
    {
        RTMCVehicleOwnerRegistrationDetail AddRTMCVehicleOwnerRegistrationDetail(RTMCVehicleOwnerRegistrationDetail entity);
    
        Task<List<RTMCVehicleOwnerRegistrationDetail>> Where(Expression<Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate);

        Task<RTMCVehicleOwnerRegistrationDetail> FirstOrDefaultAsync(Expression<Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate);

        Task<RTMCVehicleOwnerRegistrationDetail> LastOrDefaultAsync(Expression<Func<RTMCVehicleOwnerRegistrationDetail, bool>> predicate);

    }
}