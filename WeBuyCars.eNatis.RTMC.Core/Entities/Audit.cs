using System;
using Newtonsoft.Json;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public class Audit
    {
        public Guid Id { get; set; }

        public string TableName { get; set; }

        public DateTimeOffset AuditDateTimeUtc { get; set; } = DateTimeOffset.UtcNow;

        public string KeyValues { get; set; }

        public string OldValues { get; set; }

        public string NewValues { get; set; }

        public string User { get; set; }

        public override string ToString() => JsonConvert.SerializeObject(this);

    }
}
