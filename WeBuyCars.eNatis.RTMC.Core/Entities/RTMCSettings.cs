using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCSettings : Entity
    {

        #region Properties

        public string BusinessRegistrationNumber {get;set;}
        public string Username { get; set; }
        //public string Password { get; set; }
        public byte[] EncryptedPassword { get; set; }
        public string OwnerDocumentNumber { get; set; }
        public string ProxyDocumentNumber { get; set; }
        public string RepresentativeDocumentNumber { get; set; }
        public bool? HasLogin { get; set; }
        public bool? CanOnlineReg { get; set; }
        public bool? CanOnlineRelease {get;set;}
                    
        #endregion

    }
}