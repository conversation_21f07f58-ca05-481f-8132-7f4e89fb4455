using WeBuyCars.eNatis.RTMC.Core.SharedKernel;
using System;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCOwnershipHistoryDetail : Entity
    {

        #region Properties
        public Guid RequestId { get; set; }        
        public string VinOrChassis { get; set; }
        public string RegisterNumber { get; set; }
        public string LicenseNumber { get; set; }
        public string EntityName { get; set; }
        public string IdentificationNumber { get; set; }
        public string OwnershipStatus { get; set; }
        public string InsuranceCompany { get; set; }
        public string OwnershipType { get; set; }
        public string OwnershipDate { get; set; }                    
        #endregion

    }
}