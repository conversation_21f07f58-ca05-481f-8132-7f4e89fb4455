using WeBuyCars.eNatis.RTMC.Core.Exceptions;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public class CustomProperty : Entity
    {
        #region Fields


        #endregion

        #region Properties

        public string PropertyName { get; set; }

        public string PropertyValue { get; set; }

        #endregion

        #region Constructors

        public CustomProperty()
        {

        }

        public CustomProperty(string propertyName, string propertyValue) : this()
        {
            PropertyName = propertyName;
            PropertyValue = propertyValue;

            Validate();
        }

        #endregion

        #region Public Methods

        public static CustomProperty Create(string propertyName, string propertyValue)
        {
            return new CustomProperty(propertyName, propertyValue);
        }

        #endregion

        #region Private Methods

        void Validate()
        {
            if (string.IsNullOrWhiteSpace(PropertyName))
            {
                throw new DomainException($"{nameof(PropertyName)} is required.");
            }
        }

        #endregion
    }
}