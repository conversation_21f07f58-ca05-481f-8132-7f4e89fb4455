using System;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public class RTMCAudit
    {
        string _user;

        string _workstation;

        string _locality;

        string _networkaddress;

        DateTimeOffset _Date;

        public RTMCAudit(string user,string workstation, string locality, string networkstation, DateTimeOffset date)
        {
            _user = !string.IsNullOrWhiteSpace(user) ? user : throw new ArgumentNullException(nameof(user));
            _workstation = !string.IsNullOrWhiteSpace(workstation) ? workstation : throw new ArgumentNullException(nameof(workstation));
            _locality = !string.IsNullOrWhiteSpace(locality) ? locality : throw new ArgumentNullException(nameof(locality));
            _networkaddress = !string.IsNullOrWhiteSpace(networkstation) ? networkstation : throw new ArgumentNullException(nameof(networkstation));            
            _Date = date;
        }

        public string User => _user;

        public string WorkStation => _workstation;

        public string Locality => _locality;

        public string NetworkAdress => _networkaddress;

        public DateTimeOffset Date => _Date;
    }
}