using System;
using WeBuyCars.eNatis.RTMC.Core.SharedKernel;

namespace WeBuyCars.eNatis.RTMC.Core.Entities
{
    public sealed class RTMCVehicleOwnerRegistrationDetail : Entity
    {

        #region Properties
        public string RegistrationFeeAmount { get; set; }
        public string ConvenienceFeeAmount { get; set; }
        public string TotalRegistrationFeeAmount { get; set; }
        public string PaymentReferenceNumber { get; set; }

        public string RegisterNumber { get; set; }
        public string VinOrChassis { get; set; }
        public byte[] EncryptedVehicleCertificateNumber { get; set; }
        public string TitleHolderIdDocumentType { get; set; }
        public string TitleHolderIdDocumentNumber { get; set; }

        public string OwnerIdDocumentType { get; set; }
        public string OwnerIdDocumentNumber { get; set; }
        public Guid? ClientMessageId { get; set; }     
        public Guid Reference { get; set; }
                    
        #endregion

    }
}