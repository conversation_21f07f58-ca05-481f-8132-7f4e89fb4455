using System;

namespace WeBuyCars.eNatis.RTMC.Core.Exceptions
{
    public class InfrastructureException : Exception
    {
        #region Constructors

        public InfrastructureException()
        { }

        public InfrastructureException(string message)
            : base(message)
        { }

        public InfrastructureException(string message, Exception innerException)
            : base(message, innerException)
        { }

        public InfrastructureException(Exception ex)
            : base(ex.Message)
        { }

        #endregion
    }
}
