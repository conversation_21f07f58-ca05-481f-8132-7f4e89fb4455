using System.ComponentModel.DataAnnotations;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations;

public enum AddressTypeEnum
{
    [Display(Name = "STAND/ERF")]
    StandErf = 1,

    [Display(Name = "HOUSE")]
    House = 2,

    [Display(Name = "COMPLEX/FLAT/BUILDING")]
    ComplexFlatBuilding = 3,

    [Display(Name = "ESTATE")]
    Estate = 4,

    [Display(Name = "OFFICE/RETAIL PARK")]
    OfficeRetailPark = 5
}