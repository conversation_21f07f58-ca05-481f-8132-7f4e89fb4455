using System.ComponentModel;
using System.Runtime.Serialization;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations
{
    public enum IdentificationTypeEnum
    {
        [Description("Reg no certificate")]
        [EnumMember(Value = "01")]
        RegNoCertificate = 01,

        [Description("RSA ID document")]
        [EnumMember(Value = "02")]
        RSAIDDocument = 02,

        [Description("Foreign ID document")]
        [EnumMember(Value = "03")]
        ForeignIDDocument = 03,

        [Description("Business reg certificate")]
        [EnumMember(Value = "04")]
        BusinessRegCertificate = 04
    }
}