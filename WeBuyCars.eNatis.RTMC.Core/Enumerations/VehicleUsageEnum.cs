using System.ComponentModel;
using System.Runtime.Serialization;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations
{
    public enum VehicleUsageEnum
    {
        [Description("Unknown")]
        [EnumMember(Value = "00")]
        Unknown = 00,

        [Description("Passengers")]
        [EnumMember(Value = "01")]
        Passengers = 01,

        [Description("Persons for reward")]
        [EnumMember(Value = "02")]
        PersonsForReward = 02,

        [Description("Medical & allied")]
        [EnumMember(Value = "03")]
        MedicalAllied = 03,

        [Description("Fire fight & rescue")]
        [EnumMember(Value = "04")]
        FireFightRescue = 04,

        [Description("Broadcasting")]
        [EnumMember(Value = "05")]
        Broadcasting = 05,

        [Description("Library")]
        [EnumMember(Value = "06")]
        Library = 06,


        [Description("Cryogenic")]
        [EnumMember(Value = "07")]
        Cryogenic = 07,


        [Description("Refrigerated")]
        [EnumMember(Value = "08")]
        Refrigerated = 08,

        [Description("Containers")]
        [EnumMember(Value = "09")]
        Containers = 09,


        [Description("Livestock")]
        [EnumMember(Value = "10")]
        Livestock = 10,


        [Description("Crop")]
        [EnumMember(Value = "11")]
        Crop = 11,


        [Description("Water/Beverages")]
        [EnumMember(Value = "12")]
        WaterBeverages = 12,

        [Description("Petroleum")]
        [EnumMember(Value = "13")]
        Petroleum = 13,

        [Description("Chemicals")]
        [EnumMember(Value = "14")]
        Chemicals = 14,

        [Description("Hazardous substances")]
        [EnumMember(Value = "15")]
        HazardousSubstances = 15,

        [Description("Bulk commod/raw materials")]
        [EnumMember(Value = "16")]
        BulkCommodRawMaterials = 16,

        [Description("Earth removal")]
        [EnumMember(Value = "17")]
        EarthRemoval = 17,

        [Description("Building & materials")]
        [EnumMember(Value = "18")]
        BuildingMaterials = 18,

        [Description("Road building")]
        [EnumMember(Value = "19")]
        RoadBuilding = 19,

        [Description("Refuse")]
        [EnumMember(Value = "20")]
        Refuse = 20,

        [Description("Sanitary")]
        [EnumMember(Value = "21")]
        Sanitary = 21,

        [Description("Racing")]
        [EnumMember(Value = "22")]
        Racing = 22,

        [Description("General purpose")]
        [EnumMember(Value = "23")]
        GeneralPurpose = 23,

        [Description("Other")]
        [EnumMember(Value = "99")]
        Other = 99,





    }
}