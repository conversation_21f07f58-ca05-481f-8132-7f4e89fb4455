using System.ComponentModel;
using System.Runtime.Serialization;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations
{
    public enum NatureOfOwnershipEnum
    {
        [Description("Unknown")]
        [EnumMember(Value = "0")]
        Unknown = 0,

        [Description("Private")]
        [EnumMember(Value = "1")]
        Private = 1,

        [Description("Business")]
        [EnumMember(Value = "2")]
        Business = 2,

        [Description("MD Stock")]
        [EnumMember(Value = "3")]
        MDStock = 3,

        [Description("MIB Stock/Under construction")]
        [EnumMember(Value = "4")]
        MIBStockUnderConstruction = 4,

    }
}