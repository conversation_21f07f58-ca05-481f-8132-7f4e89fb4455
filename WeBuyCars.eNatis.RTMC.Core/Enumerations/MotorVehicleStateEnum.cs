using System.ComponentModel;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations
{
    public enum MotorVehicleStateEnum
    {
        [Description("Introduced by on-line MIB - released")]
        IntroducedOnlineMIBReleased = 02,

        [Description("Introduced by non-MIB")]
        IntroducedBNonMIB = 03,

        [Description("Business) /Introduced by police (Private")]
        BusinessIntroducedPolicePrivate = 04,

        [Description("Registered (Exempt from licensing)")]
        RegisteredExemptFromLicensing = 05,

        [Description("Registered (Liable for licensing)")]
        RegisteredLiableForLicensing = 06,

        [Description("Deregistered by request")]
        DeregisteredByRequest = 08,

        [Description("Licensed")]
        Licensed = 12,

        [Description("Pending (Sale of used MV)")]
        PendingSaleUsedMV = 16,

    }
}