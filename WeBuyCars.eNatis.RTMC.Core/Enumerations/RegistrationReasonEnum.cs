using System.ComponentModel;
using System.Runtime.Serialization;

namespace WeBuyCars.eNatis.RTMC.Core.Enumerations
{
    public enum RegistrationReasonEnum
    {
        [Description("First Registration")]
        [EnumMember(Value = "01")]
        FirstRegistration = 01,

        [Description("Ownership")]
        [EnumMember(Value = "02")]
        Ownership = 02,

        [Description("Re-register")]
        [EnumMember(Value = "03")]
        ReRegister = 03,

        [Description("Repossessed")]
        [EnumMember(Value = "04")]
        Repossessed = 04,

        [Description("Amalgamation")]
        [EnumMember(Value = "05")]
        Amalgamation = 05,

        [Description("Built-up")]
        [EnumMember(Value = "06")]
        BuiltUp = 06,

        [Description("Removed/Ownership stolen MV")]
        [EnumMember(Value = "07")]
        RemovedOwnershipStolenMV = 07,

        [Description("Estate")]
        [EnumMember(Value = "08")]
        Estate = 08,

        [Description("Deregistered as exempt")]
        [EnumMember(Value = "09")]
        DeregisteredExempt = 09,

    }
}